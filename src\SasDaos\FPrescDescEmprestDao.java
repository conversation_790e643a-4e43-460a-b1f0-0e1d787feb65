package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ATMHist;
import SasBeans.FPRescDescEmprest;
import SasBeans.FuncionVerbasConsig;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FPrescDescEmprestDao {
    
    String sql;
    
    public FPRescDescEmprest getFPrescDescEmprestDao(String sCodFil, 
            String sCodMovFP, String sMatr, Persistencia persistencia) throws Exception {

        FPRescDescEmprest oFPrescDescEmprest1 = new FPRescDescEmprest();
        sql = "Select " +
                "    FPrescDescEmprest.CodFil," +
                "    FPrescDescEmprest.CodMovFP, " +
                "    FPrescDescEmprest.Matr, " +
                "    FPrescDescEmprest.Verba, " +
                "    FPrescDescEmprest.Banco, " +
                "    FPrescDescEmprest.Contrato, " +
                "    FPrescDescEmprest.Obs, " +
                "    FPrescDescEmprest.Dt_alter, " +
                "    FPrescDescEmprest.Hr_alter, " +
                "    FPrescDescEmprest.Operador " +
                "From " +
                "    FPrescDescEmprest " +
                "Where " +
                "    FPrescDescEmprest.CodFil = ? And " +
                "    FPrescDescEmprest.CodMovFP = ? And " +
                "    FPrescDescEmprest.Matr = ?";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodFil);
            stm.setString(sCodMovFP);
            stm.setString(sMatr);
            stm.select();

            if (stm.Proximo()) {
                oFPrescDescEmprest1.setCodFil(stm.getBigDecimal("CodFil"));
                oFPrescDescEmprest1.setCodMovFP(stm.getBigDecimal("CodMovFP"));
                oFPrescDescEmprest1.setMatr(stm.getString("Matr"));
                oFPrescDescEmprest1.setBanco(stm.getString("Banco"));
                oFPrescDescEmprest1.setVerba(stm.getString("Verba"));
                oFPrescDescEmprest1.setBanco(stm.getString("Banco"));
                oFPrescDescEmprest1.setContrato(stm.getString("Contrato"));
                oFPrescDescEmprest1.setObs(stm.getString("Obs"));
                oFPrescDescEmprest1.setOperador(stm.getString("Operador"));
            }
            stm.Close();
            return oFPrescDescEmprest1;
        } catch (Exception e) {
            throw new Exception("FPMensalDao.getFPMensal  - " + e.getMessage() + "\r\n"+ 
                "Select" +
                "    FPrescDescEmprest.CodFil," +
                "    FPrescDescEmprest.CodMovFP, " +
                "    FPrescDescEmprest.Matr, " +
                "    FPrescDescEmprest.Verba, " +
                "    FPrescDescEmprest.Banco, " +
                "    FPrescDescEmprest.Contrato, " +
                "    FPrescDescEmprest.Obs, " +
                "    FPrescDescEmprest.Dt_alter, " +
                "    FPrescDescEmprest.Hr_alter, " +
                "    FPrescDescEmprest.Operador " +
                "From " +
                "    FPrescDescEmprest " +
                "Where " +
                "    FPrescDescEmprest.CodFil = " + sCodFil + " And " +
                "    FPrescDescEmprest.CodMovFP = " + sCodMovFP + " And " +
                "    FPrescDescEmprest.Matr = " + sMatr);
        }

    }

    
    public void preencherComFuncionVerbaConsig(String sCodFil, 
            String sCodMovFP, String sMatr, Persistencia persistencia) throws Exception {

            String ano = sCodMovFP.substring(2, 4);
            String mes = sCodMovFP.substring(5, 7);
            String competencia = ano + mes;
            FPRescDescEmprest umFPRescDescEmprest = getFPrescDescEmprestDao(sCodFil, competencia, 
                    sMatr, persistencia);
            
            // Se não encontrou nenhum registro
            if (umFPRescDescEmprest.getMatr() == null) {
                FuncionVerbasConsigDao daoFuncionVerbasConsigDao = new FuncionVerbasConsigDao();
                List<FuncionVerbasConsig> listaFuncionVerbasConsig = 
                        daoFuncionVerbasConsigDao.getFPMensal(sCodFil, sMatr, persistencia);
                if (!listaFuncionVerbasConsig.isEmpty()){
                    FuncionVerbasConsig umFuncionVerbasConsig = listaFuncionVerbasConsig.get(0);
                    FPRescDescEmprest novoFPRescDescEmprest = new FPRescDescEmprest();
                    novoFPRescDescEmprest.setCodFil(umFuncionVerbasConsig.getCodFil());
                    novoFPRescDescEmprest.setMatr(sMatr);
                    novoFPRescDescEmprest.setCodMovFP(new BigDecimal(competencia));
                    novoFPRescDescEmprest.setContrato(umFuncionVerbasConsig.getContrato());
                    novoFPRescDescEmprest.setBanco(umFuncionVerbasConsig.getBanco());
                    novoFPRescDescEmprest.setVerba(umFuncionVerbasConsig.getVerba());
                    novoFPRescDescEmprest.setObs(umFuncionVerbasConsig.getObs());
                    novoFPRescDescEmprest.setOperador(umFuncionVerbasConsig.getOperador());
                    novoFPRescDescEmprest.setDt_Alter(DataAtual.getDataAtual("SQL"));
                    novoFPRescDescEmprest.setHr_Alter(DataAtual.getDataAtual("HORA"));
                    gravar(novoFPRescDescEmprest, persistencia);                    
                }
            }
            
    }
    
    
        public boolean gravar(FPRescDescEmprest novoFPRescDescEmprest, Persistencia persistencia) {
        boolean retorno;
        
        sql = "insert into FPrescDescEmprest (CodFil, CodMovFP, Matr, Verba, Banco, Contrato, Obs, Operador, Dt_Alter, Hr_Alter)"
                + "Values(?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(novoFPRescDescEmprest.getCodFil());
            consulta.setBigDecimal(novoFPRescDescEmprest.getCodMovFP());
            consulta.setString(novoFPRescDescEmprest.getMatr());
            consulta.setString(novoFPRescDescEmprest.getVerba());
            consulta.setString(novoFPRescDescEmprest.getBanco());
            consulta.setString(novoFPRescDescEmprest.getContrato());
            consulta.setString(novoFPRescDescEmprest.getObs());
            consulta.setString(novoFPRescDescEmprest.getOperador());
            consulta.setString(novoFPRescDescEmprest.getDt_Alter());
            consulta.setString(novoFPRescDescEmprest.getHr_Alter());

            consulta.insert();
            consulta.Close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    
}

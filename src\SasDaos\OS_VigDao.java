package SasDaos;

import <PERSON><PERSON>.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.OS_Vig;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class OS_VigDao {

    public void inserirOS_Vig(OS_Vig os_vig, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO OS_Vig (OS, CodFil, DtInicio, DtFim, Descricao, TipoOS, CodSrv, Cliente, NRed, \n"
                    + " CliDst, NRedDst, CliFat, NRedFat, KM, KMTerra, Distancia, ViaCxf, DiasCst, EntregaSab, EntregaDom, EntregaFer, \n"
                    + " Contrato, Aditivo, CCusto, MsgExtrato, OSGrp, Agrupador, Situacao, GTVQtde, GTVEstMin, \n"
                    + " <PERSON><PERSON>, <PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON>_<PERSON><PERSON>, OperIncl, Dt_Incl, Hr_Incl) \n"
                    + "VALUES ((select ISNULL(max(OS),0)+1 from os_vig where codfil = ?),?,?,?,?,?,?,?"
                    + ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os_vig.getCodFil());
            consulta.setString(os_vig.getCodFil());
            consulta.setString(os_vig.getDtInicio());
            consulta.setString(os_vig.getDtFim());
            consulta.setString(os_vig.getDescricao());
            consulta.setString(os_vig.getTipoOS());
            consulta.setString(os_vig.getCodSrv());
            consulta.setString(os_vig.getCliente());
            consulta.setString(os_vig.getNRed());
            consulta.setString(os_vig.getCliDst());
            consulta.setString(os_vig.getNRedDst());
            consulta.setString(os_vig.getCliFat());
            consulta.setString(os_vig.getNRedFat());
            consulta.setString(os_vig.getKM());
            consulta.setString(os_vig.getKMTerra());
            consulta.setString(os_vig.getDistancia());
            consulta.setString(os_vig.getViaCxF());
            consulta.setInt(os_vig.getDiasCst());
            consulta.setString(os_vig.getEntregaSab());
            consulta.setString(os_vig.getEntregaDom());
            consulta.setString(os_vig.getEntregaFer());
            consulta.setString(os_vig.getContrato());
            consulta.setString(os_vig.getAditivo());
            consulta.setString(os_vig.getCCusto());
            consulta.setString(os_vig.getMsgExtrato());
            consulta.setString(os_vig.getOSGrp());
            consulta.setString(os_vig.getAgrupador());
            consulta.setString(os_vig.getSituacao());
            consulta.setString(os_vig.getGTVQtde());
            consulta.setString(os_vig.getGTVEstMin());
            consulta.setString(os_vig.getOperador());
            consulta.setString(os_vig.getDt_Alter());
            consulta.setString(os_vig.getHr_Alter());
            consulta.setString(os_vig.getOperIncl());
            consulta.setString(os_vig.getDt_Incl());
            consulta.setString(os_vig.getHr_Incl());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VigDao.inserirOS_Vig - " + e.getMessage() + "\r\n"
                    + " INSERT INTO OS_Vig (OS, CodFil, DtInicio, DtFim, Descricao, TipoOS, CodSrv, Cliente, NRed, \n"
                    + " CliDst, NRedDst, CliFat, NRedFat, KM, KMTerra, Distancia, ViaCxf, DiasCst, EntregaSab, EntregaDom, EntregaFer, \n"
                    + " Contrato, Aditivo, CCusto, MsgExtrato, OSGrp, Agrupador, Situacao,  GTVQtde, GTVEstMin, \n"
                    + " Operador, Dt_Alter, Hr_Alter, OperIncl, Dt_Incl, Hr_Incl) \n"
                    + "VALUES ((select max(ISNULL(OS,0))+1 from os_vig where codfil = " + os_vig.getCodFil() + "),"
                    + os_vig.getCodFil() + ", " + os_vig.getDtInicio() + ", " + os_vig.getDtFim() + ", " + os_vig.getDescricao() + ", "
                    + os_vig.getTipoOS() + ", " + os_vig.getCodSrv() + ", " + os_vig.getCliente() + ", " + os_vig.getNRed() + ", "
                    + os_vig.getCliDst() + ", " + os_vig.getNRedDst() + ", " + os_vig.getCliFat() + ", " + os_vig.getNRedFat() + ", "
                    + os_vig.getKM() + ", " + os_vig.getKMTerra() + ", " + os_vig.getDistancia() + ", " + os_vig.getViaCxF() + ", "
                    + os_vig.getDiasCst() + ", " + os_vig.getEntregaSab() + ", " + os_vig.getEntregaDom() + ", " + os_vig.getEntregaFer() + ", "
                    + os_vig.getContrato() + ", " + os_vig.getAditivo() + ", " + os_vig.getCCusto() + ", " + os_vig.getMsgExtrato() + ", "
                    + os_vig.getOSGrp() + ", " + os_vig.getAgrupador() + ", " + os_vig.getSituacao() + ", "
                    + os_vig.getGTVQtde() + ", " + os_vig.getGTVEstMin() + ", "
                    + os_vig.getOperador() + ", " + os_vig.getDt_Alter() + ", " + os_vig.getHr_Alter() + ", "
                    + os_vig.getOperIncl() + ", " + os_vig.getDt_Incl() + ", " + os_vig.getHr_Incl() + ")");

        }
    }

    public void atualizarOS_Vig(OS_Vig os_vig, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE OS_Vig SET DtInicio = ?, DtFim = ?, Descricao = ?, TipoOS = ?, CodSrv = ?, Cliente = ?, NRed = ?, CliDst = ?, \n"
                    + " NRedDst = ?, CliFat = ?, NRedFat = ?, KM = ?, KMTerra = ?, Distancia = ?, ViaCxf = ?, DiasCst = ?, EntregaSab = ?, \n"
                    + " EntregaDom = ?, EntregaFer = ?, Contrato = ?, Aditivo = ?, CCusto = ?, MsgExtrato = ?, OSGrp = ?, Agrupador = ?, Situacao = ?, \n"
                    + " GTVQtde = ?, GTVEstMin = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? \n"
                    + " WHERE OS = ? AND CodFil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os_vig.getDtInicio());
            consulta.setString(os_vig.getDtFim());
            consulta.setString(os_vig.getDescricao());
            consulta.setString(os_vig.getTipoOS());
            consulta.setString(os_vig.getCodSrv());
            consulta.setString(os_vig.getCliente());
            consulta.setString(os_vig.getNRed());
            consulta.setString(os_vig.getCliDst());
            consulta.setString(os_vig.getNRedDst());
            consulta.setString(os_vig.getCliFat());
            consulta.setString(os_vig.getNRedFat());
            consulta.setString(os_vig.getKM());
            consulta.setString(os_vig.getKMTerra());
            consulta.setString(os_vig.getDistancia());
            consulta.setString(os_vig.getViaCxF());
            consulta.setInt(os_vig.getDiasCst());
            consulta.setString(os_vig.getEntregaSab());
            consulta.setString(os_vig.getEntregaDom());
            consulta.setString(os_vig.getEntregaFer());
            consulta.setString(os_vig.getContrato());
            consulta.setString(os_vig.getAditivo());
            consulta.setString(os_vig.getCCusto());
            consulta.setString(os_vig.getMsgExtrato());
            consulta.setString(os_vig.getOSGrp());
            consulta.setString(os_vig.getAgrupador());
            consulta.setString(os_vig.getSituacao());
            consulta.setString(os_vig.getGTVQtde());
            consulta.setString(os_vig.getGTVEstMin());
            consulta.setString(os_vig.getOperador());
            consulta.setString(os_vig.getDt_Alter());
            consulta.setString(os_vig.getHr_Alter());
            consulta.setString(os_vig.getOS());
            consulta.setString(os_vig.getCodFil());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VigDao.atualizarOS_Vig - " + e.getMessage() + "\r\n"
                    + " UPDATE OS_Vig SET DtInicio = " + os_vig.getDtInicio() + ", DtFim = " + os_vig.getDtFim() + ", Descricao = " + os_vig.getDescricao() + ", "
                    + "TipoOS = " + os_vig.getTipoOS() + ", CodSrv = " + os_vig.getCodSrv() + ", Cliente = " + os_vig.getCliente() + ", "
                    + "NRed = " + os_vig.getNRed() + ", CliDst = " + os_vig.getCliDst() + ", NRedDst = " + os_vig.getNRedDst() + ", "
                    + "CliFat = " + os_vig.getCliFat() + ", NRedFat = " + os_vig.getNRedFat() + ", KM = " + os_vig.getKM() + ", "
                    + "KMTerra = " + os_vig.getKMTerra() + ", Distancia = " + os_vig.getDistancia() + ", ViaCxf = " + os_vig.getViaCxF() + ", "
                    + "DiasCst = " + os_vig.getDiasCst() + ", EntregaSab = " + os_vig.getEntregaSab() + ", EntregaDom = " + os_vig.getEntregaDom() + ", "
                    + "EntregaFer = " + os_vig.getEntregaFer() + ", Contrato = " + os_vig.getContrato() + ", Aditivo = " + os_vig.getAditivo() + ", "
                    + "CCusto = " + os_vig.getCCusto() + ", MsgExtrato = " + os_vig.getMsgExtrato() + ", OSGrp = " + os_vig.getOSGrp() + ", "
                    + "Agrupador = " + os_vig.getAgrupador() + ", Situacao = " + os_vig.getSituacao() + ", GTVQtde = " + os_vig.getGTVQtde() + ", "
                    + "GTVEstMin = " + os_vig.getGTVEstMin() + ", Operador = " + os_vig.getOperador() + ", Dt_Alter = " + os_vig.getDt_Alter() + ", "
                    + "Hr_Alter = " + os_vig.getHr_Alter() + " \n WHERE OS = " + os_vig.getOS() + " AND CodFil = " + os_vig.getCodFil());

        }
    }

    public String obterOS(String cliente, String clidst, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT OS FROM OS_VIG WHERE cliente = ? AND clidst = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cliente);
            consulta.setString(clidst);
            consulta.setString(CodFil);
            consulta.select();
            String retorno = "0";
            if (consulta.Proximo()) {
                retorno = consulta.getString("OS");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.obterOS - " + e.getMessage() + "\r\n"
                    + "  SELECT OS FROM OS_VIG WHERE cliente = " + cliente + " AND clidst = " + clidst + " AND CodFil = " + CodFil);
        }
    }

    public OS_Vig obterOSContainer(String cliente, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 OS_Vig.*, Clientes.Ende Endereco, Clientes.Bairro, Clientes.Cidade, Clientes.Estado UF \n"
                    + " FROM OS_Vig \n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + "WHERE cliente = ? and DtFim >= ? ORDER BY OS";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cliente);
            consulta.setString(data);
            consulta.select();
            OS_Vig retorno = null;
            if (consulta.Proximo()) {
                retorno = new OS_Vig();
                retorno.setOS(consulta.getString("OS"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setDtInicio(consulta.getString("DtInicio"));
                retorno.setDtFim(consulta.getString("DtFim"));
                retorno.setDescricao(consulta.getString("Descricao"));
                retorno.setTipoOS(consulta.getString("TipoOS"));
                retorno.setCodSrv(consulta.getString("CodSrv"));
                retorno.setCliente(consulta.getString("Cliente"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setCliDst(consulta.getString("CliDst"));
                retorno.setNRedDst(consulta.getString("NRedDst"));
                retorno.setCliFat(consulta.getString("CliFat"));
                retorno.setNRedFat(consulta.getString("NRedFat"));
                retorno.setKM(consulta.getString("KM"));
                retorno.setKMTerra(consulta.getString("KMTerra"));
                retorno.setDistancia(consulta.getString("Distancia"));
                retorno.setViaCxF(consulta.getString("ViaCxF"));
                retorno.setDiasCst(consulta.getInt("DiasCst"));
                retorno.setEntregaSab(consulta.getString("EntregaSab"));
                retorno.setEntregaDom(consulta.getString("EntregaDom"));
                retorno.setEntregaFer(consulta.getString("EntregaFer"));
                retorno.setGTVQtde(consulta.getString("GTVQtde"));
                retorno.setGTVEstMin(consulta.getString("GTVEstMin"));
                retorno.setContrato(consulta.getString("Contrato"));
                retorno.setAditivo(consulta.getString("Aditivo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setMsgExtrato(consulta.getString("MsgExtrato"));
                retorno.setOSGrp(consulta.getString("OSGrp"));
                retorno.setAgrupador(consulta.getString("Agrupador"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setOperIncl(consulta.getString("OperIncl"));
                retorno.setDt_Incl(consulta.getString("Dt_Incl"));
                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setSitFiscal(consulta.getString("SitFiscal"));
                retorno.setObsFiscal(consulta.getString("ObsFiscal"));
                retorno.setOperFiscal(consulta.getString("OperFiscal"));
                retorno.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                retorno.setHr_Fiscal(consulta.getString("Hr_Fiscal"));
                retorno.setEndereco(consulta.getString("Endereco"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setUF(consulta.getString("UF"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.obterOSContainer - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 OS_Vig.*, Clientes.Ende Endereco, Clientes.Bairro, Clientes.Cidade, Clientes.Estado UF \n"
                    + " FROM OS_Vig \n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + " WHERE cliente = " + cliente + " and DtFim >= " + data + " ORDER BY OS");
        }
    }

    public OS_Vig obterOS(String codfil, String os, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 OS_Vig.* \n"
                    + " FROM OS_Vig \n"
                    + "WHERE OS_Vig.codfil = ? and OS_Vig.os = ? ORDER BY OS";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(os);
            consulta.select();
            OS_Vig retorno = null;
            if (consulta.Proximo()) {
                retorno = new OS_Vig();
                retorno.setOS(consulta.getString("OS").replace(".0", ""));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setDtInicio(consulta.getString("DtInicio"));
                retorno.setDtFim(consulta.getString("DtFim"));
                retorno.setDescricao(consulta.getString("Descricao"));
                retorno.setTipoOS(consulta.getString("TipoOS"));
                retorno.setCodSrv(consulta.getString("CodSrv"));
                retorno.setCliente(consulta.getString("Cliente"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setCliDst(consulta.getString("CliDst"));
                retorno.setNRedDst(consulta.getString("NRedDst"));
                retorno.setCliFat(consulta.getString("CliFat"));
                retorno.setNRedFat(consulta.getString("NRedFat"));
                retorno.setKM(consulta.getString("KM"));
                retorno.setKMTerra(consulta.getString("KMTerra"));
                retorno.setDistancia(consulta.getString("Distancia"));
                retorno.setViaCxF(consulta.getString("ViaCxF"));
                retorno.setDiasCst(consulta.getInt("DiasCst"));
                retorno.setEntregaSab(consulta.getString("EntregaSab"));
                retorno.setEntregaDom(consulta.getString("EntregaDom"));
                retorno.setEntregaFer(consulta.getString("EntregaFer"));
                retorno.setGTVQtde(consulta.getString("GTVQtde"));
                retorno.setGTVEstMin(consulta.getString("GTVEstMin"));
                retorno.setContrato(consulta.getString("Contrato"));
                retorno.setAditivo(consulta.getString("Aditivo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setMsgExtrato(consulta.getString("MsgExtrato"));
                retorno.setOSGrp(consulta.getString("OSGrp"));
                retorno.setAgrupador(consulta.getString("Agrupador"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setOperIncl(consulta.getString("OperIncl"));
                retorno.setDt_Incl(consulta.getString("Dt_Incl"));
                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setSitFiscal(consulta.getString("SitFiscal"));
                retorno.setObsFiscal(consulta.getString("ObsFiscal"));
                retorno.setOperFiscal(consulta.getString("OperFiscal"));
                retorno.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                retorno.setHr_Fiscal(consulta.getString("Hr_Fiscal"));

            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.obterOS - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 OS_Vig.* \n"
                    + " FROM OS_Vig \n"
                    + "WHERE OS_Vig.codfil = " + codfil + " and OS_Vig.os = " + os + " ORDER BY OS");
        }
    }

    public OS_Vig obterOSTrajeto(String cliente, String clidst, String codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT TOP 1 OS_Vig.* \n"
                    + " FROM OS_Vig \n"
                    + "WHERE cliente = ? AND clidst = ? AND CodFil = ?  ORDER BY DtFim DESC";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cliente);
            consulta.setString(clidst);
            consulta.setString(codfil);
            consulta.select();
            OS_Vig retorno = null;
            if (consulta.Proximo()) {
                retorno = new OS_Vig();
                retorno.setOS(consulta.getString("OS").replace(".0", ""));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setDtInicio(consulta.getString("DtInicio"));
                retorno.setDtFim(consulta.getString("DtFim"));
                retorno.setDescricao(consulta.getString("Descricao"));
                retorno.setTipoOS(consulta.getString("TipoOS"));
                retorno.setCodSrv(consulta.getString("CodSrv"));
                retorno.setCliente(consulta.getString("Cliente"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setCliDst(consulta.getString("CliDst"));
                retorno.setNRedDst(consulta.getString("NRedDst"));
                retorno.setCliFat(consulta.getString("CliFat"));
                retorno.setNRedFat(consulta.getString("NRedFat"));
                retorno.setKM(consulta.getString("KM"));
                retorno.setKMTerra(consulta.getString("KMTerra"));
                retorno.setDistancia(consulta.getString("Distancia"));
                retorno.setViaCxF(consulta.getString("ViaCxF"));
                retorno.setDiasCst(consulta.getInt("DiasCst"));
                retorno.setEntregaSab(consulta.getString("EntregaSab"));
                retorno.setEntregaDom(consulta.getString("EntregaDom"));
                retorno.setEntregaFer(consulta.getString("EntregaFer"));
                retorno.setGTVQtde(consulta.getString("GTVQtde"));
                retorno.setGTVEstMin(consulta.getString("GTVEstMin"));
                retorno.setContrato(consulta.getString("Contrato"));
                retorno.setAditivo(consulta.getString("Aditivo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setMsgExtrato(consulta.getString("MsgExtrato"));
                retorno.setOSGrp(consulta.getString("OSGrp"));
                retorno.setAgrupador(consulta.getString("Agrupador"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setOperIncl(consulta.getString("OperIncl"));
                retorno.setDt_Incl(consulta.getString("Dt_Incl"));
                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setSitFiscal(consulta.getString("SitFiscal"));
                retorno.setObsFiscal(consulta.getString("ObsFiscal"));
                retorno.setOperFiscal(consulta.getString("OperFiscal"));
                retorno.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                retorno.setHr_Fiscal(consulta.getString("Hr_Fiscal"));

            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.obterOSContainer - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 OS_Vig.* \n"
                    + " FROM OS_Vig \n"
                    + "WHERE cliente = " + cliente + " AND clidst = " + clidst + " AND CodFil = " + codfil + " ORDER BY DtFim DESC");
        }
    }

    public OS_Vig obterOSPedido(String cliente, String clidst, String codfil, String codCliCaixaForte, Persistencia persistencia) throws Exception {
        String sql = " SELECT TOP 1 OS_Vig.* \n"
                + " FROM OS_Vig \n"
                + " WHERE CodFil = ?\n";
        try {

            if ((cliente.equals(codCliCaixaForte)) || ((cliente.substring(3, 4)).equals("7"))) {
                sql += " and (Cliente  = '" + clidst + "' or  CliDst    = '" + clidst + "')\n"
                        + " and ViaCxF    = 'S'";
            } else if ((clidst.equals(codCliCaixaForte)) || ((clidst.substring(3, 4)).equals("7"))) {
                sql += " and (Cliente  = '" + cliente + "' or  CliDst    = '" + cliente + "')\n"
                        + " and ViaCxF    = 'S'";
            } else {
                sql += " and ((Cliente  = '" + cliente + "' or  CliDst    = '" + clidst + "')\n"
                        + " or (Cliente  = '" + clidst + "' or  CliDst    = '" + cliente + "'))\n";
            }

            sql += " ORDER BY DtFim DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();
            OS_Vig retorno = null;
            if (consulta.Proximo()) {
                retorno = new OS_Vig();
                retorno.setOS(consulta.getString("OS").replace(".0", ""));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setDtInicio(consulta.getString("DtInicio"));
                retorno.setDtFim(consulta.getString("DtFim"));
                retorno.setDescricao(consulta.getString("Descricao"));
                retorno.setTipoOS(consulta.getString("TipoOS"));
                retorno.setCodSrv(consulta.getString("CodSrv"));
                retorno.setCliente(consulta.getString("Cliente"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setCliDst(consulta.getString("CliDst"));
                retorno.setNRedDst(consulta.getString("NRedDst"));
                retorno.setCliFat(consulta.getString("CliFat"));
                retorno.setNRedFat(consulta.getString("NRedFat"));
                retorno.setKM(consulta.getString("KM"));
                retorno.setKMTerra(consulta.getString("KMTerra"));
                retorno.setDistancia(consulta.getString("Distancia"));
                retorno.setViaCxF(consulta.getString("ViaCxF"));
                retorno.setDiasCst(consulta.getInt("DiasCst"));
                retorno.setEntregaSab(consulta.getString("EntregaSab"));
                retorno.setEntregaDom(consulta.getString("EntregaDom"));
                retorno.setEntregaFer(consulta.getString("EntregaFer"));
                retorno.setGTVQtde(consulta.getString("GTVQtde"));
                retorno.setGTVEstMin(consulta.getString("GTVEstMin"));
                retorno.setContrato(consulta.getString("Contrato"));
                retorno.setAditivo(consulta.getString("Aditivo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setMsgExtrato(consulta.getString("MsgExtrato"));
                retorno.setOSGrp(consulta.getString("OSGrp"));
                retorno.setAgrupador(consulta.getString("Agrupador"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setOperIncl(consulta.getString("OperIncl"));
                retorno.setDt_Incl(consulta.getString("Dt_Incl"));
                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setSitFiscal(consulta.getString("SitFiscal"));
                retorno.setObsFiscal(consulta.getString("ObsFiscal"));
                retorno.setOperFiscal(consulta.getString("OperFiscal"));
                retorno.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                retorno.setHr_Fiscal(consulta.getString("Hr_Fiscal"));

            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.obterOSPedido - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    /**
     * busca clifat de uma determinada OS.
     *
     * @param OS
     * @param CodFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getCliFat(String OS, String CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT CliFat FROM OS_VIG WHERE OS = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(CodFil);
            consulta.select();
            String retorno = "0000000";
            if (consulta.Proximo()) {
                retorno = consulta.getString("CliFat");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.getCliFat - " + e.getMessage() + "\r\n"
                    + " SELECT CliFat FROM OS_VIG WHERE OS = " + OS + " AND CodFil = " + CodFil);
        }
    }

    private List<String> ultListaRotulos;
    private List<String> ultTipo;
    private List<Integer> ultTamanho;

    /**
     * Busca as OS de determinado cliente
     *
     * @param cliente - código do cliente, faz por aproximação
     * @param persistencia - conexão ao banco
     * @return - lista com os clintes a faturar com esse código
     * @throws Exception
     */
    public List<OS_Vig> buscaOSs(String cliente, Persistencia persistencia) throws Exception {
        try {
            List<OS_Vig> retorno = new ArrayList();
            OS_Vig os;
            String sql = "select clifat, nredfat "
                    + "from os_vig "
                    + "where clifat like ? "//'844%'
                    + "and situacao = 'A' "
                    + "and dtfim >=getdate()"
                    + "group by clifat, nredfat ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cliente + "%");
            consult.select();
            while (consult.Proximo()) {
                os = new OS_Vig();
                os.setCliFat(consult.getString("clifat"));
                os.setNRedFat(consult.getString("nredfat"));
                retorno.add(os);
            }
            this.ultListaRotulos = consult.getMetadataRotulo();
            this.ultTamanho = consult.getMetadataTamanho();
            this.ultTipo = consult.getMetadataTipo();
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar OSs\r\n" + e.getMessage());
        }
    }

    public List<OS_Vig> listarPedido(String query, boolean tipo, String codigo, Persistencia persistencia) throws Exception {
        List<OS_Vig> retorno = new ArrayList<>();
        try {
            String sql = "Select min(OS_Vig.OS) OS, OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.CliDst, OS_Vig.NRedDst from OS_Vig\n"
                    + "Left Join PessoaCliAut on (PessoaCliAut.CodCli = OS_Vig.Cliente or PessoaCliAut.CodCli = OS_Vig.CliDst)\n"
                    + "                       and PessoaCliAut.CodFil = OS_Vig.CodFil\n"
                    + "where PessoaCliAut.Codigo = ? \n"
                    + "and PessoaCliAut.Flag_Excl <> '*' \n";
            if (tipo) {
                sql = sql + " and OS_Vig.Nred like ? \n";
            } else {
                sql = sql + " and OS_Vig.NredDst like ? \n";
            }
            sql = sql + "and OS_Vig.Situacao = 'A'"
                    + "Group by OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.dtinicio, OS_Vig.CliDst, OS_Vig.NRedDSt";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString("%" + query + "%");
            consulta.select();

            OS_Vig os_vig;

            while (consulta.Proximo()) {
                os_vig = new OS_Vig();
                Integer os = consulta.getInt("OS");
                os_vig.setCliente(consulta.getString("Cliente"));
                os_vig.setOS(os.toString());
                os_vig.setNRed(consulta.getString("Nred"));
                os_vig.setCliDst(consulta.getString("CliDst"));
                os_vig.setNRedDst(consulta.getString("NRedDSt"));

                retorno.add(os_vig);
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("falha ao buscar pessoas: " + e.getMessage());
        }
    }

    public void clonarOS(OS_Vig os_vig, Persistencia persistencia) throws Exception {
        StringBuilder str = new StringBuilder();

        try {
            str.append(" DECLARE @Nome      AS VARCHAR(255)\n");
            str.append(" DECLARE @Data      AS VARCHAR(10)\n");
            str.append(" DECLARE @Hora      AS VARCHAR(10)\n");
            str.append(" DECLARE @OS        AS INT\n");
            str.append(" DECLARE @CodFil    AS INT\n");
            str.append(" DECLARE @OSClonada AS INT\n");

            str.append(" SET @Nome   = ?;\n");
            str.append(" SET @Data   = ?;\n");
            str.append(" SET @Hora   = ?;\n");
            str.append(" SET @OS     = ?;\n");
            str.append(" SET @CodFil = ?;\n");

            /* CRIAR CLONE DE OS */
            str.append(" INSERT INTO OS_Vig (OS,\n");
            str.append("                     CodFil,\n");
            str.append("                     DtInicio,\n");
            str.append("                     DtFim,\n");
            str.append("                     Descricao,\n");
            str.append("                     TipoOS,\n");
            str.append("                     CodSrv,\n");
            str.append("                     Cliente,\n");
            str.append("                     NRed,\n");
            str.append("                     CliDst,\n");
            str.append("                     NRedDst,\n");
            str.append("                     CliFat,\n");
            str.append("                     NredFat,\n");
            str.append("                     KM,\n");
            str.append("                     KMTerra,\n");
            str.append("                     Distancia,\n");
            str.append("                     ViaCxF,\n");
            str.append("                     DiasCst,\n");
            str.append("                     EntregaSab,\n");
            str.append("                     EntregaDom,\n");
            str.append("                     EntregaFer,\n");
            str.append("                     GTVQtde,\n");
            str.append("                     GTVEstMin,\n");
            str.append("                     Contrato,\n");
            str.append("                     Aditivo,\n");
            str.append("                     CCusto,\n");
            str.append("                     MsgExtrato,\n");
            str.append("                     OSGrp,\n");
            str.append("                     Agrupador,\n");
            str.append("                     Situacao,\n");
            str.append("                     OperIncl,\n");
            str.append("                     Dt_Incl,\n");
            str.append("                     Hr_Incl,\n");
            str.append("                     Operador,\n");
            str.append("                     Dt_Alter,\n");
            str.append("                     Hr_Alter,\n");
            str.append("                     SitFiscal,\n");
            str.append("                     ObsFiscal,\n");
            str.append("                     OperFiscal,\n");
            str.append("                     Dt_Fiscal,\n");
            str.append("                     Hr_Fiscal) \n");
            str.append(" SELECT\n");
            str.append(" (SELECT ISNULL((MAX(OS) + 1),1) FROM OS_Vig WHERE CodFil = @CodFil),\n");
            str.append(" OS_Vig.CodFil,\n");
            str.append(" OS_Vig.DtInicio,\n");
            str.append(" OS_Vig.DtFim,\n");
            str.append(" OS_Vig.Descricao,\n");
            str.append(" OS_Vig.TipoOS,\n");
            str.append(" OS_Vig.CodSrv,\n");
            str.append(" OS_Vig.Cliente,\n");
            str.append(" OS_Vig.NRed,\n");
            str.append(" OS_Vig.CliDst,\n");
            str.append(" OS_Vig.NRedDst,\n");
            str.append(" OS_Vig.CliFat,\n");
            str.append(" OS_Vig.NredFat,\n");
            str.append(" OS_Vig.KM,\n");
            str.append(" OS_Vig.KMTerra,\n");
            str.append(" OS_Vig.Distancia,\n");
            str.append(" OS_Vig.ViaCxF,\n");
            str.append(" OS_Vig.DiasCst,\n");
            str.append(" OS_Vig.EntregaSab,\n");
            str.append(" OS_Vig.EntregaDom,\n");
            str.append(" OS_Vig.EntregaFer,\n");
            str.append(" OS_Vig.GTVQtde,\n");
            str.append(" OS_Vig.GTVEstMin,\n");
            str.append(" OS_Vig.Contrato,\n");
            str.append(" OS_Vig.Aditivo,\n");
            str.append(" OS_Vig.CCusto,\n");
            str.append(" OS_Vig.MsgExtrato,\n");
            str.append(" OS_Vig.OSGrp,\n");
            str.append(" OS_Vig.Agrupador,\n");
            str.append(" OS_Vig.Situacao,\n");
            str.append(" @Nome,\n");
            str.append(" @Data,\n");
            str.append(" @Hora,\n");
            str.append(" @Nome,\n");
            str.append(" @Data,\n");
            str.append(" @Hora,\n");
            str.append(" OS_Vig.SitFiscal,\n");
            str.append(" OS_Vig.ObsFiscal,\n");
            str.append(" OS_Vig.OperFiscal,\n");
            str.append(" OS_Vig.Dt_Fiscal,\n");
            str.append(" OS_Vig.Hr_Fiscal\n");
            str.append(" FROM OS_Vig\n");
            str.append(" WHERE OS_Vig.OS     = @OS\n");
            str.append(" AND   OS_Vig.CodFil = @CodFil;\n");

            /* RECUPERA NUMERO DA OS CLONADA */
            str.append(" SET @OSClonada = (SELECT MAX(OS) FROM OS_Vig WHERE CodFil = @CodFil);\n");

            /* CLONAR DADOS DE: OS_VItens */
            str.append(" INSERT INTO OS_VItens (OS,\n");
            str.append("                        CodFil,\n");
            str.append("                        TipoPosto,\n");
            str.append("                        DtInicio,\n");
            str.append("                        DtFim,\n");
            str.append("                        Qtde,\n");
            str.append("                        MsgExtrato,\n");
            str.append("                        Valor,\n");
            str.append("                        Operador,\n");
            str.append("                        Dt_Alter,\n");
            str.append("                        Hr_Alter,\n");
            str.append("                        CHSeman,\n");
            str.append("                        CHMensal,\n");
            str.append("                        Salario)\n");
            str.append(" SELECT\n");
            str.append(" @OSClonada,\n");
            str.append(" OS_VItens.CodFil,\n");
            str.append(" OS_VItens.TipoPosto,\n");
            str.append(" OS_VItens.DtInicio,\n");
            str.append(" OS_VItens.DtFim,\n");
            str.append(" OS_VItens.Qtde,\n");
            str.append(" OS_VItens.MsgExtrato,\n");
            str.append(" OS_VItens.Valor,\n");
            str.append(" @Nome,\n");
            str.append(" @Data,\n");
            str.append(" @Hora,\n");
            str.append(" OS_VItens.CHSeman,\n");
            str.append(" OS_VItens.CHMensal,\n");
            str.append(" OS_VItens.Salario\n");
            str.append(" FROM OS_VItens\n");
            str.append(" WHERE OS_VItens.OS  = @OS\n");
            str.append(" AND   OS_VItens.CodFil = @CodFil;\n");

            /* CLONAR DADOS DE: OS_VTes */
            str.append(" INSERT INTO OS_VTes (OS,\n");
            str.append("                      CodFil,\n");
            str.append("                      LotePdr,\n");
            str.append("                      ContaPdr,\n");
            str.append("                      Agencia,\n");
            str.append("                      Conta,\n");
            str.append("                      Digito,\n");
            str.append("                      CNPJ,\n");
            str.append("                      Razao,\n");
            str.append("                      UI,\n");
            str.append("                      CI,\n");
            str.append("                      CNAB,\n");
            str.append("                      OCTGTV,\n");
            str.append("                      Interface,\n");
            str.append("                      Remetente,\n");
            str.append("                      Finalidade,\n");
            str.append("                      CNABGTV,\n");
            str.append("                      CNABCodExt,\n");
            str.append("                      CNABVariavel,\n");
            str.append("                      TipoNum,\n");
            str.append("                      HrCorteCI,\n");
            str.append("                      Operador,\n");
            str.append("                      Dt_Alter,\n");
            str.append("                      Hr_Alter)\n");
            str.append(" SELECT\n");
            str.append(" @OSClonada,\n");
            str.append(" OS_VTes.CodFil,\n");
            str.append(" OS_VTes.LotePdr,\n");
            str.append(" OS_VTes.ContaPdr,\n");
            str.append(" OS_VTes.Agencia,\n");
            str.append(" OS_VTes.Conta,\n");
            str.append(" OS_VTes.Digito,\n");
            str.append(" OS_VTes.CNPJ,\n");
            str.append(" OS_VTes.Razao,\n");
            str.append(" OS_VTes.UI,\n");
            str.append(" OS_VTes.CI,\n");
            str.append(" OS_VTes.CNAB,\n");
            str.append(" OS_VTes.OCTGTV,\n");
            str.append(" OS_VTes.Interface,\n");
            str.append(" OS_VTes.Remetente,\n");
            str.append(" OS_VTes.Finalidade,\n");
            str.append(" OS_VTes.CNABGTV,\n");
            str.append(" OS_VTes.CNABCodExt,\n");
            str.append(" OS_VTes.CNABVariavel,\n");
            str.append(" OS_VTes.TipoNum,\n");
            str.append(" OS_VTes.HrCorteCI,\n");
            str.append(" @Nome,\n");
            str.append(" @Data,\n");
            str.append(" @Hora\n");
            str.append(" FROM OS_VTes\n");
            str.append(" WHERE OS_VTes.OS     = @OS\n");
            str.append(" AND   OS_VTes.CodFil = @CodFil;\n");

            /* CLONAR DADOS DE: OS_VFreq */
            str.append(" INSERT INTO OS_VFreq (OS,\n");
            str.append("                       CodFil,\n");
            str.append("                       DiaSem,\n");
            str.append("                       Hora1,\n");
            str.append("                       Tipo,\n");
            str.append("                       Hora2,\n");
            str.append("                       RotaC,\n");
            str.append("                       Dias,\n");
            str.append("                       DU,\n");
            str.append("                       Operador,\n");
            str.append("                       Dt_Alter,\n");
            str.append("                       Hr_Alter)\n");
            str.append(" SELECT\n");
            str.append(" @OSClonada,\n");
            str.append(" OS_VFreq.CodFil,\n");
            str.append(" OS_VFreq.DiaSem,\n");
            str.append(" OS_VFreq.Hora1,\n");
            str.append(" OS_VFreq.Tipo,\n");
            str.append(" OS_VFreq.Hora2,\n");
            str.append(" OS_VFreq.RotaC,\n");
            str.append(" OS_VFreq.Dias,\n");
            str.append(" OS_VFreq.DU,\n");
            str.append(" @Nome,\n");
            str.append(" @Data,\n");
            str.append(" @Hora\n");
            str.append(" FROM OS_VFreq \n");
            str.append(" WHERE OS_VFreq.OS     = @OS\n");
            str.append(" AND   OS_VFreq.CodFil = @CodFil;");

            Consulta consulta = new Consulta(str.toString(), persistencia);

            consulta.setString(os_vig.getOperador());
            consulta.setString(os_vig.getDt_Alter());
            consulta.setString(os_vig.getHr_Alter());
            consulta.setString(os_vig.getOS());
            consulta.setString(os_vig.getCodFil());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VigDao.clonarOS - " + e.getMessage() + "\r\n" + str.toString());
        }
    }

    public List<OS_Vig> listarPedidoOSConatiner(String query, String codigo, boolean coleta, Persistencia persistencia) throws Exception {
        List<OS_Vig> retorno = new ArrayList<>();
        try {
            String sql = "Select min(OS_Vig.OS) OS, OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.CliDst, OS_Vig.NRedDst, \n"
                    + " Clientes.Ende Endereco, Clientes.Bairro, Clientes.Cidade, Clientes.Estado UF, OS_Vig.GTVQtde \n"
                    + " FROM OS_Vig\n"
                    + " Left Join PessoaCliAut on (PessoaCliAut.CodCli = OS_Vig.Cliente or PessoaCliAut.CodCli = OS_Vig.CliDst)\n"
                    + "                       and PessoaCliAut.CodFil = OS_Vig.CodFil\n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + " where  OS_Vig.Cliente in ( Select b.Cliente from PessoaCliAut a \n"
                    + "                                           Left Join OS_Vig b   on a.CodCli = b.CliFat \n"
                    + "                                                               and a.CodFil = b.CodFil \n"
                    + "                                           where a.CodFil = OS_Vig.CodFil \n"
                    + "                                             and a.Codigo = ? \n"
                    + "                                             and a.Flag_Excl <> '*') \n"
                    + " and OS_Vig.Nred like ? \n"
                    + " and OS_Vig.Dtfim >= Convert(date,getdate()) \n";

            if (coleta) {
                sql += " and OS_Vig.Cliente in (Select  CodCli1 from CtrOperEquip "
                        + "where CtrOperEquip.CodFil = OS_Vig.Codfil) \n";
            }

            sql += " Group by OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.dtinicio, OS_Vig.CliDst, OS_Vig.NRedDSt, \n"
                    + " Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, OS_Vig.GTVQtde ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString("%" + query + "%");
            consulta.select();

            OS_Vig os_vig;

            while (consulta.Proximo()) {
                os_vig = new OS_Vig();
                os_vig.setCliente(consulta.getString("Cliente"));
                os_vig.setOS(consulta.getString("OS"));
                os_vig.setNRed(consulta.getString("Nred"));
                os_vig.setCliDst(consulta.getString("CliDst"));
                os_vig.setNRedDst(consulta.getString("NRedDSt"));

                os_vig.setEndereco(consulta.getString("Endereco"));
                os_vig.setBairro(consulta.getString("Bairro"));
                os_vig.setCidade(consulta.getString("Cidade"));
                os_vig.setUF(consulta.getString("UF"));
                os_vig.setGTVQtde(consulta.getString("GTVQtde").replace(".0", ""));

                retorno.add(os_vig);
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("OS_Vig.Dao.listarPedidoOSConatiner" + e.getMessage() + "\r\n"
                    + "Select min(OS_Vig.OS) OS, OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.CliDst, OS_Vig.NRedDst, \n"
                    + " Clientes.Ende Endereco, Clientes.Bairro, Clientes.Cidade, Clientes.Estado UF \n"
                    + " FROM OS_Vig\n"
                    + " Left Join PessoaCliAut on (PessoaCliAut.CodCli = OS_Vig.Cliente or PessoaCliAut.CodCli = OS_Vig.CliDst)\n"
                    + "                       and PessoaCliAut.CodFil = OS_Vig.CodFil\n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + " where  OS_Vig.Cliente in ( Select b.Cliente from PessoaCliAut a \n"
                    + "                                           Left Join OS_Vig b   on a.CodCli = b.CliFat \n"
                    + "                                                               and a.CodFil = b.CodFil \n"
                    + "                                           where a.CodFil = OS_Vig.CodFil \n"
                    + "                                             and a.Codigo = ? \n"
                    + "                                             and a.Flag_Excl <> '*') \n"
                    + " and OS_Vig.Nred like '%" + query + "%'\n"
                    + " and OS_Vig.Situacao = 'A' \n"
                    + " Group by OS_Vig.Cliente, OS_Vig.Nred, OS_Vig.dtinicio, OS_Vig.CliDst, OS_Vig.NRedDSt, \n"
                    + " Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado");
        }
    }

    public OS_Vig dadosFatCacambas(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select top 01 * \n"
                    + "from OS_Vig \n"
                    + "where CliFat in ( \n"
                    + "Select CodCli from PessoaCliAut  \n"
                    + "where PessoaCliAut.Codigo = ? \n"
                    + "  and PessoaCliAut.CodFil = OS_Vig.CodFil \n"
                    + "  and PessoaCliAut.Flag_Excl <> '*') \n"
                    + "  and OS_Vig.DtFim >= Convert(Date,Getdate())\n"
                    + "Order by OS";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            OS_Vig os_vig = new OS_Vig();
            if (consulta.Proximo()) {
                os_vig.setCodFil(consulta.getString("CodFil"));
                os_vig.setAditivo(consulta.getString("Aditivo"));
                os_vig.setAgrupador(consulta.getString("Agrupador"));
                os_vig.setCCusto(consulta.getString("CCusto"));
                os_vig.setCliDst(consulta.getString("CliDst"));
                os_vig.setCliFat(consulta.getString("CliFat"));
                os_vig.setCliente(consulta.getString("Cliente"));
                os_vig.setCodSrv(consulta.getString("CodSrv"));
                os_vig.setContrato(consulta.getString("Contrato"));
                os_vig.setDescricao(consulta.getString("Descricao"));
                os_vig.setDiasCst(consulta.getInt("DiasCst"));
                os_vig.setDistancia(consulta.getString("Distancia"));
                os_vig.setDtFim(consulta.getString("DtFim"));
                os_vig.setDtInicio(consulta.getString("DtInicio"));
                os_vig.setDt_Alter(consulta.getString("Dt_Alter"));
                os_vig.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                os_vig.setDt_Incl(consulta.getString("Dt_Incl"));
                os_vig.setEntregaDom(consulta.getString("EntregaDom"));
                os_vig.setEntregaFer(consulta.getString("EntregaFer"));
                os_vig.setEntregaSab(consulta.getString("EntregaSab"));
                os_vig.setGTVEstMin(consulta.getString("GTVEstMin"));
                os_vig.setGTVQtde(consulta.getString("GTVQtde"));
                os_vig.setHr_Alter(consulta.getString("Hr_Alter"));
                os_vig.setHr_Incl(consulta.getString("Hr_Incl"));
                os_vig.setHr_Fiscal(consulta.getString("Hr_Fiscal"));
                os_vig.setKM(consulta.getString("KM"));
                os_vig.setKMTerra(consulta.getString("KMTerra"));
                os_vig.setMsgExtrato(consulta.getString("MsgExtrato"));
                os_vig.setNRed(consulta.getString("NRed"));
                os_vig.setNRedDst(consulta.getString("NRedDst"));
                os_vig.setNRedFat(consulta.getString("NRedFat"));
                os_vig.setOS(consulta.getString("OS"));
                os_vig.setOSGrp(consulta.getString("OSGrp"));
                os_vig.setObsFiscal(consulta.getString("ObsFiscal"));
                os_vig.setOperFiscal(consulta.getString("OperFiscal"));
                os_vig.setOperIncl(consulta.getString("OperIncl"));
                os_vig.setOperador(consulta.getString("Operador"));
                os_vig.setSitFiscal(consulta.getString("SitFiscal"));
                os_vig.setSituacao(consulta.getString("Situacao"));
                os_vig.setTipoOS(consulta.getString("TipoOS"));
                os_vig.setViaCxF(consulta.getString("ViaCxF"));
                //os_vig.setAgencia(consulta.getString("Agencia"));
                //os_vig.setDiaFechaFat(consulta.getString("DiaFechaFat"));
                //os_vig.setDiaVencNF(consulta.getString("DiaVencNF"));
            }
            consulta.close();
            return os_vig;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.dadosFatCacambas - " + e.getMessage());
        }
    }

    public List<OS_Vig> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<OS_Vig> retorno = new ArrayList();
        try {
            String sql = "SELECT  * \n"
                    + "FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY OS_Vig.NRed ) AS RowNum, \n"
                    + "     OS_Vig.*, CliOri.Agencia, CliFatX.DiaFechaFat, CliFatX.DiaVencNF, Contratos.Descricao DescrContrato \n"
                    + "     FROM OS_Vig \n"
                    + "     LEFT JOIN Clientes CliOri  on CliOri.CodFil = OS_Vig.CodFil \n"
                    + "                               and CliOri.Codigo = OS_Vig.Cliente \n"
                    + "     LEFT JOIN Clientes CliFatX  on CliFatX.CodFil = OS_Vig.CodFil \n"
                    + "                                and CliFatX.Codigo = OS_Vig.CliFat \n"
                    + "     LEFT JOIN Contratos         on OS_Vig.CodFil = Contratos.CodFil \n"
                    + "                                and LEFT(OS_Vig.Contrato,8) = LEFT(Contratos.Contrato,8) \n"
                    + "     WHERE OS_Vig.OS IS NOT NULL \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            OS_Vig os_vig;
            while (consulta.Proximo()) {
                os_vig = new OS_Vig();
                os_vig.setCodFil(consulta.getString("CodFil").replace(".0", ""));
                os_vig.setAditivo(consulta.getString("Aditivo"));
                os_vig.setAgrupador(consulta.getString("Agrupador").replace(".0", ""));
                os_vig.setCCusto(consulta.getString("CCusto"));
                os_vig.setCliDst(consulta.getString("CliDst"));
                os_vig.setCliFat(consulta.getString("CliFat"));
                os_vig.setCliente(consulta.getString("Cliente"));
                os_vig.setCodSrv(consulta.getString("CodSrv"));
                os_vig.setContrato(consulta.getString("Contrato"));
                os_vig.setDescricao(consulta.getString("Descricao"));
                os_vig.setDiasCst(consulta.getInt("DiasCst"));
                os_vig.setDistancia(consulta.getString("Distancia"));
                os_vig.setDtFim(consulta.getString("DtFim"));
                os_vig.setDtInicio(consulta.getString("DtInicio"));
                os_vig.setDt_Alter(consulta.getString("Dt_Alter"));
                os_vig.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                os_vig.setDt_Incl(consulta.getString("Dt_Incl"));
                os_vig.setEntregaDom(consulta.getString("EntregaDom"));
                os_vig.setEntregaFer(consulta.getString("EntregaFer"));
                os_vig.setEntregaSab(consulta.getString("EntregaSab"));
                os_vig.setGTVEstMin(consulta.getString("GTVEstMin").replace(".0", ""));
                os_vig.setGTVQtde(consulta.getString("GTVQtde").replace(".0", ""));
                os_vig.setHr_Alter(consulta.getString("Hr_Alter"));
                os_vig.setHr_Incl(consulta.getString("Hr_Incl"));
                os_vig.setHr_Fiscal(consulta.getString("Hr_Fiscal"));
                os_vig.setKM(consulta.getString("KM").replace(".0", ""));
                os_vig.setKMTerra(consulta.getString("KMTerra").replace(".0", ""));
                os_vig.setMsgExtrato(consulta.getString("MsgExtrato"));
                os_vig.setNRed(consulta.getString("NRed"));
                os_vig.setNRedDst(consulta.getString("NRedDst"));
                os_vig.setNRedFat(consulta.getString("NRedFat"));
                os_vig.setOS(consulta.getString("OS").replace(".0", ""));
                os_vig.setOSGrp(consulta.getString("OSGrp").replace(".0", ""));
                os_vig.setObsFiscal(consulta.getString("ObsFiscal"));
                os_vig.setOperFiscal(consulta.getString("OperFiscal"));
                os_vig.setOperIncl(consulta.getString("OperIncl"));
                os_vig.setOperador(consulta.getString("Operador"));
                os_vig.setSitFiscal(consulta.getString("SitFiscal"));
                os_vig.setSituacao(consulta.getString("Situacao"));
                os_vig.setTipoOS(consulta.getString("TipoOS"));
                os_vig.setViaCxF(consulta.getString("ViaCxF"));

                os_vig.setAgencia(consulta.getString("Agencia"));
                os_vig.setDiaFechaFat(consulta.getString("DiaFechaFat"));
                os_vig.setDiaVencNF(consulta.getString("DiaVencNF"));
                
                os_vig.setDescrContrato(consulta.getString("DescrContrato"));

                retorno.add(os_vig);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.listaPaginada - " + e.getMessage());
        }
    }

    public Integer totalListaPaginada(Map filtros, Persistencia persistencia) throws Exception {
        Integer retorno = null;
        try {
            String sql = "SELECT  COUNT (*) Total \n"
                    + "     FROM OS_Vig \n"
                    + "     LEFT JOIN Clientes CliOri  on CliOri.CodFil = OS_Vig.CodFil \n"
                    + "                               and CliOri.Codigo = OS_Vig.Cliente \n"
                    + "     LEFT JOIN Clientes CliFatX  on CliFatX.CodFil = OS_Vig.CodFil \n"
                    + "                                and CliFatX.Codigo = OS_Vig.CliFat \n"
                    + "     WHERE OS_Vig.OS IS NOT NULL \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getInt("Total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VigDao.totalListaPaginada - " + e.getMessage());
        }
    }

    public OS_Vig getOS_VigById(String OS, String codFil, String codPessoa, String banco, Persistencia persistencia) throws Exception {
        OS_Vig os_vig = new OS_Vig();
        Consulta consulta = null;
        String sql = "SELECT OS_Vig.*, CliOri.Agencia, CliFatX.DiaFechaFat, CliFatX.DiaVencNF\n"
                + "FROM OS_Vig\n"
                + "LEFT JOIN Clientes CliOri ON CliOri.CodFil = OS_Vig.CodFil\n"
                + "                          AND CliOri.Codigo = OS_Vig.Cliente\n"
                + "LEFT JOIN Clientes CliFatX  ON CliFatX.CodFil = OS_Vig.CodFil\n"
                + "                            AND CliFatX.Codigo = OS_Vig.CliFat\n"
                + "WHERE OS_Vig.OS IS NOT NULL\n"
                + "AND OS_Vig.OS = ? \n"
                + "AND OS_Vig.CodFil = ? \n"
                + "AND OS_Vig.codfil IN (SELECT filiais.codfil\n"
                + "    FROM saspw\n"
                + "    INNER JOIN saspwfil ON saspwfil.nome = saspw.nome\n"
                + "    INNER JOIN filiais ON filiais.codfil = saspwfil.codfilac\n"
                + "    INNER JOIN paramet ON paramet.filial_pdr = filiais.codfil\n"
                + "    WHERE saspw.codpessoa = ? AND paramet.path = ? \n"
                + ");";

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(OS);
            consulta.setBigDecimal(codFil);
            consulta.setString(codPessoa);
            consulta.setString(banco);

            consulta.select();
            if (consulta.Proximo()) {
                os_vig.setCodFil(consulta.getString("CodFil").replace(".0", ""));
                os_vig.setAditivo(consulta.getString("Aditivo"));
                os_vig.setAgrupador(consulta.getString("Agrupador").replace(".0", ""));
                os_vig.setCCusto(consulta.getString("CCusto"));
                os_vig.setCliDst(consulta.getString("CliDst"));
                os_vig.setCliFat(consulta.getString("CliFat"));
                os_vig.setCliente(consulta.getString("Cliente"));
                os_vig.setCodSrv(consulta.getString("CodSrv"));
                os_vig.setContrato(consulta.getString("Contrato"));
                os_vig.setDescricao(consulta.getString("Descricao"));
                os_vig.setDiasCst(consulta.getInt("DiasCst"));
                os_vig.setDistancia(consulta.getString("Distancia"));
                os_vig.setDtFim(consulta.getString("DtFim"));
                os_vig.setDtInicio(consulta.getString("DtInicio"));
                os_vig.setDt_Alter(consulta.getString("Dt_Alter"));
                os_vig.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                os_vig.setDt_Incl(consulta.getString("Dt_Incl"));
                os_vig.setEntregaDom(consulta.getString("EntregaDom"));
                os_vig.setEntregaFer(consulta.getString("EntregaFer"));
                os_vig.setEntregaSab(consulta.getString("EntregaSab"));
                os_vig.setGTVEstMin(consulta.getString("GTVEstMin").replace(".0", ""));
                os_vig.setGTVQtde(consulta.getString("GTVQtde").replace(".0", ""));
                os_vig.setHr_Alter(consulta.getString("Hr_Alter"));
                os_vig.setHr_Incl(consulta.getString("Hr_Incl"));
                os_vig.setHr_Fiscal(consulta.getString("Hr_Fiscal"));
                os_vig.setKM(consulta.getString("KM").replace(".0", ""));
                os_vig.setKMTerra(consulta.getString("KMTerra").replace(".0", ""));
                os_vig.setMsgExtrato(consulta.getString("MsgExtrato"));
                os_vig.setNRed(consulta.getString("NRed"));
                os_vig.setNRedDst(consulta.getString("NRedDst"));
                os_vig.setNRedFat(consulta.getString("NRedFat"));
                os_vig.setOS(consulta.getString("OS").replace(".0", ""));
                os_vig.setOSGrp(consulta.getString("OSGrp").replace(".0", ""));
                os_vig.setObsFiscal(consulta.getString("ObsFiscal"));
                os_vig.setOperFiscal(consulta.getString("OperFiscal"));
                os_vig.setOperIncl(consulta.getString("OperIncl"));
                os_vig.setOperador(consulta.getString("Operador"));
                os_vig.setSitFiscal(consulta.getString("SitFiscal"));
                os_vig.setSituacao(consulta.getString("Situacao"));
                os_vig.setTipoOS(consulta.getString("TipoOS"));
                os_vig.setViaCxF(consulta.getString("ViaCxF"));
                os_vig.setAgencia(consulta.getString("Agencia"));
                os_vig.setDiaFechaFat(consulta.getString("DiaFechaFat"));
                os_vig.setDiaVencNF(consulta.getString("DiaVencNF"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return os_vig;
    }

    public static void inserirOsReduzida(String codfil, String Descricao, String tipoOS,
            String codSrv, String cliente, String nred, String cliDst, String nredDst, String cliFat,
            String nredFat, String viaCxf, String contrato, String osGrp, String agrupador, String situacao,
            String operIncl, String dtIncl, String hrIncl, String operador, String dtAlter, String hrAlter,
            String sitFiscal, String obsFiscal, String lotePdr, String contaPdr, String gtvQtde, Persistencia persistencia) throws Exception {
        try {
            String sql = " Insert into OS_Vig (OS, CodFil, DtInicio, DtFim, Descricao, TipoOS, CodSrv, Cliente, Nred, \n"
                    + " CliDst, NredDst, CliFat, NredFat, ViaCXF, Contrato, OSGrp, Agrupador, Situacao, OperIncl, \n"
                    + " Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter, SitFiscal, ObsFiscal, gtvQtde) \n"
                    + " values ( (Select isnull(max(os),0)+1 from Os_Vig where codfil = ?),   \n"
                    + " ?,"
                    + " (Select convert(date,getdate())),"
                    + " (Select convert(date,getdate()+730)),"
                    + " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? ) ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(codfil);
            consulta.setString(Descricao);
            consulta.setString(tipoOS);
            consulta.setString(codSrv);
            consulta.setString(cliente);
            consulta.setString(nred);
            consulta.setString(cliDst);
            consulta.setString(nredDst);
            consulta.setString(cliFat);
            consulta.setString(nredFat);
            consulta.setString(viaCxf);
            consulta.setString(contrato);
            consulta.setString(osGrp);
            consulta.setString(agrupador);
            consulta.setString(situacao);
            consulta.setString(operIncl);
            consulta.setString(dtIncl);
            consulta.setString(hrIncl);
            consulta.setString(operador);
            consulta.setString(dtAlter);
            consulta.setString(hrAlter);
            consulta.setString(sitFiscal);
            consulta.setString(obsFiscal);
            consulta.setString(gtvQtde);
            consulta.insert();
            consulta.Close();

            String sqlOsvTes = " Insert into OS_VTes (OS, CodFil, LotePdr, ContaPdr) values ("
                    + " (Select isnull(max(OS),0) from OS_Vig where CodFil = ?), "
                    + "?,?,?)";
            Consulta consultaOsvTes = new Consulta(sqlOsvTes, persistencia);
            consultaOsvTes.setBigDecimal(codfil);
            consultaOsvTes.setBigDecimal(codfil);
            consultaOsvTes.setString(lotePdr);
            consultaOsvTes.setString(contaPdr);
            consultaOsvTes.insert();
            consultaOsvTes.Close();

            String sqlOsVItens = "Insert into OS_VItens (OS, CodFil, TipoPosto, DtInicio, DtFim, Qtde, Operador, \n"
                    + "Dt_Alter, Hr_Alter) values ( \n"
                    + " (Select isnull(max(OS),0) from OS_Vig where CodFil = ?), ?, ?, \n"
                    + " (Select convert(date,getdate())), \n"
                    + " (Select convert(date,getdate()+730)), \n"
                    + " ?, ?, ?, ? )";
            Consulta consultaOsVItens = new Consulta(sqlOsVItens, persistencia);
            consultaOsVItens.setString(codfil);
            consultaOsVItens.setString(codfil);
            consultaOsVItens.setString("010");
            consultaOsVItens.setString("1");
            consultaOsVItens.setString(operador);
            consultaOsVItens.setString(dtAlter);
            consultaOsVItens.setString(hrAlter);
            consultaOsVItens.insert();
            consultaOsVItens.Close();

        } catch (Exception e) {
            throw new Exception("OS_VigDao.inserirOsCI - " + e.getMessage());
        }
    }

    public void atualizarGtvQtde(String os, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update OS_Vig set GTVQtde = GTVQtde-1 where OS = ? and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os);
            consulta.setString(codFil);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VigDao.atualizarGtvQtde - " + e.getMessage());
        }

    }

    public void acrescentarGtvQtde(String os, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update OS_Vig set GTVQtde = GTVQtde+1 where OS = ? and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os);
            consulta.setString(codFil);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VigDao.acrescentarGtvQtde - " + e.getMessage());
        }

    }

    public Clientes getClienteDeOS(String codFil, String OS, Persistencia persistencia) throws Exception {
        String sql = "SELECT TOP 1 CliDst, NRedDst\n"
                + "FROM OS_Vig\n"
                + "WHERE OS = ? \n"
                + "  AND CodFil = ? ;";

        Clientes cliente = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(codFil);
            consulta.select();

            if (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodCli(consulta.getString("CliDst"));
                cliente.setNRed(consulta.getString("NRedDst"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return cliente;
    }

    public OS_Vig getOSComGTVById(String codFil, String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "SELECT TOP 1 GTV.OS, OS_Vig.CliDst, OS_Vig.NRedDst\n"
                + "FROM GTV\n"
                + "         LEFT JOIN OS_Vig ON OS_Vig.OS = GTV.OS\n"
                + "    AND OS_Vig.CodFil = GTV.CodFil\n"
                + "WHERE GTV.Guia = ? \n"
                + "  AND GTV.Serie = ? \n"
                + "  AND GTV.CodFil = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);

            consulta.select();
            if (consulta.Proximo()) {
                OS_Vig item = new OS_Vig();
                item.setOS(consulta.getString("OS"));
                item.setCliDst(consulta.getString("CliDst"));
                item.setNRedDst(consulta.getString("NRedDst"));
                return item;
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return null;
    }

    public List<OS_Vig> getListaOSFromCodCli(
            String codFil,
            String codCli,
            String cliDst,
            String situacao,
            Persistencia persistencia
    ) throws Exception {
        String sql = "SELECT OS, NRed, NRedDst, ViaCxf, CliDst\n"
                + "FROM OS_Vig\n"
                + "WHERE CodFil = ? \n"
                + "  AND Cliente = ? ";
        if (cliDst != null) {
            sql += "  AND CliDst = ? ";
        }
        if (situacao != null) {
            sql += "  AND Situacao = ? ";
        }
        sql += ";";

        List<OS_Vig> lista = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codCli);
            if (cliDst != null) {
                consulta.setString(cliDst);
            }
            if (situacao != null) {
                consulta.setString(situacao);
            }

            consulta.select();
            if (consulta.Proximo()) {
                OS_Vig item = new OS_Vig();
                item.setOS(consulta.getString("OS"));
                item.setNRed(consulta.getString("NRed"));
                item.setNRedDst(consulta.getString("NRedDst"));
                item.setViaCxF(consulta.getString("ViaCxf"));
                item.setCliDst(consulta.getString("CliDst"));

                lista.add(item);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

}

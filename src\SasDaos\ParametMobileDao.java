package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ParametMobile;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ParametMobileDao {

    public ParametMobile listaVersao(String Plataforma, String nome, Persistencia persistencia) throws Exception {
        Consulta consult;
        String sql = "Select Versao, VersaoAceita from ParametMobile where plataforma = ? and Aplicativo = ? ";
        try {
            consult = new Consulta(sql, persistencia);
            consult.setString(Plataforma);
            consult.setString(nome);
            consult.select();
            ParametMobile pm = new ParametMobile();
            if (consult.Proximo()) {
                pm.setVersao(consult.getString("Versao"));
                pm.setVersaoAceita(consult.getString("VersaoAceita"));
                pm.setPlataforma(Plataforma);
            }
            consult.Close();
            return pm;
        } catch (Exception e) {
            throw new Exception("ParametMobileDao.listaVersao - " + e.getMessage() + "\r\n"
                    + "Select Versao, VersaoAceita from ParametMobile where plataforma = " + Plataforma + " and Aplicativo = " + nome);
        }
    }

    public List<ParametMobile> listaVersao(String Plataforma, Persistencia persistencia) throws Exception {
        Consulta consult;
        List<ParametMobile> lpm = new ArrayList();
        String sql = "Select Versao, VersaoAceita from ParametMobile where plataforma = ? ";
        try {
            consult = new Consulta(sql, persistencia);
            consult.setString(Plataforma);
            consult.select();
            while (consult.Proximo()) {
                ParametMobile pm = new ParametMobile();
                pm.setVersao(consult.getString("Versao"));
                pm.setVersaoAceita(consult.getString("VersaoAceita"));
                pm.setPlataforma(Plataforma);
                lpm.add(pm);
            }
            consult.Close();
            return lpm;
        } catch (Exception e) {
            throw new Exception("Falha ao Carregar Versão Corrente da Plataforma - " + e.getMessage());
        }
    }

    /**
     * Atualiza a versão atual e aceita do mobile
     *
     * @param plataforma
     * @param versao
     * @param versaoAceita
     * @param operador
     * @param persistencia
     * @throws Exception
     */
    public void atualizarVersao(String plataforma, String versao, String versaoAceita, String operador, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE ParametMobile "
                    + " SET Versao = ?, VersaoAceita = ?, Dt_Alter = ?, Hr_Alter = ?, Operador = ? "
                    + " WHERE Plataforma = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(versao);
            consulta.setString(versaoAceita);
            consulta.setString(LocalDate.now().toString());
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(operador);
            consulta.setString(plataforma);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar Versão em ParametMobile - " + e.getMessage());
        }
    }
}

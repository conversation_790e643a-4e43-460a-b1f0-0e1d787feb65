package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.OS_Vig;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class OSDao {

    /**
     * Busca OS automaticamente
     *
     * @param CliOriPesq - Código de Cliente de Origem
     * @param CliDestPesq - Código de Cliente de Destino
     * @param CliFatPesq - Código de Cliente a Faturar
     * @param Filial - Código da Filial
     * @param DtAtend - Data de Atendimento
     * @param TipoOS - Tipo da OS
     * @param CodCliCxf - Código do Caixa Forte
     * @param persistencia - Conexão ao Banco
     * @return - Código da OS identificado
     * @throws Exception
     */
    /*public BigDecimal BuscaOS(String CliOriPesq, String CliDestPesq, String CliFatPesq, String Filial, 
            String DtAtend, int TipoOS, String CodCliCxf, Persistencia persistencia) throws Exception{
        BigDecimal retorno = new BigDecimal("0");
        String SQLFiltro;
        String sql,comp1="",comp2="",parametroFiltro1, parametroFiltro2;
        boolean inverteParametro;
        //define complemtno conforme tipo de OS
        if ((TipoOS <= 2)||(TipoOS>4)){
            comp1= " and (TipoOS = '1' or TipoOS = '2' or TipoOs = '6') ";
        }
        else if(TipoOS == 3 ){
            comp1= " and TipoOS = '3' ";
        }
        else if(TipoOS == 4){
            comp1= " and TipoOS = '4' ";
        }
        //define complemento pela presença de Cliente Faturametno a pesquisar
        if (!"".equals(CliFatPesq)){
            comp2= " and CliFat = '"+CliFatPesq+"' ";
        }
        //determina filtro caso algum cliente seja o Caixa Forte
        if(CliOriPesq.equals(CodCliCxf)){
            SQLFiltro = " and (Cliente = ? or CliDst = ?) and ViaCxF = 'S' ";
            parametroFiltro1=parametroFiltro2=CliDestPesq;
            inverteParametro=false;
        }
        else if (CliDestPesq.equals(CodCliCxf)){
            SQLFiltro = " and (Cliente = ? or CliDst = ? and ViaCxf = 'S') ";
            parametroFiltro1=parametroFiltro2=CliOriPesq;
            inverteParametro=false;
        }
        else{
            SQLFiltro = " and ((Cliente = ? and CliDst = ?) "
                      + " or (Cliente = ? and CliDst = ?)) ";
            parametroFiltro1=CliOriPesq;
            parametroFiltro2=CliDestPesq;
            inverteParametro=true;
        }
        sql = "select top 1 OS from Os_Vig "
            +" where CodFil = ? and Situacao = 'A' and "
            +" DtInicio <=? and DtFim >= ? "+comp1+comp2;
        try{
            Consulta consult = new Consulta(sql+SQLFiltro,persistencia);
            consult.setString(Filial);
            consult.setString(DtAtend);
            consult.setString(DtAtend);
            consult.setString(parametroFiltro1);
            consult.setString(parametroFiltro2);
            if(inverteParametro){
                consult.setString(parametroFiltro2);
                consult.setString(parametroFiltro2);
            }
            consult.Executa();
            boolean vazio=true;
            while (consult.Proximo()){
                vazio=false;
                retorno = new BigDecimal(consult.getString("OS"));   
            }
            consult.Close();
            if (vazio){ 
                SQLFiltro=" and Cliente = ? ";
                consult = new Consulta(sql+SQLFiltro,persistencia);
                consult.setString(Filial);
                consult.setString(DtAtend);
                consult.setString(DtAtend);
                consult.setString(CliOriPesq);
                consult.Executa();
                while (consult.Proximo()){
                    vazio=false;
                    retorno = new BigDecimal(consult.getString("OS"));
                }
                consult.Close();
                if (vazio){
                    consult = new Consulta(sql+SQLFiltro,persistencia);
                    consult.setString(Filial);
                    consult.setString(DtAtend);
                    consult.setString(DtAtend);
                    consult.setString(CliDestPesq);
                    consult.Executa();
                    while (consult.Proximo()){
                        vazio=false;
                        retorno = new BigDecimal(consult.getString("OS"));
                    }
                }
            }
           if (!vazio){
               return retorno;
           }
           else{
               return new BigDecimal("0");
           }
         }
         catch(Exception e){
             throw new Exception("Falha ao Buscar OS - "+e.getMessage());
         }
    }*/

    /**
     * Busca OS automaticamente
     *
     * @param CliOriPesq - Código de Cliente de Origem
     * @param CliDestPesq - Código de Cliente de Destino
     * @param CliFatPesq - Código de Cliente a Faturar
     * @param Filial - Código da Filial
     * @param DtAtend - Data de Atendimento
     * @param TipoOS - Tipo da OS
     * @param CodCliCxf - Código do Caixa Forte
     * @param persistencia - Conexão ao Banco
     * @return - Código da OS identificado
     * @throws Exception
     */
    public OS_Vig BuscaOS(String CliOriPesq, String CliDestPesq, String CliFatPesq, String Filial,
            String DtAtend, int TipoOS, String CodCliCxf, Persistencia persistencia) throws Exception {
        OS_Vig retorno = new OS_Vig();
        String SQLFiltro;
        String sql, comp1 = "", comp2 = "", parametroFiltro1, parametroFiltro2;
        boolean inverteParametro;
        //define complemtno conforme tipo de OS
        if ((TipoOS <= 2) || (TipoOS > 4)) {
            comp1 = " and (TipoOS = '1' or TipoOS = '2' or TipoOs = '6') ";
        } else if (TipoOS == 3) {
            comp1 = " and TipoOS = '3' ";
        } else if (TipoOS == 4) {
            comp1 = " and TipoOS = '4' ";
        }
        //define complemento pela presença de Cliente Faturametno a pesquisar
        if (!"".equals(CliFatPesq)) {
            comp2 = " and CliFat = '" + CliFatPesq + "' ";
        }
        //determina filtro caso algum cliente seja o Caixa Forte
        if (CliOriPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = ? or CliDst = ?) and ViaCxF = 'S' ";
            parametroFiltro1 = parametroFiltro2 = CliDestPesq;
            inverteParametro = false;
        } else if (CliDestPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = ? or CliDst = ? and ViaCxf = 'S') ";
            parametroFiltro1 = parametroFiltro2 = CliOriPesq;
            inverteParametro = false;
        } else {
            SQLFiltro = " and ((Cliente = ? and CliDst = ?) "
                    + " or (Cliente = ? and CliDst = ?)) ";
            parametroFiltro1 = CliOriPesq;
            parametroFiltro2 = CliDestPesq;
            inverteParametro = true;
        }
        sql = "select top 1 OS, KM, KMTerra from Os_Vig "
                + " where CodFil = ? and Situacao = 'A' and "
                + " DtInicio <=? and DtFim >= ? " + comp1 + comp2;
        try {
            Consulta consult = new Consulta(sql + SQLFiltro, persistencia);
            consult.setString(Filial);
            consult.setString(DtAtend);
            consult.setString(DtAtend);
            consult.setString(parametroFiltro1);
            consult.setString(parametroFiltro2);
            if (inverteParametro) {
                consult.setString(parametroFiltro2);
                consult.setString(parametroFiltro1);
            }
            consult.select();
            boolean vazio = true;
            while (consult.Proximo()) {
                vazio = false;
                retorno.setOS(consult.getString("OS"));
                retorno.setKM(consult.getString("KM"));
                retorno.setKMTerra(consult.getString("KMTerra"));
            }
            consult.Close();
            if (vazio) {
                SQLFiltro = " and Cliente = ? ";
                consult = new Consulta(sql + SQLFiltro, persistencia);
                consult.setString(Filial);
                consult.setString(DtAtend);
                consult.setString(DtAtend);
                consult.setString(CliOriPesq);
                consult.select();
                while (consult.Proximo()) {
                    vazio = false;
                    retorno.setOS(consult.getString("OS"));
                    retorno.setKM(consult.getString("KM"));
                    retorno.setKMTerra(consult.getString("KMTerra"));
                }
                consult.Close();
                if (vazio) {
                    consult = new Consulta(sql + SQLFiltro, persistencia);
                    consult.setString(Filial);
                    consult.setString(DtAtend);
                    consult.setString(DtAtend);
                    consult.setString(CliDestPesq);
                    consult.select();
                    while (consult.Proximo()) {
                        vazio = false;
                        retorno.setOS(consult.getString("OS"));
                        retorno.setKM(consult.getString("KM"));
                        retorno.setKMTerra(consult.getString("KMTerra"));
                    }
                }
                consult.Close();
            }
            if (!vazio) {
                return retorno;
            } else {
                retorno.setOS("0");
                return retorno;
            }
        } catch (Exception e) {
            throw new Exception("Falha ao Buscar OS - " + e.getMessage());
        }
    }

    /**
     * Compara clientes OS
     *
     * @param persistencia - Conexão ao banco
     * @param OS - Ordem de serviço
     * @param CodFil - código da filial
     * @return - Retorna boolean
     * @throws java.lang.Exception - pode gerar exception
     */
    public OS_Vig ClientesOS(Persistencia persistencia, BigDecimal OS, String CodFil) throws Exception {

        OS_Vig os = new OS_Vig();
        String sql = "select top 1 Cliente, KM, KMTerra from OS_Vig where OS=? and CodFil=? AND Situacao <> 'I' ";
        try {
            Consulta validaSG = new Consulta(sql, persistencia);
            validaSG.setBigDecimal(OS);
            validaSG.setString(CodFil);
            validaSG.select();
            while (validaSG.Proximo()) {
                os.setCliente(validaSG.getString("Cliente"));
                os.setKM(validaSG.getString("KM"));
                os.setKMTerra(validaSG.getString("KMTerra"));
                os.setOS(OS.toString());
            }
            validaSG.Close();
            return os;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar OS - " + e.getMessage());
        }
    }
}

/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxForte;
import SasBeans.Pedido;
import SasBeans.PedidoDN;
import SasBeans.TesMoedas;
import SasBeansCompostas.GTVPedidoOSClienteTesSaida;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class PedidoDao {

    public List<Pedido> listagemPedidosRefeicao(String CodCli, String CodFil, String CodPedido, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "SELECT"
                    + " PstServ.Local,"
                    + " PstServ.Secao,"
                    + " 0 'QtdeCafe',"
                    + " 0 'QtdeAlmoco',"
                    + " 0 'QtdeJanta',"
                    + " 0 'QtdeCeia'"
                    + " FROM PstServ"
                    + " WHERE PstServ.CodFil = ?"
                    + " AND   PstServ.CodCli = ?"
                    + " ORDER BY PstServ.Local";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(CodFil);
            consulta.setString(CodCli);
            consulta.select();

            List<Pedido> listaPedidoRefeicao = new ArrayList<>();
            Pedido pedido;

            while (consulta.Proximo()) {
                pedido = new Pedido();

                pedido.setSecao(consulta.getString("Secao"));
                pedido.setCli2Nred(consulta.getString("Local"));
                pedido.setQtdeCafe(consulta.getString("QtdeCafe"));
                pedido.setQtdeAlmoco(consulta.getString("QtdeAlmoco"));
                pedido.setQtdeJanta(consulta.getString("QtdeJanta"));
                pedido.setQtdeCeia(consulta.getString("QtdeCeia"));

                listaPedidoRefeicao.add(pedido);
            }

            consulta.close();
            return listaPedidoRefeicao;

        } catch (Exception e) {
            throw new Exception("PedidoDao.listagemPedidosRefeicao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public Pedido obterSituacaoPedido(String pedidoCliente, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT CodFil, PedidoCliente, \n"
                    + "Max(Case when ER = 'R' then Dt_Incl end) Data_Rec,\n"
                    + "Max(Case when ER = 'R' then Hr_Incl end) Hora_Rec,\n"
                    + "Max(Case when ER = 'R' then Status end) Status_Rec,\n"
                    + "Max(Case when ER = 'R' then Valor end) Valor_Rec,\n"
                    + "Max(Guias_Rec) Guias_Rec,\n"
                    + "Max(Case when ER = 'E' then Dt_Incl end) Data_Ent,\n"
                    + "Max(Case when ER = 'E' then Hr_Incl end) Hora_Ent,\n"
                    + "Max(Case when ER = 'E' then Status end) Status_Ent,\n"
                    + "Max(Case when ER = 'E' then Valor end) Valor_Ent,\n"
                    + "Max(Guias_Ent) Guias_Ent, "
                    + "Max(Numero) Numero \n"
                    + "\n"
                    + "FROM (\n"
                    + "SELECT \n"
                    + "Pedido.CodFil, Pedido.PedidoCliente, Rt_Perc.ER, Pedido.Numero, convert(varchar, Rotas.Data, 112) Dt_Incl,\n"
                    + "Rt_Perc.HrCheg Hr_Incl, Rt_Perc.HrSaida,\n"
                    + "Case --when Rt_Perc.Sequencia is null and Pedido.Flag_Excl <> '*' then '0' --Recebido\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) <= 1 and len(Rt_Perc.HrSaida) <= 1 and Pedido.Flag_Excl <> '*' then '1' --Confirmado\n"
                    + "when Pedido.Flag_Excl = '*' then '2' --Recusado\n"
                    + "when len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) > 1 then '3' --Entregue\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) <= 1 and Pedido.Flag_Excl <> '*' then '6' --Em Processamento\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) > 1 and Pedido.Flag_Excl <> '*' then '7' --Processado\n"
                    + "        else '0' --Recebido\n"
                    + "end Status,\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "(STUFF((SELECT ', '+Convert(Varchar,Rt_Guias.Guia)+'-'+Convert(Varchar,Rt_Guias.Serie)\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "									   and rt.ER = 'R'\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada\n"
                    + "		Order by Rt_Guias.Guia\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		))  Guias_Rec,\n"
                    + "\n"
                    + "(STUFF((SELECT ', '+Convert(Varchar,Rt_Guias.Guia)+'-'+Convert(Varchar,Rt_Guias.Serie)\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "									   and rt.ER = 'E'\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada\n"
                    + "		--Order by Rt_Guias.Guia\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		))  Guias_Ent,\n"
                    + "\n"
                    + "(SELECT Sum(isnull(Rt_Guias.Valor,0))\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada) Valor\n"
                    + "\n"
                    + "FROM Pedido (nolock)\n"
                    + "Left join Rt_Perc (nolock) on Pedido.SeqRota = Rt_Perc.Sequencia\n"
                    + "                  and Pedido.Parada  = Rt_Perc.Parada\n"
                    + "                  and Pedido.Numero = Rt_Perc.Pedido\n"
                    + " and (Rt_Perc.Flag_Excl <> '*' or Rt_Perc.Flag_Excl is null)\n"
                    + "Left join Rotas (nolock) on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "                  and Rotas.flag_excl <> '*'\n"
                    + "WHERE Pedido.PedidoCliente = ? ) a\n"
                    + "Group by CodFil, PedidoCliente";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoCliente);
            consulta.select();
            Pedido retorno = null;
            if (consulta.Proximo()) {
                retorno = new Pedido();

                retorno.setNumero(consulta.getString("Numero"));
//                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
//                retorno.setData(consulta.getString("Data"));
//                retorno.setTipo(consulta.getString("Tipo"));
//                retorno.setCodCli1(consulta.getString("CodCli1"));
//                retorno.setNRed1(consulta.getString("NRed1"));
//                retorno.setRegiao1(consulta.getString("Regiao1"));
//                retorno.setHora1O(consulta.getString("Hora1O"));
//                retorno.setHora2O(consulta.getString("Hora2O"));
//                retorno.setCodCli2(consulta.getString("CodCli2"));
//                retorno.setNRed2(consulta.getString("Nred2"));
//                retorno.setRegiao2(consulta.getString("Regiao2"));
//                retorno.setHora1D(consulta.getString("Hora1D"));
//                retorno.setHora2D(consulta.getString("Hora2D"));
//                retorno.setCodCli3(consulta.getString("CodCli3"));
//                retorno.setSeqSuprim(consulta.getString("SeqSuprim"));
//                retorno.setSolicitante(consulta.getString("Solicitante"));
//                retorno.setPedidoCliente(consulta.getString("PedidoCliente"));
//                retorno.setValor(consulta.getBigDecimal("Valor"));
//                retorno.setObs(consulta.getString("Obs"));
//                retorno.setClassifSrv(consulta.getString("ClassifSrv"));
//                retorno.setOperIncl(consulta.getString("OperIncl"));
//                retorno.setRotaCanc(consulta.getString("RotaCanc"));
//                retorno.setSeqCanc(consulta.getString("SeqCanc"));
//                retorno.setDt_Incl(consulta.getString("Dt_Incl"));
//                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
//                retorno.setChequesQtde(consulta.getInt("ChequesQtde"));
//                retorno.setChequesValor(consulta.getString("ChequesValor"));
//                retorno.setTicketsQtde(consulta.getString("TicketsQtde"));
//                retorno.setTicketsValor(consulta.getString("TicketsValor"));
//                retorno.setOperador(consulta.getString("Operador"));
//                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
//                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
//                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
//                retorno.setOperExcl(consulta.getString("OperExcl"));
//                retorno.setDt_Excl(consulta.getString("Dt_Excl"));
//                retorno.setHr_Excl(consulta.getString("Hr_Excl"));
//                retorno.setSituacao(consulta.getString("Status"));
//                retorno.setSeqRota(consulta.getString("SeqRota"));
//                retorno.setParada(consulta.getInt("Parada"));
//                retorno.setHrOper(consulta.getString("HrOper"));
//                retorno.setFlag_Excl(consulta.getString("Flag_Excl"));
                retorno.setDataRec(consulta.getString("Data_Rec"));
                retorno.setHoraRec(consulta.getString("Hora_Rec"));
                retorno.setStatusRec(consulta.getString("Status_Rec"));
                retorno.setValorRec(consulta.getString("Valor_Rec"));
                retorno.setGuiasRec(consulta.getString("Guias_Rec"));

                retorno.setDataEnt(consulta.getString("Data_Ent"));
                retorno.setHoraEnt(consulta.getString("Hora_Ent"));
                retorno.setStatusEnt(consulta.getString("Status_Ent"));
                retorno.setValorEnt(consulta.getString("Valor_Ent"));
                retorno.setGuiasEnt(consulta.getString("Guias_Ent"));

            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.obterSituacaoPedido - " + e.getMessage() + "\r\n"
                    + "SELECT CodFil, PedidoCliente, \n"
                    + "Max(Case when ER = 'R' then Dt_Incl end) Data_Rec,\n"
                    + "Max(Case when ER = 'R' then Hr_Incl end) Hora_Rec,\n"
                    + "Max(Case when ER = 'R' then Status end) Status_Rec,\n"
                    + "Max(Case when ER = 'R' then Valor end) Valor_Rec,\n"
                    + "Max(Guias_Rec) Guias_Rec,\n"
                    + "Max(Case when ER = 'E' then Dt_Incl end) Data_Ent,\n"
                    + "Max(Case when ER = 'E' then Hr_Incl end) Hora_Ent,\n"
                    + "Max(Case when ER = 'E' then Status end) Status_Ent,\n"
                    + "Max(Case when ER = 'E' then Valor end) Valor_Ent,\n"
                    + "Max(Guias_Ent) Guias_Ent\n"
                    + "\n"
                    + "FROM (\n"
                    + "SELECT \n"
                    + "Pedido.CodFil, Pedido.PedidoCliente, Rt_Perc.ER, Pedido.Numero, convert(varchar, Rotas.Data, 112) Dt_Incl,\n"
                    + "Rt_Perc.HrCheg Hr_Incl, Rt_Perc.HrSaida,\n"
                    + "Case --when Rt_Perc.Sequencia is null and Pedido.Flag_Excl <> '*' then '0' --Recebido\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) <= 1 and len(Rt_Perc.HrSaida) <= 1 and Pedido.Flag_Excl <> '*' then '1' --Confirmado\n"
                    + "when Pedido.Flag_Excl = '*' then '2' --Recusado\n"
                    + "when len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) > 1 then '3' --Entregue\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) <= 1 and Pedido.Flag_Excl <> '*' then '6' --Em Processamento\n"
                    + "when Rt_Perc.Sequencia > 0 and len(Rt_Perc.HrCheg) > 1 and len(Rt_Perc.HrSaida) > 1 and Pedido.Flag_Excl <> '*' then '7' --Processado\n"
                    + "        else '0' --Recebido\n"
                    + "end Status,\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "(STUFF((SELECT ', '+Convert(Varchar,Rt_Guias.Guia)\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "									   and rt.ER = 'R'\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada\n"
                    + "		Order by Rt_Guias.Guia\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		))  Guias_Rec,\n"
                    + "\n"
                    + "(STUFF((SELECT ', '+Convert(Varchar,Rt_Guias.Guia)\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "									   and rt.ER = 'E'\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada\n"
                    + "		--Order by Rt_Guias.Guia\n"
                    + "		For XML PATH('')),1,2,''\n"
                    + "		))  Guias_Ent,\n"
                    + "\n"
                    + "(SELECT Sum(isnull(Rt_Guias.Valor,0))\n"
                    + "		FROM Rt_Guias (Nolock)\n"
                    + "		Inner join Rt_Perc  rt (Nolock)  on rt.Sequencia = Rt_Guias.Sequencia\n"
                    + "		                               and rt.Parada = Rt_Guias.Parada\n"
                    + "		WHERE Rt_Guias.Sequencia = Pedido.SeqRota\n"
                    + "		  and Rt_Guias.Parada = Pedido.Parada) Valor\n"
                    + "\n"
                    + "FROM Pedido (nolock)\n"
                    + "Left join Rt_Perc (nolock) on Pedido.SeqRota = Rt_Perc.Sequencia\n"
                    + "                  and Pedido.Parada  = Rt_Perc.Parada\n"
                    + "                  and Pedido.Numero = Rt_Perc.Pedido\n"
                    + " and (Rt_Perc.Flag_Excl <> '*' or Rt_Perc.Flag_Excl is null)\n"
                    + "Left join Rotas (nolock) on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "                  and Rotas.flag_excl <> '*'\n"
                    + "WHERE Pedido.PedidoCliente = " + pedidoCliente + " a \n"
                    + "Group by CodFil, PedidoCliente");
        }
    }

    public void excluiPedido(String pedidoCliente, String Dt_Excl, String Hr_Excl, String OperExcl, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Pedido SET Flag_Excl = ?, Dt_Excl = ?, Hr_Excl = ?, OperExcl = ? WHERE PedidoCliente = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("*");
            consulta.setString(Dt_Excl);
            consulta.setString(Hr_Excl);
            consulta.setString(OperExcl);
            consulta.setString(pedidoCliente);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PedidoDao.excluiPedido - " + e.getMessage() + "\r\n"
                    + " UPDATE Pedido SET Flag_Excl = ? WHERE PedidoCliente = " + pedidoCliente);
        }
    }

    public Pedido buscaPedidoCliente(String pedidoCliente, Persistencia persistencia) throws Exception {
        try {
            Pedido retorno = null;
            String sql = " SELECT convert(varchar, Dt_Incl, 112) Dt_InclF, * FROM Pedido WHERE PedidoCliente = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoCliente);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Pedido();

                retorno.setNumero(consulta.getString("Numero"));
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setData(consulta.getString("Data"));
                retorno.setTipo(consulta.getString("Tipo"));
                retorno.setCodCli1(consulta.getString("CodCli1"));
                retorno.setNRed1(consulta.getString("NRed1"));
                retorno.setRegiao1(consulta.getString("Regiao1"));
                retorno.setHora1O(consulta.getString("Hora1O"));
                retorno.setHora2O(consulta.getString("Hora2O"));
                retorno.setCodCli2(consulta.getString("CodCli2"));
                retorno.setNRed2(consulta.getString("Nred2"));
                retorno.setRegiao2(consulta.getString("Regiao2"));
                retorno.setHora1D(consulta.getString("Hora1D"));
                retorno.setHora2D(consulta.getString("Hora2D"));
                retorno.setCodCli3(consulta.getString("CodCli3"));
                retorno.setSeqSuprim(consulta.getString("SeqSuprim"));
                retorno.setSolicitante(consulta.getString("Solicitante"));
                retorno.setPedidoCliente(consulta.getString("PedidoCliente"));
                retorno.setValor(consulta.getBigDecimal("Valor"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setClassifSrv(consulta.getString("ClassifSrv"));
                retorno.setOperIncl(consulta.getString("OperIncl"));
                retorno.setRotaCanc(consulta.getString("RotaCanc"));
                retorno.setSeqCanc(consulta.getString("SeqCanc"));
                retorno.setDt_Incl(consulta.getString("Dt_InclF"));
                retorno.setHr_Incl(consulta.getString("Hr_Incl"));
                retorno.setChequesQtde(consulta.getInt("ChequesQtde"));
                retorno.setChequesValor(consulta.getString("ChequesValor"));
                retorno.setTicketsQtde(consulta.getString("TicketsQtde"));
                retorno.setTicketsValor(consulta.getString("TicketsValor"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setOperExcl(consulta.getString("OperExcl"));
                retorno.setDt_Excl(consulta.getString("Dt_Excl"));
                retorno.setHr_Excl(consulta.getString("Hr_Excl"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setSeqRota(consulta.getString("SeqRota"));
                retorno.setParada(consulta.getInt("Parada"));
                retorno.setHrOper(consulta.getString("HrOper"));
                retorno.setFlag_Excl(consulta.getString("Flag_Excl"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.buscaPedidoCliente - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Pedido WHERE PedidoCliente = " + pedidoCliente);
        }
    }

    public boolean existePedidoCliente(String pedidoCliente, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Pedido WHERE PedidoCliente = ? AND (Flag_excl IS NULL OR Flag_excl = '')";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoCliente);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.existePedidoCliente - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Pedido WHERE PedidoCliente = " + pedidoCliente);
        }
    }

    public BigDecimal getOS(String sequencia, String parada, String codFil, Persistencia persistencia) throws Exception {
        BigDecimal OS = null;
        try {
            String sql = "SELECT \n"
                    + "     OS \n"
                    + "FROM \n"
                    + "     pedido\n"
                    + " WHERE \n"
                    + "     numero = ((SELECT ISNULL(Rt_perc.Pedido,0) Pedido FROM Rt_perc WHERE Rt_perc.Sequencia = ? AND Rt_Perc.Parada = ?)) and codFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                OS = consulta.getBigDecimal("OS");
            }
            consulta.Close();
            return OS;
        } catch (Exception e) {
            throw new Exception("PedidoDao.getOS - " + e.getMessage() + "\r\n"
                    + "SELECT \n"
                    + "     OS \n"
                    + "FROM \n"
                    + "     pedido\n"
                    + " WHERE \n"
                    + "     numero = ((SELECT ISNULL(Rt_perc.Pedido,0) Pedido FROM Rt_perc  WHERE Rt_perc.Sequencia = ? AND Rt_Perc.Parada = ?)) and codFil = ?");
        }
    }

    /**
     * Busca OS do pedido pelo numero do pedido
     *
     * @param pedido
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getOS(String pedido, String codFil, Persistencia persistencia) throws Exception {
        BigDecimal OS = null;
        try {
            String sql = "SELECT OS FROM pedido"
                    + " WHERE Numero = ? and codFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedido);
            consulta.setString(codFil);
            consulta.select();
            while (consulta.Proximo()) {
                OS = consulta.getBigDecimal("OS");
            }
            consulta.Close();
            return OS;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar OS - " + e.getMessage());
        }
    }

    public String inserirPedidoNumero(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @Pedido Integer\n"
                    + "SET @Pedido = (SELECT isnull(max(numero), 0) + 1 Pedido FROM Pedido WHERE Codfil = ?)\n"
                    + "SET NOCOUNT ON\n"
                    + "INSERT INTO Pedido (Numero, CodFil, CodCli1, NRed1, Regiao1, CodCli2, NRed2, Regiao2, Data, \n"
                    + "    Tipo, ClassifSrv, Hora1O, Hora2O, Hora1D, Hora2D, Valor, Obs, OperIncl, Dt_Incl, Hr_Incl, \n"
                    + "    Operador, Dt_Alter, Hr_Alter, Situacao, PedidoCliente, Flag_Excl, TipoMoeda, OS, SeqRota)\n"
                    + "VALUES (@Pedido, ?, ?, \n"
                    + "    (SELECT TOP 1 NRed FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    (SELECT TOP 1 Regiao FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    ?,"
                    + "    (SELECT TOP 1 NRed FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    (SELECT TOP 1 Regiao FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?), ?, \n"
                    + "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + "    ?, ?, ?, ?, ?, ?, ?, ?, ?)\n"
                    + "\n"
                    + "SELECT @Pedido NumeroPedido\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pedido.getCodFil());

            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getCodCli1());
            consulta.setString(pedido.getCodCli1());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getCodCli1());
            consulta.setBigDecimal(pedido.getCodFil());

            consulta.setString(pedido.getCodCli2());
            consulta.setString(pedido.getCodCli2());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getCodCli2());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getData());

            consulta.setString(pedido.getTipo());
            consulta.setString(pedido.getClassifSrv());
            consulta.setString(pedido.getHora1O());
            consulta.setString(pedido.getHora2O());
            consulta.setString(pedido.getHora1D());
            consulta.setString(pedido.getHora2D());
            consulta.setBigDecimal(pedido.getValor());
            consulta.setString(pedido.getObs());
            consulta.setString(pedido.getOperIncl());
            consulta.setString(pedido.getDt_Incl());
            consulta.setString(pedido.getHr_Incl());

            consulta.setString(pedido.getOperador());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getHr_Alter());
            consulta.setString(pedido.getSituacao());
            consulta.setString(pedido.getPedidoCliente());
            consulta.setString(pedido.getFlag_Excl());
            consulta.setString(pedido.getTipoMoeda());
            consulta.setString(pedido.getOS());
            consulta.setBigDecimal(pedido.getSeqRota());

            consulta.select();
            String retorno = null;
            if (consulta.Proximo()) {
                retorno = consulta.getString("NumeroPedido");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.inserirPedidoNumero - " + e.getMessage() + "\r\n"
                    + "DECLARE @Pedido Integer\n"
                    + "SET @Pedido = (SELECT isnull(max(numero), 0) + 1 Pedido FROM Pedido WHERE Codfil = ?)\n"
                    + "SET NOCOUNT ON\n"
                    + "INSERT INTO Pedido (Numero, CodFil, CodCli1, NRed1, Regiao1, CodCli2, NRed2, Regiao2, Data, \n"
                    + "    Tipo, ClassifSrv, Hora1O, Hora2O, Hora1D, Hora2D, Valor, Obs, OperIncl, Dt_Incl, Hr_Incl, \n"
                    + "    Operador, Dt_Alter, Hr_Alter, Situacao, PedidoCliente, Flag_Excl, TipoMoeda, OS)\n"
                    + "VALUES (@Pedido, ?, ?, \n"
                    + "    (SELECT TOP 1 NRed FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    (SELECT TOP 1 Regiao FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    ?,"
                    + "    (SELECT TOP 1 NRed FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?),\n"
                    + "    (SELECT TOP 1 Regiao FROM Clientes WHERE Clientes.Codigo = ? AND Clientes.CodFil = ?), ?, \n"
                    + "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + "    ?, ?, ?, ?, ?, ?, ?, ?)\n"
                    + "\n"
                    + "SELECT @Pedido NumeroPedido\n");
        }
    }

    public void inserirPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append("INSERT INTO pedido(numero, codfil, regiao1, regiao2, data, tipo, codcli1, nred1,  hora1o, hora2o, codcli2, nred2,\n");
            sql.append("                    hora1d, hora2d, solicitante, valor, obs, classifsrv, operincl, dt_incl, hr_incl,\n");
            sql.append("                   os, operador, dt_alter, hr_alter, situacao, PedidoCliente, flag_excl, HrOper,");
            sql.append("                     ChequesQtde,ChequesValor, TicketsQtde, TicketsValor, TipoMoeda, SeqRota)\n");
            sql.append(" VALUES ((SELECT isnull(max(numero), 0) + 1 Pedido FROM Pedido WHERE Codfil = ?),");
            sql.append("?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);");

            sql.append("INSERT INTO ATMInter (OS, CodFil, Data, Hora, Hora2, CodCli, Regiao, Solicitante, PedidoCli, CodAST, OBS,\n")
                    .append(" Situacao, Pedido, OSFat, OperIncl, Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n")
                    .append("SELECT (SELECT ISNULL(MAX(OS),0) + 1 OS FROM ATMInter WHERE ATMInter.CodFil = Pedido.CodFil) OS,\n")
                    .append(" Pedido.CodFil CodFil, Pedido.Data Data, \n")
                    .append(" CASE WHEN SUBSTRING(Pedido.CodCli1,4,1) = '9' THEN Pedido.Hora1O ELSE Pedido.Hora1D END Hora,\n")
                    .append(" CASE WHEN SUBSTRING(Pedido.CodCli1,4,1) = '9' THEN Pedido.Hora2O ELSE Pedido.Hora2D END Hora2,\n")
                    .append(" CASE WHEN SUBSTRING(Pedido.CodCli1,4,1) = '9' THEN Pedido.CodCli1 ELSE Pedido.CodCli2 END CodCli,\n")
                    .append(" CASE WHEN SUBSTRING(Pedido.CodCli1,4,1) = '9' THEN Pedido.Regiao1 ELSE Pedido.Regiao2 END Regiao,\n")
                    .append(" Pedido.Solicitante Solicitante, Pedido.PedidoCliente PedidoCli,\n")
                    .append(" CASE WHEN SUBSTRING(Pedido.CodCli1,4,1) = '9' THEN 'AST' ELSE ' SUP' END CodAST, \n")
                    .append(" Pedido.Obs OBS, Pedido.Situacao Situacao, Pedido.Numero Pedido, Pedido.OS OSFat, Pedido.Operador OperIncl, \n")
                    .append(" Pedido.Dt_Alter Dt_Incl, Pedido.Hr_Alter Hr_Incl, Pedido.Operador Operador, Pedido.Dt_Alter Dt_Alter,\n")
                    .append(" Pedido.Hr_Alter Hr_Alter, Pedido.Flag_Excl Flag_Excl\n")
                    .append("FROM Pedido\n")
                    .append("WHERE (SUBSTRING(Pedido.CodCli1,4,1) = '9' OR SUBSTRING(Pedido.CodCli2,4,1) = '9')\n")
                    .append("AND Pedido.Numero = (SELECT MAX(Numero) FROM Pedido WHERE CodFil = ?);");

            /* ------------------------------------
            |   Delete de composições existentes  |
            -------------------------------------*/
            sql.append(" DELETE FROM PedidoDN WHERE Numero = (SELECT MAX(Numero) FROM Pedido WHERE CodFil = ?);\n");
            sql.append(" DELETE FROM PedidoMD WHERE Numero = (SELECT MAX(Numero) FROM Pedido WHERE CodFil = ?);\n");

            /* ------------------------------------
            |    Insert de composições (DN + MD)   |
            -------------------------------------*/
            if (null != pedido.getListaPedidosComposicao() && pedido.getListaPedidosComposicao().size() > 0) {
                for (PedidoDN listaPedidosComposicao : pedido.getListaPedidosComposicao()) {
                    if (listaPedidosComposicao.getTipo().equals("C")) {
                        // CÉDULA
                        sql.append(" INSERT INTO PedidoDN (Numero, CodFil, Codigo, Docto, Qtde)\n");
                        sql.append(" SELECT\n");
                        sql.append(" Pedido.Numero,\n");
                        sql.append(" Pedido.CodFil,\n");
                        sql.append(listaPedidosComposicao.getCodigo()).append(",\n");
                        sql.append(" 'PED',\n");
                        sql.append(listaPedidosComposicao.getQtde()).append("\n");
                        sql.append(" FROM Pedido\n");
                        sql.append(" JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                        sql.append("      FROM Pedido WHERE Pedido.CodFil = ?) AS UltimoPedido\n");
                        sql.append("   ON Pedido.Numero = UltimoPedido.Numero;");
                    } else {
                        // MOEDA
                        sql.append(" INSERT INTO PedidoMD (Numero, CodFil, Codigo, Docto, Qtde)\n");
                        sql.append(" SELECT\n");
                        sql.append(" Pedido.Numero,\n");
                        sql.append(" Pedido.CodFil,\n");
                        sql.append(Math.round(new Float(listaPedidosComposicao.getCodigo()) * 100)).append(",\n");
                        sql.append(" 'PED',\n");
                        sql.append(listaPedidosComposicao.getQtde()).append("\n");
                        sql.append(" FROM Pedido\n");
                        sql.append(" JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                        sql.append("      FROM Pedido WHERE Pedido.CodFil = ?) AS UltimoPedido\n");
                        sql.append("   ON Pedido.Numero = UltimoPedido.Numero;");
                    }
                }

                /* ------------------------------------
                |    Atualiza Valor Total do Pedido   |
                -------------------------------------*/
                sql.append(" UPDATE Pedido\n");
                sql.append(" SET Pedido.Valor = ISNULL((SELECT\n");
                sql.append("                            ISNULL(SUM((PedidoDN.Qtde * PedidoDN.Codigo)),0)\n");
                sql.append("                            FROM PedidoDN\n");
                sql.append("                            JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                sql.append("                                 FROM Pedido WHERE Pedido.CodFil = ?) AS UltimoPedido\n");
                sql.append("                              ON PedidoDN.Numero = UltimoPedido.Numero\n");
                sql.append("                            WHERE PedidoDN.Numero = (SELECT MAX(Numero) FROM Pedido WHERE Pedido.CodFil = ?)) + \n");
                sql.append("                           (SELECT\n");
                sql.append("                            ISNULL(SUM(((PedidoMD.Qtde  / 100) * PedidoMD.Codigo)),0)\n");
                sql.append("                            FROM PedidoMD\n");
                sql.append("                            JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                sql.append("                                 FROM Pedido) AS UltimoPedido\n");
                sql.append("                              ON PedidoMD.Numero = UltimoPedido.Numero\n");
                sql.append("                            WHERE PedidoMD.Numero = (SELECT MAX(Numero) FROM Pedido WHERE Pedido.CodFil = ?)), Pedido.Valor)\n");
                sql.append(" WHERE Pedido.Numero = (SELECT MAX(Numero) FROM Pedido WHERE Pedido.CodFil = ?);");
            }

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getRegiao1());
            consulta.setString(pedido.getRegiao2());
            consulta.setString(pedido.getData());
            consulta.setString(pedido.getTipo());
            consulta.setString(pedido.getCodCli1());
            consulta.setString(pedido.getNRed1());
            consulta.setString(pedido.getHora1O());
            consulta.setString(pedido.getHora2O());
            consulta.setString(pedido.getCodCli2());
            consulta.setString(pedido.getNRed2());
            consulta.setString(pedido.getHora1D());
            consulta.setString(pedido.getHora2D());
            consulta.setString(pedido.getSolicitante());
            consulta.setBigDecimal(pedido.getValor());
            consulta.setString(pedido.getObs());
            consulta.setString(pedido.getClassifSrv());
            consulta.setString(pedido.getOperIncl());
            consulta.setString(pedido.getDt_Incl());
            consulta.setString(pedido.getHr_Incl());
            consulta.setBigDecimal(pedido.getOS());
            consulta.setString(pedido.getOperador());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getHr_Alter());
            consulta.setString(pedido.getSituacao());
            consulta.setString(pedido.getPedidoCliente());
            consulta.setString(pedido.getFlag_Excl());
            consulta.setString(pedido.getHrOper());
            consulta.setInt(pedido.getChequesQtde());
            consulta.setBigDecimal(pedido.getChequesValor());
            consulta.setString(pedido.getTicketsQtde());
            consulta.setBigDecimal(pedido.getTicketsValor());
            consulta.setString(pedido.getTipoMoeda());
            consulta.setBigDecimal(pedido.getSeqRota());

            // ATMInter
            consulta.setBigDecimal(pedido.getCodFil());

            // Delete de composições existentes
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setBigDecimal(pedido.getCodFil());

            // Lista de Composições
            if (null != pedido.getListaPedidosComposicao() && pedido.getListaPedidosComposicao().size() > 0) {
                for (PedidoDN listaPedidosComposicao : pedido.getListaPedidosComposicao()) {
                    consulta.setBigDecimal(pedido.getCodFil());
                }

                // Total do Pedido
                consulta.setBigDecimal(pedido.getCodFil());
                consulta.setBigDecimal(pedido.getCodFil());
                consulta.setBigDecimal(pedido.getCodFil());
                consulta.setBigDecimal(pedido.getCodFil());
            }

            consulta.insert();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("PedidoDao.inserirPedido - " + e.getMessage() + "\r\n" + sql.toString());
        }

    }

    public String salvarPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        String retorno = null;
        for (int i = 0; i < 20; i++) {
            try {
                sql = new StringBuilder();
                retorno = null;
                String sqlPedido = "SELECT isnull(max(numero), 0) + 1 Pedido FROM Pedido WHERE Codfil = ? ";
                Consulta consultaPedido = new Consulta(sqlPedido, persistencia);
                consultaPedido.setBigDecimal(pedido.getCodFil());
                consultaPedido.select();
                if (consultaPedido.Proximo()) {
                    retorno = consultaPedido.getString("Pedido");
                }

                /* ------------------------------------
                |      Insert em Tabela Pedido        |
                -------------------------------------*/
                sql.append(" INSERT INTO pedido(numero, codfil, regiao1, regiao2, data, tipo, codcli1, nred1,  hora1o, hora2o, codcli2, nred2,\n");
                sql.append("                    hora1d, hora2d, solicitante, valor, obs, classifsrv, operincl, dt_incl, hr_incl,\n");
                sql.append("                   os, operador, dt_alter, hr_alter, situacao, PedidoCliente, flag_excl, HrOper,");
                sql.append("                     ChequesQtde,ChequesValor, TicketsQtde, TicketsValor, SeqRota)\n");
                sql.append(" VALUES (?,?,(SELECT top 1 regiao FROM clientes WHERE nred like ?  ), ");
                sql.append("(SELECT top 1 regiao FROM clientes WHERE nred like ? ),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                /* ------------------------------------
                |   Delete de composições existentes  |
                -------------------------------------*/
                sql.append(" DELETE FROM PedidoDN WHERE Numero = (SELECT MAX(Numero) FROM Pedido);\n");
                sql.append(" DELETE FROM PedidoMD WHERE Numero = (SELECT MAX(Numero) FROM Pedido);\n");

                /* ------------------------------------
                |    Insert de composições (DN + MD)   |
                -------------------------------------*/
                if (null != pedido.getListaPedidosComposicao() && pedido.getListaPedidosComposicao().size() > 0) {
                    for (PedidoDN listaPedidosComposicao : pedido.getListaPedidosComposicao()) {
                        if (listaPedidosComposicao.getTipo().equals("C")) {
                            // CÉDULA
                            sql.append(" INSERT INTO PedidoDN (Numero, CodFil, Codigo, Docto, Qtde)\n");
                            sql.append(" SELECT\n");
                            sql.append(" Pedido.Numero,\n");
                            sql.append(" Pedido.CodFil,\n");
                            sql.append(listaPedidosComposicao.getCodigo()).append(",\n");
                            sql.append(" 'PED',\n");
                            sql.append(listaPedidosComposicao.getQtde()).append("\n");
                            sql.append(" FROM Pedido\n");
                            sql.append(" JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                            sql.append("      FROM Pedido) AS UltimoPedido\n");
                            sql.append("   ON Pedido.Numero = UltimoPedido.Numero;");
                        } else {
                            // MOEDA
                            sql.append(" INSERT INTO PedidoMD (Numero, CodFil, Codigo, Docto, Qtde)\n");
                            sql.append(" SELECT\n");
                            sql.append(" Pedido.Numero,\n");
                            sql.append(" Pedido.CodFil,\n");
                            sql.append(Math.round(new Float(listaPedidosComposicao.getCodigo()) * 100)).append(",\n");
                            sql.append(" 'PED',\n");
                            sql.append(listaPedidosComposicao.getQtde()).append("\n");
                            sql.append(" FROM Pedido\n");
                            sql.append(" JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                            sql.append("      FROM Pedido) AS UltimoPedido\n");
                            sql.append("   ON Pedido.Numero = UltimoPedido.Numero;");
                        }
                    }

                    /* ------------------------------------
                    |    Atualiza Valor Total do Pedido   |
                    -------------------------------------*/
                    sql.append(" UPDATE Pedido\n");
                    sql.append(" SET Pedido.Valor = ISNULL((SELECT\n");
                    sql.append("                            ISNULL(SUM((PedidoDN.Qtde * PedidoDN.Codigo)),0)\n");
                    sql.append("                            FROM PedidoDN\n");
                    sql.append("                            JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                    sql.append("                                 FROM Pedido) AS UltimoPedido\n");
                    sql.append("                              ON PedidoDN.Numero = UltimoPedido.Numero\n");
                    sql.append("                            WHERE PedidoDN.Numero = (SELECT MAX(Numero) FROM Pedido)) + \n");
                    sql.append("                           (SELECT\n");
                    sql.append("                            ISNULL(SUM(((PedidoMD.Qtde  / 100) * PedidoMD.Codigo)),0)\n");
                    sql.append("                            FROM PedidoMD\n");
                    sql.append("                            JOIN(SELECT MAX(Pedido.Numero) Numero\n");
                    sql.append("                                 FROM Pedido) AS UltimoPedido\n");
                    sql.append("                              ON PedidoMD.Numero = UltimoPedido.Numero\n");
                    sql.append("                            WHERE PedidoMD.Numero = (SELECT MAX(Numero) FROM Pedido)), Pedido.Valor)\n");
                    sql.append(" WHERE Pedido.Numero = (SELECT MAX(Numero) FROM Pedido);");
                }

                Consulta consulta = new Consulta(sql.toString(), persistencia);
                consulta.setString(retorno);
                consulta.setBigDecimal(pedido.getCodFil());
                //Para pegara a regiao para as regioes 1 e 2
                consulta.setString(pedido.getNRed1());
                consulta.setString(pedido.getNRed2());
                //resto da inserção, na ordem do values
                if (pedido.getData().contains("T")) {
                    pedido.setData(pedido.getData().split("T")[0].replace("-", ""));
                }
                consulta.setString(pedido.getData());
                consulta.setString(pedido.getTipo());
                consulta.setString(pedido.getCodCli1());
                consulta.setString(pedido.getNRed1());
                consulta.setString(pedido.getHora1O());
                consulta.setString(pedido.getHora2O());
                consulta.setString(pedido.getCodCli2());
                consulta.setString(pedido.getNRed2());
                consulta.setString(pedido.getHora1D());
                consulta.setString(pedido.getHora2D());
                consulta.setString(pedido.getSolicitante());
                consulta.setBigDecimal(pedido.getValor());
                consulta.setString(pedido.getObs());
                consulta.setString(pedido.getClassifSrv());
                consulta.setString(pedido.getOperIncl());
                consulta.setString(pedido.getDt_Incl());
                consulta.setString(pedido.getHr_Incl());
                consulta.setBigDecimal(pedido.getOS());
                consulta.setString(pedido.getOperador());
                consulta.setString(pedido.getDt_Alter());
                consulta.setString(pedido.getHr_Alter());
                consulta.setString(pedido.getSituacao());
                consulta.setString(pedido.getPedidoCliente());
                consulta.setString(pedido.getFlag_Excl());
                if (null == pedido.getHrOper() || pedido.getHrOper().equals("")) {
                    pedido.setHrOper(pedido.getHr_Alter());
                }
                consulta.setString(pedido.getHrOper());
                consulta.setInt(pedido.getChequesQtde());
                consulta.setBigDecimal(pedido.getChequesValor());
                consulta.setString(pedido.getTicketsQtde());
                consulta.setBigDecimal(pedido.getTicketsValor());
                consulta.setBigDecimal(pedido.getSeqRota());

                consulta.insert();
                consulta.Close();

                if (null != retorno && !retorno.equals("") && !retorno.equals("0")) {
                    break;
                }

            } catch (Exception e) {
                retorno = null;
                System.out.println(e);
            }
        }
        if (null == retorno || retorno.equals("") || retorno.equals("0")) {
            throw new Exception("PedidoDao.salvarPedido - Tentativas excedidas");
        }
        return retorno;
    }

    public void salvarPedidoItens(Pedido pedido, List<Pedido> pedidoItens, Persistencia persistencia) throws Exception {
        String sql = "DELETE B\n"
                + " FROM PedidoRefeicao      AS A\n"
                + " JOIN PedidoRefeicaoItens AS B\n"
                + "   ON A.Sequencia = B.Sequencia\n"
                + " WHERE A.Codcli = ?\n"
                + " AND   A.CodFil = ? \n"
                + " AND   A.Data   = ?;\n"
                + " DELETE FROM PedidoRefeicao\n"
                + " WHERE Codcli = ?\n"
                + " AND   CodFil = ? \n"
                + " AND   Data   = ?;\n"
                + " INSERT INTO PedidoRefeicao (Sequencia, Codcli, CodFil, Data, Solicitante, Obs, Operador, Dt_alter, Hr_Alter) VALUES (\n"
                + " ﻿(SELECT ISNULL((MAX(Sequencia) + 1), 1) FROM PedidoRefeicao), ?, ?, ?, ?, ?, ?, ?, ?\n"
                + " );";

        for (Pedido pedidoIten : pedidoItens) {
            sql += " INSERT INTO PedidoRefeicaoItens (Sequencia, Ordem, Secao, QtdeCafe, QtdeAlmoco, QtdeJantar, QtdeCeia, Operador, Dt_alter, Hr_Alter) VALUES (\n"
                    + " (SELECT MAX(Sequencia) FROM PedidoRefeicao),\n"
                    + " (SELECT ISNULL((MAX(Ordem) + 1), 1) FROM PedidoRefeicaoItens WHERE Sequencia = (SELECT MAX(Sequencia) FROM PedidoRefeicao)),\n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?\n"
                    + " );";
        }

        Consulta consulta = new Consulta(sql, persistencia);
        consulta.setString(pedido.getCodCli1());
        consulta.setBigDecimal(pedido.getCodFil());
        consulta.setString(pedido.getData());
        consulta.setString(pedido.getCodCli1());
        consulta.setBigDecimal(pedido.getCodFil());
        consulta.setString(pedido.getData());
        consulta.setString(pedido.getCodCli1());
        consulta.setBigDecimal(pedido.getCodFil());
        consulta.setString(pedido.getData());
        consulta.setString(pedido.getSolicitante());
        consulta.setString(pedido.getObs());
        consulta.setString(pedido.getOperador());
        consulta.setString(pedido.getDt_Alter());
        consulta.setString(pedido.getHr_Alter());

        for (Pedido pedidoIten : pedidoItens) {
            consulta.setString(pedidoIten.getSecao());
            consulta.setBigDecimal(pedidoIten.getQtdeCafe());
            consulta.setBigDecimal(pedidoIten.getQtdeAlmoco());
            consulta.setBigDecimal(pedidoIten.getQtdeJanta());
            consulta.setBigDecimal(pedidoIten.getQtdeCeia());
            consulta.setString(pedidoIten.getOperador());
            consulta.setString(pedidoIten.getDt_Alter());
            consulta.setString(pedidoIten.getHr_Alter());
        }

        consulta.insert();
        consulta.Close();
    }

    /**
     * Busca dados de cédula / moedas
     *
     * @param MoedaCedula - M = Moeda / C = Cedula
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TesMoedas> buscaCedulasMoedas(String MoedaCedula, Persistencia persistencia) throws Exception {
        List<TesMoedas> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM ";
            if (MoedaCedula.equals("C")) {
                sql += "TesMoedasCD\n";
            } else {
                sql += "TesMoedasMD\n";
            }
            sql += " ORDER BY Codigo";

            Consulta consult = new Consulta(sql, persistencia);
            consult.select();

            TesMoedas tesMoedas = new TesMoedas();

            while (consult.Proximo()) {
                tesMoedas = new TesMoedas();

                tesMoedas.setCodMoeda(consult.getString("CodMoeda"));
                if (MoedaCedula.equals("M") && Integer.parseInt(consult.getString("Codigo")) < 100) {
                    if (consult.getString("Codigo").length() < 2) {
                        tesMoedas.setCodigo("0.0" + consult.getString("Codigo"));
                    } else {
                        tesMoedas.setCodigo("0." + consult.getString("Codigo"));
                    }
                } else if (MoedaCedula.equals("M")) {
                    switch (consult.getString("Codigo")) {
                        case "100":
                            tesMoedas.setCodigo("1.00");
                            break;
                        case "200":
                            tesMoedas.setCodigo("2.00");
                            break;
                        case "300":
                            tesMoedas.setCodigo("3.00");
                            break;
                        case "400":
                            tesMoedas.setCodigo("4.00");
                            break;
                        case "500":
                            tesMoedas.setCodigo("5.00");
                            break;
                        case "600":
                            tesMoedas.setCodigo("6.00");
                            break;
                        case "700":
                            tesMoedas.setCodigo("7.00");
                            break;
                        case "800":
                            tesMoedas.setCodigo("8.00");
                            break;
                        case "900":
                            tesMoedas.setCodigo("9.00");
                            break;
                        case "1000":
                            tesMoedas.setCodigo("10.00");
                            break;
                    }
                } else {
                    tesMoedas.setCodigo(consult.getString("Codigo"));
                }
                tesMoedas.setDt_Alter(consult.getString("Valor"));
                tesMoedas.setHr_Alter(consult.getString("Operador"));
                tesMoedas.setOperador(consult.getString("Dt_Alter"));
                tesMoedas.setValor(consult.getString("Hr_Alter"));

                retorno.add(tesMoedas);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados de cédulas/moedas - " + e.getMessage());
        }
        return retorno;
    }

    public void tratarPedidoParadaExcluida(String pedidoNumero, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            // Verifica se pedido está roterizado em para excluída
            sql = "select COUNT(*) qtde\n"
                    + "from pedido \n"
                    + "JOIN rt_perc\n"
                    + "  ON pedido.seqrota = rt_perc.sequencia\n"
                    + " AND pedido.parada = rt_perc.parada \n"
                    + "where numero = ?\n"
                    + "AND   rt_perc.flag_excl <> '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoNumero);
            consulta.select();

            Boolean chavePedido = false;

            while (consulta.Proximo()) {
                if (consulta.getString("qtde").equals("0")) {
                    chavePedido = true;
                }
            }

            consulta.Close();

            // Volta situação de pedido pendente se pedido não estiver roteirizado ou estiver em parada excluída
            if (chavePedido) {
                sql = "UPDATE pedido\n"
                        + "SET situacao = 'PD',\n"
                        + "    seqrota  = null,\n"
                        + "    parada   = null\n"
                        + "where numero = ?\n"
                        + "AND   codfil = ?";

                consulta = new Consulta(sql, persistencia);

                consulta.setString(pedidoNumero);
                consulta.setString(codFil);

                consulta.update();
                consulta.Close();
            }

        } catch (Exception e) {
            throw new Exception("Falha ao tratar pedidos em paradas excluídas - " + e.getMessage());
        }
    }

    public void editarPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append(" Update pedido set \n"
                    + " codfil = ?, \n"
                    + " regiao1 = ?, \n"
                    + " regiao2 = ?, \n"
                    + " data = ?, \n"
                    + " tipo = ?, \n"
                    + " codcli1 = ?, \n"
                    + " nred1 = ?, \n"
                    + " hora1o = ?, \n"
                    + " hora2o = ?, \n"
                    + " codcli2 = ?, \n"
                    + " nred2 = ?, \n"
                    + " hora1d = ?, \n"
                    + " hora2d = ?, \n"
                    + " solicitante = ?, \n"
                    //                    + " valor = ?, \n"
                    + " obs = ?, \n"
                    + " classifsrv = ?, \n"
                    //                    + " operincl = ?, \n"
                    //                    + " dt_incl = ?, \n"
                    //                    + " hr_incl = ?, \n"
                    + " os = ?, \n"
                    + " operador = ?, \n"
                    + " dt_alter = ?, \n"
                    + " hr_alter = ?, \n"
                    + " situacao = ?, \n"
                    + " PedidoCliente = ?, \n"
                    + " flag_excl = ?, \n"
                    + " TipoMoeda = ?, \n"
                    + " HrOper = ?, \n"
                    //                    + " ChequesQtde = ?, \n"
                    //                    + " ChequesValor = ?, \n"
                    //                    + " TicketsQtde = ?, \n"
                    //                    + " TicketsValor = ?, \n"
                    //                    + " SeqRota = ? \n"
                    + " OperExcl = ?, \n"
                    + "Dt_Excl = ?, \n"
                    + "Hr_Excl = ? \n"
                    + " WHERE Pedido.Numero = ? \n"
                    + "   and Pedido.CodFil = ? ;");

            /* ------------------------------------
            |   Delete de composições existentes  |
            -------------------------------------*/
            sql.append(" DELETE FROM PedidoDN WHERE Numero = ").append(pedido.getNumero().toPlainString()).append(" AND CodFil = ").append(pedido.getCodFil().toPlainString().replace(".0", "")).append(";\n");
            sql.append(" DELETE FROM PedidoMD WHERE Numero = ").append(pedido.getNumero().toPlainString()).append(" AND CodFil = ").append(pedido.getCodFil().toPlainString().replace(".0", "")).append(";\n");

            /* ------------------------------------
            |    Insert de composições (DN + MD)   |
            -------------------------------------*/
            if (null != pedido.getListaPedidosComposicao() && pedido.getListaPedidosComposicao().size() > 0) {
                for (PedidoDN listaPedidosComposicao : pedido.getListaPedidosComposicao()) {
                    if (listaPedidosComposicao.getTipo().equals("C")) {
                        // CÉDULA
                        sql.append(" INSERT INTO PedidoDN (Numero, CodFil, Codigo, Docto, Qtde)\n");
                        sql.append(" SELECT\n");
                        sql.append(" ").append(pedido.getNumero()).append(",\n");
                        sql.append(" ").append(pedido.getCodFil()).append(",\n");
                        sql.append(listaPedidosComposicao.getCodigo()).append(",\n");
                        sql.append(" 'PED',\n");
                        sql.append(listaPedidosComposicao.getQtde()).append(";\n");
                    } else {
                        // MOEDA
                        sql.append(" INSERT INTO PedidoMD (Numero, CodFil, Codigo, Docto, Qtde)\n");
                        sql.append(" SELECT\n");
                        sql.append(" ").append(pedido.getNumero()).append(",\n");
                        sql.append(" ").append(pedido.getCodFil()).append(",\n");
                        sql.append(Math.round(new Float(listaPedidosComposicao.getCodigo()) * 100)).append(",\n");
                        sql.append(" 'PED',\n");
                        sql.append(listaPedidosComposicao.getQtde()).append(";\n");
                    }
                }

                /* ------------------------------------
                |    Atualiza Valor Total do Pedido   |
                -------------------------------------*/
                sql.append(" UPDATE Pedido\n");
                sql.append(" SET Pedido.Valor = ISNULL((SELECT\n");
                sql.append("                            ISNULL(SUM((PedidoDN.Qtde * PedidoDN.Codigo)),0)\n");
                sql.append("                            FROM PedidoDN\n");
                sql.append("                            WHERE PedidoDN.Numero = ").append(pedido.getNumero().toPlainString()).append(" AND CodFil = ").append(pedido.getCodFil().toPlainString().replace(".0", "")).append(" ) + \n");
                sql.append("                           (SELECT\n");
                sql.append("                            ISNULL(SUM(((PedidoMD.Qtde  / 100) * PedidoMD.Codigo)),0)\n");
                sql.append("                            FROM PedidoMD\n");
                sql.append("                            WHERE PedidoMD.Numero = ").append(pedido.getNumero().toPlainString()).append(" AND CodFil = ").append(pedido.getCodFil().toPlainString().replace(".0", "")).append("), Pedido.Valor)\n");
                sql.append(" WHERE Pedido.Numero = ").append(pedido.getNumero().toPlainString()).append(";");
            }

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            //consulta.setString(retorno);
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getRegiao1());
            consulta.setString(pedido.getRegiao2());
            consulta.setString(pedido.getData());
            consulta.setString(pedido.getTipo());
            consulta.setString(pedido.getCodCli1());
            consulta.setString(pedido.getNRed1());
            consulta.setString(pedido.getHora1O());
            consulta.setString(pedido.getHora2O());
            consulta.setString(pedido.getCodCli2());
            consulta.setString(pedido.getNRed2());
            consulta.setString(pedido.getHora1D());
            consulta.setString(pedido.getHora2D());
            consulta.setString(pedido.getSolicitante());
//            consulta.setBigDecimal(pedido.getValor());
            consulta.setString(pedido.getObs());
            consulta.setString(pedido.getClassifSrv());
//            consulta.setString(pedido.getOperIncl());
//            consulta.setString(pedido.getDt_Incl());
//            consulta.setString(pedido.getHr_Incl());
            consulta.setBigDecimal(pedido.getOS());
            consulta.setString(pedido.getOperador());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getHr_Alter());
            consulta.setString(pedido.getSituacao());
            consulta.setString(pedido.getPedidoCliente());
            consulta.setString(pedido.getFlag_Excl());
            consulta.setString(pedido.getTipoMoeda());
            consulta.setString(pedido.getHrOper());
//            consulta.setInt(pedido.getChequesQtde());
//            consulta.setBigDecimal(pedido.getChequesValor());
//            consulta.setString(pedido.getTicketsQtde());
//            consulta.setBigDecimal(pedido.getTicketsValor());
//            consulta.setBigDecimal(pedido.getSeqRota());
            consulta.setString(pedido.getOperExcl());
            consulta.setString(pedido.getDt_Excl());
            consulta.setString(pedido.getHr_Excl());
            consulta.setBigDecimal(pedido.getNumero());
            consulta.setBigDecimal(pedido.getCodFil());

            consulta.update();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception(sql.toString());
        }
    }

    public void tratarPreOrderPedido(Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            /* ------------------------------------
            |    Verificar último ID em Pedido    |
            -------------------------------------*/
            sql.append("DECLARE @ultimoIdPedido INT = (SELECT ISNULL(MAX(Pedido.Numero),0) FROM Pedido);\n");

            /* ------------------------------------
            |    Verificar último ID em ATMInter  |
            -------------------------------------*/
            sql.append("DECLARE @ultimoIdATM INT    = (SELECT ISNULL(MAX(ATMInter.OS),0) FROM ATMInter);\n");

            /* -------------------------------------
            |       Insert em Tabela Pedido        |
            --------------------------------------*/
            sql.append(" INSERT INTO Pedido (Numero, CodFil, Data, Tipo, CodCli1, NRed1, Regiao1, Hora1O, Hora2O, CodCli2, NRed2, Regiao2, hora1D, Hora2D, Solicitante, PedidoCliente, Valor, Obs, ClassifSrv, OperIncl, Dt_Incl, Hr_Incl, OS, Operador, Dt_Alter, Hr_Alter, Situacao, Flag_Excl)\n");
            sql.append(" SELECT\n");
            sql.append(" (@ultimoIdPedido + ROW_NUMBER() OVER(ORDER BY PreOrder.Sequencia)),\n");
            sql.append(" PreOrder.CodFil,\n");
            sql.append(" PreOrder.DtColeta,\n");
            sql.append(" 'T',\n");
            sql.append(" PreOrder.CodCli1,\n");
            sql.append(" CliOri.NRed,\n");
            sql.append(" CliOri.Regiao,\n");
            sql.append(" PreOrder.Hora1O,\n");
            sql.append(" PreOrder.Hora2O,\n");
            sql.append(" PreOrder.CodCli2,\n");
            sql.append(" CliDst.NRed,\n");
            sql.append(" CliDst.Regiao,\n");
            sql.append(" PreOrder.Hora1D,\n");
            sql.append(" PreOrder.Hora2D,\n");
            sql.append(" PreOrder.Solicitante,\n");
            sql.append(" PreOrder.PedidoCliente,\n");
            sql.append(" ISNULL(Total.Valor, PreOrder.Valor),\n");
            sql.append(" PreOrder.Obs,\n");
            sql.append(" PreOrder.ClassifSrv,\n");
            sql.append(" PreOrder.OperIncl,\n");
            sql.append(" PreOrder.Dt_Incl,\n");
            sql.append(" PreOrder.Hr_Incl,\n");
            sql.append(" PreOrder.OS,\n");
            sql.append(" PreOrder.Operador,\n");
            sql.append(" PreOrder.Dt_Alter,\n");
            sql.append(" PreOrder.Hr_Alter,\n");
            sql.append(" PreOrder.Situacao,\n");
            sql.append(" ''\n");
            sql.append(" FROM PreOrder\n");
            sql.append(" LEFT JOIN Clientes CliOri\n");
            sql.append("   ON PreOrder.CodCli1 = CliOri.Codigo\n");
            sql.append("  AND PreOrder.CodFil  = CliOri.CodFil\n");
            sql.append(" LEFT JOIN Clientes CliDst\n");
            sql.append("   ON PreOrder.CodCli2 = CliDst.Codigo\n");
            sql.append("  AND PreOrder.CodFil  = CliDst.CodFil\n");
            sql.append(" LEFT JOIN (SELECT\n");
            sql.append("            PreOrderVol.Sequencia,\n");
            sql.append("            PreOrderVol.CodFil,\n");
            sql.append("            SUM((PreOrderVol.Qtde * PreOrderVol.Lacre)) AS Valor\n");
            sql.append("            FROM PreOrderVol\n");
            sql.append("            GROUP BY PreOrderVol.Sequencia, \n");
            sql.append("                     PreOrderVol.CodFil) Total\n");
            sql.append("   ON PreOrder.Sequencia = Total.Sequencia\n");
            sql.append("  AND PreOrder.CodFil    = Total.CodFil\n");
            sql.append(" ORDER BY PreOrder.Sequencia;\n");

            /* -------------------------------------
            |      Insert em Tabela PedidoDN       |
            --------------------------------------*/
            sql.append(" INSERT INTO PedidoDN (Numero, CodFil, Codigo, Docto, Qtde)\n");
            sql.append(" SELECT\n");
            sql.append(" SeqPedido.NumeroPedido,\n");
            sql.append(" PreOrderVol.CodFil,\n");
            sql.append(" PreOrderVol.Lacre,\n");
            sql.append(" 'PED',\n");
            sql.append(" PreOrderVol.Qtde\n");
            sql.append(" FROM PreOrderVol \n");
            sql.append(" JOIN (SELECT\n");
            sql.append("       (@ultimoIdPedido + ROW_NUMBER() OVER(ORDER BY PreOrder.Sequencia)) NumeroPedido,\n");
            sql.append("       PreOrder.Sequencia\n");
            sql.append("       FROM PreOrder) SeqPedido\n");
            sql.append("   ON PreOrderVol.Sequencia = SeqPedido.Sequencia\n");
            sql.append(" ORDER BY PreOrderVol.Sequencia;\n");

            /* -------------------------------------
            |      ﻿Insert em Tabela ATMInter      |
            --------------------------------------*/
            sql.append(" INSERT INTO ATMInter (OS, CodFil, Data, Hora, Hora2, CodCli, Regiao, Solicitante, PedidoCli, CodAST, OBS, Situacao, Pedido, OSFat, OperIncl, Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n");
            sql.append(" SELECT\n");
            sql.append(" (@ultimoIdATM + ROW_NUMBER() OVER(ORDER BY PreOrder.Sequencia)),\n");
            sql.append(" PreOrder.CodFil,\n");
            sql.append(" PreOrder.DtColeta,\n");
            sql.append(" PreOrder.Hora1D,\n");
            sql.append(" PreOrder.Hora2D,\n");
            sql.append(" CASE WHEN SUBSTRING(PreOrder.CodCli1,4,1) = '9' THEN PreOrder.CodCli1 ELSE PreOrder.CodCli2 END,\n");
            sql.append(" CASE WHEN SUBSTRING(PreOrder.CodCli1,4,1) = '9' THEN CliOri.Regiao    ELSE CliDst.Regiao    END,\n");
            sql.append(" PreOrder.Solicitante,\n");
            sql.append(" PreOrder.PedidoCliente,\n");
            sql.append(" ' SUP',\n");
            sql.append(" PreOrder.Obs,\n");
            sql.append(" PreOrder.Situacao,\n");
            sql.append(" SeqPedido.NumeroPedido,\n");
            sql.append(" PreOrder.OS,\n");
            sql.append(" PreOrder.Operador,\n");
            sql.append(" PreOrder.Dt_Alter,\n");
            sql.append(" PreOrder.Hr_Alter,\n");
            sql.append(" PreOrder.Operador,\n");
            sql.append(" PreOrder.Dt_Alter,\n");
            sql.append(" PreOrder.Hr_Alter,\n");
            sql.append(" ''\n");
            sql.append(" FROM PreOrder \n");
            sql.append(" JOIN (SELECT\n");
            sql.append("       (@ultimoIdPedido + ROW_NUMBER() OVER(ORDER BY PreOrder.Sequencia)) NumeroPedido,\n");
            sql.append("       PreOrder.Sequencia\n");
            sql.append("       FROM PreOrder) SeqPedido\n");
            sql.append("   ON PreOrder.Sequencia = SeqPedido.Sequencia\n");
            sql.append(" LEFT JOIN Clientes CliOri\n");
            sql.append("   ON PreOrder.CodCli1 = CliOri.Codigo\n");
            sql.append("  AND PreOrder.CodFil  = CliOri.CodFil\n");
            sql.append(" LEFT JOIN Clientes CliDst\n");
            sql.append("   ON PreOrder.CodCli2 = CliDst.Codigo\n");
            sql.append("  AND PreOrder.CodFil  = CliDst.CodFil\n");
            sql.append(" WHERE (SUBSTRING(PreOrder.CodCli1,4,1) = '9' OR SUBSTRING(PreOrder.CodCli2,4,1) = '9')\n");
            sql.append(" ORDER BY PreOrder.Sequencia;\n");

            /* ---------------------------------------
            |  Limpar tabelas PreOrder e PreOrderVol  |
            -----------------------------------------*/
            sql.append(" DELETE FROM PreOrderVol;\n");
            sql.append(" DELETE FROM PreOrder;");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("PedidoDao.tratarPreOrderPedido - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    //LISTAGENS PAGINADAS
    public List<Pedido> listapaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Pedido> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY Pedido.Numero desc) AS RowNum, \n"
                    + "Convert(Varchar,Pedido.Data,112) DataF, Pedido.*, \n"
                    + "Reg1.Descricao Reg1,  Reg2.Descricao Reg2, Cli1.Ende Ende1, Cli1.Cidade Cidade1, \n"
                    + "Cli1.Bairro Bairro1, Cli1.Estado UF1, Cli2.Ende Ende2, Cli2.Cidade Cidade2, \n"
                    + "Cli2.Bairro Bairro2, Cli2.Estado UF2, isnull(OS_Vig.GTVQtde,0) Orcamento \n"
                    + "FROM Pedido \n"
                    + "Left join Regiao Reg1  on Reg1.Regiao = Pedido.Regiao1 \n"
                    + "                      and Reg1.CodFil = Pedido.CodFil  \n"
                    + "Left join Regiao Reg2  on Reg2.Regiao = Pedido.Regiao2 \n"
                    + "                      and Reg2.CodFil = Pedido.CodFil  \n"
                    + "Left join Clientes Cli1  on Cli1.Codigo = Pedido.CodCli1 \n"
                    + "                        and Cli1.CodFil = Pedido.CodFil \n"
                    + "Left join Clientes Cli2  on Cli2.Codigo = Pedido.CodCli2 \n"
                    + "                        and Cli2.CodFil = Pedido.CodFil \n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS \n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil \n"
                    + " WHERE Pedido.codfil is not null \n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + "\n";
                }
            }
            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE RowNum >= ? "
                    + "  AND RowNum < ? ";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Pedido pedido;

            while (consulta.Proximo()) {
                pedido = new Pedido();

                pedido.setNumero(consulta.getString("Numero"));
                pedido.setOS(consulta.getString("OS"));
                pedido.setCodFil(consulta.getBigDecimal("CodFil"));
                pedido.setData(consulta.getString("DataF"));

                switch (consulta.getString("Tipo")) {
                    case "T":
                        pedido.setTipo("TRANSPORTE");
                        break;
                    case "C":
                        pedido.setTipo("CANCELAMENTO");
                        break;
                    case "M":
                        pedido.setTipo("MATERIAL");
                        break;
                    case "O":
                        pedido.setTipo("OCORRÊNCIA");
                        break;

                    default:
                        pedido.setTipo(consulta.getString("Tipo"));
                        break;
                }

                pedido.setCodCli1(consulta.getString("CodCli1"));

                if (consulta.getString("CodCli1").equals("9996900")) {
                    pedido.setTipoNoPedido("entrega");
                } else {
                    pedido.setTipoNoPedido("recolhimento");
                }

                pedido.setNRed1(consulta.getString("NRed1"));
                pedido.setRegiao1(consulta.getString("Regiao1"));
                pedido.setHora1O(consulta.getString("Hora1O"));
                pedido.setHora2O(consulta.getString("Hora2O"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setNRed2(consulta.getString("Nred2"));
                pedido.setRegiao2(consulta.getString("Regiao2"));
                pedido.setHora1D(consulta.getString("Hora1D"));
                pedido.setHora2D(consulta.getString("Hora2D"));
                pedido.setCodCli3(consulta.getString("CodCli3"));
                pedido.setSeqSuprim(consulta.getString("SeqSuprim"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setPedidoCliente(consulta.getString("PedidoCliente"));
                pedido.setValor(consulta.getBigDecimal("Valor"));
                pedido.setObs(consulta.getString("Obs"));
                pedido.setClassifSrv(consulta.getString("ClassifSrv"));
                pedido.setOperIncl(consulta.getString("OperIncl"));
                pedido.setRotaCanc(consulta.getString("RotaCanc"));
                pedido.setSeqCanc(consulta.getString("SeqCanc"));
                pedido.setDt_Incl(consulta.getString("Dt_Incl"));
                pedido.setHr_Incl(consulta.getString("Hr_Incl"));
                pedido.setChequesQtde(consulta.getInt("ChequesQtde"));
                pedido.setChequesValor(consulta.getString("ChequesValor"));
                pedido.setTicketsQtde(consulta.getString("TicketsQtde"));
                pedido.setTicketsValor(consulta.getString("TicketsValor"));
                pedido.setOperador(consulta.getString("Operador"));
                pedido.setDt_Alter(consulta.getString("Dt_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setOperExcl(consulta.getString("OperExcl"));
                pedido.setDt_Excl(consulta.getString("Dt_Excl"));
                pedido.setHr_Excl(consulta.getString("Hr_Excl"));
                pedido.setSituacao(consulta.getString("Situacao"));
                pedido.setSeqRota(consulta.getString("SeqRota"));
                pedido.setParada(consulta.getInt("Parada"));
                pedido.setHrOper(consulta.getString("HrOper"));
                pedido.setFlag_Excl(consulta.getString("Flag_Excl"));

                pedido.setEnde1(consulta.getString("Ende1"));
                pedido.setBairro1(consulta.getString("Bairro1"));
                pedido.setCidade1(consulta.getString("Cidade1"));
                pedido.setUf1(consulta.getString("UF1"));
                pedido.setEnde2(consulta.getString("Ende2"));
                pedido.setBairro2(consulta.getString("Bairro2"));
                pedido.setCidade2(consulta.getString("Cidade2"));
                pedido.setUf2(consulta.getString("UF2"));

                pedido.setRegiaoDesc1(consulta.getString("Reg1"));
                pedido.setRegiaoDesc2(consulta.getString("Reg2"));

                pedido.setQtdeOrcamento(consulta.getString("Orcamento"));

                retorno.add(pedido);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.listaPaginada - " + e.getMessage());

        }
    }

    public List<Pedido> listapaginadaSemFlag(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Pedido> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY Pedido.Numero desc) AS RowNum, \n"
                    + "Convert(Varchar,Pedido.Data,112) DataF, Pedido.*, \n"
                    + "Reg1.Descricao Reg1,  Reg2.Descricao Reg2, Cli1.Ende Ende1, Cli1.Cidade Cidade1, \n"
                    + "Cli1.Bairro Bairro1, Cli1.Estado UF1, Cli2.Ende Ende2, Cli2.Cidade Cidade2, \n"
                    + "Cli2.Bairro Bairro2, Cli2.Estado UF2, isnull(OS_Vig.GTVQtde,0) Orcamento \n"
                    + "FROM Pedido \n"
                    + "Left join Regiao Reg1  on Reg1.Regiao = Pedido.Regiao1 \n"
                    + "                      and Reg1.CodFil = Pedido.CodFil  \n"
                    + "Left join Regiao Reg2  on Reg2.Regiao = Pedido.Regiao2 \n"
                    + "                      and Reg2.CodFil = Pedido.CodFil  \n"
                    + "Left join Clientes Cli1  on Cli1.Codigo = Pedido.CodCli1 \n"
                    + "                        and Cli1.CodFil = Pedido.CodFil \n"
                    + "Left join Clientes Cli2  on Cli2.Codigo = Pedido.CodCli2 \n"
                    + "                        and Cli2.CodFil = Pedido.CodFil \n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS \n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil \n"
                    + "WHERE Pedido.codfil is not null \n"
                    + "AND Pedido.flag_excl != '*' \n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + "\n";
                }
            }
            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE RowNum >= ? "
                    + "  AND RowNum < ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Pedido pedido;

            while (consulta.Proximo()) {
                pedido = new Pedido();

                pedido.setNumero(consulta.getString("Numero"));
                pedido.setOS(consulta.getString("OS"));
                pedido.setCodFil(consulta.getBigDecimal("CodFil"));
                pedido.setData(consulta.getString("DataF"));

                switch (consulta.getString("Tipo")) {
                    case "T":
                        pedido.setTipo("TRANSPORTE");
                        break;
                    case "C":
                        pedido.setTipo("CANCELAMENTO");
                        break;
                    case "M":
                        pedido.setTipo("MATERIAL");
                        break;
                    case "O":
                        pedido.setTipo("OCORRÊNCIA");
                        break;

                    default:
                        pedido.setTipo(consulta.getString("Tipo"));
                        break;
                }

                pedido.setCodCli1(consulta.getString("CodCli1"));

                if (consulta.getString("CodCli1").equals("9996900")) {
                    pedido.setTipoNoPedido("entrega");
                } else {
                    pedido.setTipoNoPedido("recolhimento");
                }

                pedido.setNRed1(consulta.getString("NRed1"));
                pedido.setRegiao1(consulta.getString("Regiao1"));
                pedido.setHora1O(consulta.getString("Hora1O"));
                pedido.setHora2O(consulta.getString("Hora2O"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setNRed2(consulta.getString("Nred2"));
                pedido.setRegiao2(consulta.getString("Regiao2"));
                pedido.setHora1D(consulta.getString("Hora1D"));
                pedido.setHora2D(consulta.getString("Hora2D"));
                pedido.setCodCli3(consulta.getString("CodCli3"));
                pedido.setSeqSuprim(consulta.getString("SeqSuprim"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setPedidoCliente(consulta.getString("PedidoCliente"));
                pedido.setValor(consulta.getBigDecimal("Valor"));
                pedido.setObs(consulta.getString("Obs"));
                pedido.setClassifSrv(consulta.getString("ClassifSrv"));
                pedido.setOperIncl(consulta.getString("OperIncl"));
                pedido.setRotaCanc(consulta.getString("RotaCanc"));
                pedido.setSeqCanc(consulta.getString("SeqCanc"));
                pedido.setDt_Incl(consulta.getString("Dt_Incl"));
                pedido.setHr_Incl(consulta.getString("Hr_Incl"));
                pedido.setChequesQtde(consulta.getInt("ChequesQtde"));
                pedido.setChequesValor(consulta.getString("ChequesValor"));
                pedido.setTicketsQtde(consulta.getString("TicketsQtde"));
                pedido.setTicketsValor(consulta.getString("TicketsValor"));
                pedido.setOperador(consulta.getString("Operador"));
                pedido.setDt_Alter(consulta.getString("Dt_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setOperExcl(consulta.getString("OperExcl"));
                pedido.setDt_Excl(consulta.getString("Dt_Excl"));
                pedido.setHr_Excl(consulta.getString("Hr_Excl"));
                pedido.setSituacao(consulta.getString("Situacao"));
                pedido.setSeqRota(consulta.getString("SeqRota"));
                pedido.setParada(consulta.getInt("Parada"));
                pedido.setHrOper(consulta.getString("HrOper"));
                pedido.setFlag_Excl(consulta.getString("Flag_Excl"));

                pedido.setEnde1(consulta.getString("Ende1"));
                pedido.setBairro1(consulta.getString("Bairro1"));
                pedido.setCidade1(consulta.getString("Cidade1"));
                pedido.setUf1(consulta.getString("UF1"));
                pedido.setEnde2(consulta.getString("Ende2"));
                pedido.setBairro2(consulta.getString("Bairro2"));
                pedido.setCidade2(consulta.getString("Cidade2"));
                pedido.setUf2(consulta.getString("UF2"));

                pedido.setRegiaoDesc1(consulta.getString("Reg1"));
                pedido.setRegiaoDesc2(consulta.getString("Reg2"));

                pedido.setQtdeOrcamento(consulta.getString("Orcamento"));

                retorno.add(pedido);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.listaPaginada - " + e.getMessage());

        }
    }

    public Integer totalPedidosMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT count(*) total "
                    + "FROM Pedido "
                    + "WHERE Pedido.codfil  is not null \n";
//                    + " WHERE Pedido.codfil in (SELECT filiais.codfil "
//                    + "FROM saspw "
//                    + "left join saspwfil on saspwfil.nome = saspw.nome "
//                    + "left join filiais on filiais.codfil = saspwfil.codfilac "
//                    + "left join paramet on paramet.filial_pdr = filiais.codfil "
//                    + "WHERE saspw.codpessoa = ? and paramet.path = ?) AND ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + " AND " + entrada.getKey() + "\n";
                    }
                }
            }
//            sql = sql + "codFil IS NOT null";
            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
//                consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String query : entries) {
                        consulta.setString(query);
                    }
                }
            }
            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("PedidoDao.totalPedidosMobWeb - " + e.getMessage());
        }
    }

    public Integer totalPedidosMobWebSemFlag(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT count(*) total "
                    + "FROM Pedido "
                    + "WHERE Pedido.codfil  is not null \n"
                    + "AND Pedido.flag_excl != '*' \n";
            Map<String, List> filtro = filtros;
            List<String> entries;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                    entries = entrada.getValue();
                    if (!entries.isEmpty()) {
                        sql = sql + " AND " + entrada.getKey() + "\n";
                    }
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String query : entries) {
                        consulta.setString(query);
                    }
                }
            }
            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("PedidoDao.totalPedidosMobWeb - " + e.getMessage());
        }
    }

    public List<Pedido> listaPaginada(int primeiro, int linhas, Map filtros, List<CxForte> cxForteList, Persistencia persistencia) throws Exception {
        List<Pedido> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY Pedido.Numero) AS RowNum, \n"
                    + "Convert(Varchar,Pedido.Data,112) DataF, Pedido.*, \n"
                    + "Reg1.Descricao Reg1,  Reg2.Descricao Reg2, Cli1.Ende Ende1, Cli1.Cidade Cidade1, \n"
                    + "Cli1.Bairro Bairro1, Cli1.CEP CEP1, Cli1.Estado UF1, Cli2.Ende Ende2, Cli2.Cidade Cidade2, \n"
                    + "Cli2.Bairro Bairro2, Cli2.Estado UF2, Cli2.CEP CEP2, isnull(OS_Vig.GTVQtde,0) Orcamento,"
                    + "(SELECT Min(Guia) FROM TesSaidas TS1 WHERE TS1.Pedido = Pedido.Numero and TS1.CodCli2 = Pedido.CodCli2 and TS1.CodFil = Pedido.CodFil) Guia \n"
                    //                    + " PedidoDN.Numero NumeroDN, \n"
                    //                    + " PedidoDN.CodFil CodFilDN, \n"
                    //                    + " PedidoDN.Codigo CodigoDN, \n"
                    //                    + " PedidoDN.Docto DoctoDN, \n"
                    //                    + " PedidoDN.Qtde QtdeDN, \n"
                    //                    + " PedidoMD.Numero NumeroMD, \n"
                    //                    + " PedidoMD.CodFil CodFilMD, \n"
                    //                    + " PedidoMD.Codigo CodigoMD, \n"
                    //                    + " PedidoMD.Docto DoctoMD, \n"
                    //                    + " PedidoMD.Qtde QtdeMD \n"
                    + "FROM Pedido \n"
                    + "Left join Regiao Reg1  on Reg1.Regiao = Pedido.Regiao1 \n"
                    + "                      and Reg1.CodFil = Pedido.CodFil  \n"
                    + "Left join Regiao Reg2  on Reg2.Regiao = Pedido.Regiao2 \n"
                    + "                      and Reg2.CodFil = Pedido.CodFil  \n"
                    + "Left join Clientes Cli1  on Cli1.Codigo = Pedido.CodCli1 \n"
                    + "                        and Cli1.CodFil = Pedido.CodFil \n"
                    + "Left join Clientes Cli2  on Cli2.Codigo = Pedido.CodCli2 \n"
                    + "                        and Cli2.CodFil = Pedido.CodFil \n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS \n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil \n"
                    //                    + "Left join PedidoDN  on Pedido.Numero = PedidoDN.Numero \n"
                    //                    + "                   and Pedido.CodFil = PedidoDN.CodFil \n"
                    //                    + "Left join PedidoMD  on Pedido.Numero = PedidoMD.Numero \n"
                    //                    + "                   and Pedido.CodFil = PedidoMD.CodFil \n"
                    + " WHERE Pedido.codfil is not null \n";

            Map<String, String> filtro = filtros;
            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("")) {
                sql += " and"
                        + "(OS_Vig.Cliente in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil))";
            } else if (!filtro.get("CodCli").equals("")) {
                sql += " and  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) \n";
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE RowNum >= ? "
                    + "  AND RowNum < ? ";

            Consulta consulta = new Consulta(sql, persistencia);

            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else if (!filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Pedido pedido;
//            String Numero = "";
//            PedidoDN composicao = new PedidoDN();
//            List<PedidoDN> listaComposicao = new ArrayList<>();

            while (consulta.Proximo()) {
                pedido = new Pedido();
//                composicao = new PedidoDN();
//
//                if (!consulta.getString("QtdeDN").equals("")
//                        && !consulta.getString("CodigoDN").equals("")) {
//                    composicao.setCodFil(consulta.getString("CodFilDN"));
//                    composicao.setCodigo(consulta.getString("CodigoDN"));
//                    composicao.setDocto(consulta.getString("DoctoDN"));
//                    composicao.setNumero(consulta.getString("NumeroDN"));
//                    composicao.setQtde(consulta.getString("QtdeDN"));
//                    composicao.setTipo("C");
//                    composicao.setValor(Double.toString(new Float(consulta.getString("QtdeDN")) * new Float(consulta.getString("CodigoDN"))));
//                    listaComposicao.add(composicao);
//                }
//
//                if (!consulta.getString("QtdeMD").equals("")
//                        && !consulta.getString("CodigoMD").equals("")) {
//                    composicao = new PedidoDN();
//                    composicao.setCodFil(consulta.getString("CodFilMD"));
//                    composicao.setCodigo(consulta.getString("CodigoMD"));
//                    composicao.setDocto(consulta.getString("DoctoMD"));
//                    composicao.setNumero(consulta.getString("NumeroMD"));
//                    composicao.setQtde(consulta.getString("QtdeMD"));
//                    composicao.setTipo("M");
//                    composicao.setValor(Double.toString(new Float(consulta.getString("QtdeMD")) * new Float(consulta.getString("CodigoMD"))));
//                    listaComposicao.add(composicao);
//                }
//
//                if (!Numero.equals(consulta.getString("Numero"))) {
//                    Numero = consulta.getString("Numero");
//
//                    if (retorno.size() > 0) {
//                        retorno.get(retorno.size() - 1).setListaPedidosComposicao(listaComposicao);
//                        listaComposicao = new ArrayList<>();
//                    }

                pedido.setNumero(consulta.getString("Numero"));
                pedido.setOS(consulta.getString("OS").replace(".0", ""));
                pedido.setCodFil(consulta.getBigDecimal("CodFil"));
                pedido.setData(consulta.getString("DataF"));
                pedido.setTipo(consulta.getString("Tipo"));
                pedido.setCodCli1(consulta.getString("CodCli1"));
                pedido.setNRed1(consulta.getString("NRed1"));
                pedido.setRegiao1(consulta.getString("Regiao1"));
                pedido.setHora1O(consulta.getString("Hora1O"));
                pedido.setHora2O(consulta.getString("Hora2O"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setNRed2(consulta.getString("Nred2"));
                pedido.setRegiao2(consulta.getString("Regiao2"));
                pedido.setHora1D(consulta.getString("Hora1D"));
                pedido.setHora2D(consulta.getString("Hora2D"));
                pedido.setCodCli3(consulta.getString("CodCli3"));
                pedido.setSeqSuprim(consulta.getString("SeqSuprim"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setPedidoCliente(consulta.getString("PedidoCliente"));
                pedido.setValor(consulta.getBigDecimal("Valor"));
                pedido.setObs(consulta.getString("Obs"));
                pedido.setClassifSrv(consulta.getString("ClassifSrv"));
                pedido.setOperIncl(consulta.getString("OperIncl"));
                pedido.setRotaCanc(consulta.getString("RotaCanc"));
                pedido.setSeqCanc(consulta.getString("SeqCanc"));
                pedido.setDt_Incl(consulta.getString("Dt_Incl"));
                pedido.setHr_Incl(consulta.getString("Hr_Incl"));
                pedido.setChequesQtde(consulta.getInt("ChequesQtde"));
                pedido.setChequesValor(consulta.getString("ChequesValor"));
                pedido.setTicketsQtde(consulta.getString("TicketsQtde"));
                pedido.setTicketsValor(consulta.getString("TicketsValor"));
                pedido.setOperador(consulta.getString("Operador"));
                pedido.setDt_Alter(consulta.getString("Dt_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setOperExcl(consulta.getString("OperExcl"));
                pedido.setDt_Excl(consulta.getString("Dt_Excl"));
                pedido.setHr_Excl(consulta.getString("Hr_Excl"));
                pedido.setSituacao(consulta.getString("Situacao"));
                pedido.setTipoMoeda(consulta.getString("TipoMoeda"));
                pedido.setSeqRota(consulta.getString("SeqRota"));
                pedido.setParada(consulta.getInt("Parada"));
                pedido.setHrOper(consulta.getString("HrOper"));
                pedido.setFlag_Excl(consulta.getString("Flag_Excl"));

                pedido.setEnde1(consulta.getString("Ende1"));
                pedido.setBairro1(consulta.getString("Bairro1"));
                pedido.setCidade1(consulta.getString("Cidade1"));
                pedido.setUf1(consulta.getString("UF1"));
                pedido.setCli1CEP(consulta.getString("CEP1"));

                pedido.setEnde2(consulta.getString("Ende2"));
                pedido.setBairro2(consulta.getString("Bairro2"));
                pedido.setCidade2(consulta.getString("Cidade2"));
                pedido.setUf2(consulta.getString("UF2"));
                pedido.setCli2CEP(consulta.getString("CEP2"));

                pedido.setRegiaoDesc1(consulta.getString("Reg1"));
                pedido.setRegiaoDesc2(consulta.getString("Reg2"));

                pedido.setQtdeOrcamento(consulta.getString("Orcamento"));

                pedido.setAzul(false);
//                System.out.println(pedido.getCodCli1()+" "+pedido.getCodCli1().charAt(3)+" "+pedido.getTipo()+" ("+consulta.getString("Guia")+") "+pedido.getClassifSrv());
                for (CxForte cxForte : cxForteList) {
                    if ((cxForte.getCodCli().equals(pedido.getCodCli1()) || pedido.getCodCli1().charAt(3) == '7')
                            && pedido.getTipo().equals("T")
                            && consulta.getString("Guia").equals("")
                            && !pedido.getClassifSrv().equals("E")) {
                        pedido.setAzul(true);
                        break;
                    }
                }

                retorno.add(pedido);
//                }
            }
            consulta.Close();

//            if (retorno.size() > 0) {
//                retorno.get(retorno.size() - 1).setListaPedidosComposicao(listaComposicao);
//            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<Pedido> listaPaginada(int primeiro, int linhas, Map filtros, List<CxForte> cxForteList, Boolean acessoAdm, Persistencia persistencia) throws Exception {
        List<Pedido> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY Pedido.Numero) AS RowNum, \n"
                    + "Convert(Varchar,Pedido.Data,112) DataF, Pedido.*, \n"
                    + "Reg1.Descricao Reg1,  Reg2.Descricao Reg2, Cli1.Ende Ende1, Cli1.Cidade Cidade1, \n"
                    + "Cli1.Bairro Bairro1, Cli1.CEP CEP1, Cli1.Estado UF1, Cli2.Ende Ende2, Cli2.Cidade Cidade2, \n"
                    + "Cli2.Bairro Bairro2, Cli2.Estado UF2, Cli2.CEP CEP2, isnull(OS_Vig.GTVQtde,0) Orcamento,"
                    + "(SELECT Min(Guia) FROM TesSaidas TS1 WHERE TS1.Pedido = Pedido.Numero and TS1.CodCli2 = Pedido.CodCli2 and TS1.CodFil = Pedido.CodFil) Guia \n"
                    //                    + " PedidoDN.Numero NumeroDN, \n"
                    //                    + " PedidoDN.CodFil CodFilDN, \n"
                    //                    + " PedidoDN.Codigo CodigoDN, \n"
                    //                    + " PedidoDN.Docto DoctoDN, \n"
                    //                    + " PedidoDN.Qtde QtdeDN, \n"
                    //                    + " PedidoMD.Numero NumeroMD, \n"
                    //                    + " PedidoMD.CodFil CodFilMD, \n"
                    //                    + " PedidoMD.Codigo CodigoMD, \n"
                    //                    + " PedidoMD.Docto DoctoMD, \n"
                    //                    + " PedidoMD.Qtde QtdeMD \n"
                    + "FROM Pedido \n"
                    + "Left join Regiao Reg1  on Reg1.Regiao = Pedido.Regiao1 \n"
                    + "                      and Reg1.CodFil = Pedido.CodFil  \n"
                    + "Left join Regiao Reg2  on Reg2.Regiao = Pedido.Regiao2 \n"
                    + "                      and Reg2.CodFil = Pedido.CodFil  \n"
                    + "Left join Clientes Cli1  on Cli1.Codigo = Pedido.CodCli1 \n"
                    + "                        and Cli1.CodFil = Pedido.CodFil \n"
                    + "Left join Clientes Cli2  on Cli2.Codigo = Pedido.CodCli2 \n"
                    + "                        and Cli2.CodFil = Pedido.CodFil \n"
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS \n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil \n"
                    //                    + "Left join PedidoDN  on Pedido.Numero = PedidoDN.Numero \n"
                    //                    + "                   and Pedido.CodFil = PedidoDN.CodFil \n"
                    //                    + "Left join PedidoMD  on Pedido.Numero = PedidoMD.Numero \n"
                    //                    + "                   and Pedido.CodFil = PedidoMD.CodFil \n"
                    + " WHERE Pedido.codfil is not null \n";

            Map<String, String> filtro = filtros;
            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("") && !acessoAdm) {
                sql += " and"
                        + "(OS_Vig.Cliente in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil))";
            } else if (!filtro.get("CodCli").equals("")) {
                sql += " and  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) \n";
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE RowNum >= ? "
                    + "  AND RowNum < ? ";

            Consulta consulta = new Consulta(sql, persistencia);

            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("") && !acessoAdm) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else if (!filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Pedido pedido;
//            String Numero = "";
//            PedidoDN composicao = new PedidoDN();
//            List<PedidoDN> listaComposicao = new ArrayList<>();

            while (consulta.Proximo()) {
                pedido = new Pedido();
//                composicao = new PedidoDN();
//
//                if (!consulta.getString("QtdeDN").equals("")
//                        && !consulta.getString("CodigoDN").equals("")) {
//                    composicao.setCodFil(consulta.getString("CodFilDN"));
//                    composicao.setCodigo(consulta.getString("CodigoDN"));
//                    composicao.setDocto(consulta.getString("DoctoDN"));
//                    composicao.setNumero(consulta.getString("NumeroDN"));
//                    composicao.setQtde(consulta.getString("QtdeDN"));
//                    composicao.setTipo("C");
//                    composicao.setValor(Double.toString(new Float(consulta.getString("QtdeDN")) * new Float(consulta.getString("CodigoDN"))));
//                    listaComposicao.add(composicao);
//                }
//
//                if (!consulta.getString("QtdeMD").equals("")
//                        && !consulta.getString("CodigoMD").equals("")) {
//                    composicao = new PedidoDN();
//                    composicao.setCodFil(consulta.getString("CodFilMD"));
//                    composicao.setCodigo(consulta.getString("CodigoMD"));
//                    composicao.setDocto(consulta.getString("DoctoMD"));
//                    composicao.setNumero(consulta.getString("NumeroMD"));
//                    composicao.setQtde(consulta.getString("QtdeMD"));
//                    composicao.setTipo("M");
//                    composicao.setValor(Double.toString(new Float(consulta.getString("QtdeMD")) * new Float(consulta.getString("CodigoMD"))));
//                    listaComposicao.add(composicao);
//                }
//
//                if (!Numero.equals(consulta.getString("Numero"))) {
//                    Numero = consulta.getString("Numero");
//
//                    if (retorno.size() > 0) {
//                        retorno.get(retorno.size() - 1).setListaPedidosComposicao(listaComposicao);
//                        listaComposicao = new ArrayList<>();
//                    }

                pedido.setNumero(consulta.getString("Numero"));
                pedido.setOS(consulta.getString("OS").replace(".0", ""));
                pedido.setCodFil(consulta.getBigDecimal("CodFil"));
                pedido.setData(consulta.getString("DataF"));
                pedido.setTipo(consulta.getString("Tipo"));
                pedido.setCodCli1(consulta.getString("CodCli1"));
                pedido.setNRed1(consulta.getString("NRed1"));
                pedido.setRegiao1(consulta.getString("Regiao1"));
                pedido.setHora1O(consulta.getString("Hora1O"));
                pedido.setHora2O(consulta.getString("Hora2O"));
                pedido.setCodCli2(consulta.getString("CodCli2"));
                pedido.setNRed2(consulta.getString("Nred2"));
                pedido.setRegiao2(consulta.getString("Regiao2"));
                pedido.setHora1D(consulta.getString("Hora1D"));
                pedido.setHora2D(consulta.getString("Hora2D"));
                pedido.setCodCli3(consulta.getString("CodCli3"));
                pedido.setSeqSuprim(consulta.getString("SeqSuprim"));
                pedido.setSolicitante(consulta.getString("Solicitante"));
                pedido.setPedidoCliente(consulta.getString("PedidoCliente"));
                pedido.setValor(consulta.getBigDecimal("Valor"));
                pedido.setObs(consulta.getString("Obs"));
                pedido.setClassifSrv(consulta.getString("ClassifSrv"));
                pedido.setOperIncl(consulta.getString("OperIncl"));
                pedido.setRotaCanc(consulta.getString("RotaCanc"));
                pedido.setSeqCanc(consulta.getString("SeqCanc"));
                pedido.setDt_Incl(consulta.getString("Dt_Incl"));
                pedido.setHr_Incl(consulta.getString("Hr_Incl"));
                pedido.setChequesQtde(consulta.getInt("ChequesQtde"));
                pedido.setChequesValor(consulta.getString("ChequesValor"));
                pedido.setTicketsQtde(consulta.getString("TicketsQtde"));
                pedido.setTicketsValor(consulta.getString("TicketsValor"));
                pedido.setOperador(consulta.getString("Operador"));
                pedido.setDt_Alter(consulta.getString("Dt_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setHr_Alter(consulta.getString("Hr_Alter"));
                pedido.setOperExcl(consulta.getString("OperExcl"));
                pedido.setDt_Excl(consulta.getString("Dt_Excl"));
                pedido.setHr_Excl(consulta.getString("Hr_Excl"));
                pedido.setSituacao(consulta.getString("Situacao"));
                pedido.setTipoMoeda(consulta.getString("TipoMoeda"));
                pedido.setSeqRota(consulta.getString("SeqRota"));
                pedido.setParada(consulta.getInt("Parada"));
                pedido.setHrOper(consulta.getString("HrOper"));
                pedido.setFlag_Excl(consulta.getString("Flag_Excl"));

                pedido.setEnde1(consulta.getString("Ende1"));
                pedido.setBairro1(consulta.getString("Bairro1"));
                pedido.setCidade1(consulta.getString("Cidade1"));
                pedido.setUf1(consulta.getString("UF1"));
                pedido.setCli1CEP(consulta.getString("CEP1"));

                pedido.setEnde2(consulta.getString("Ende2"));
                pedido.setBairro2(consulta.getString("Bairro2"));
                pedido.setCidade2(consulta.getString("Cidade2"));
                pedido.setUf2(consulta.getString("UF2"));
                pedido.setCli2CEP(consulta.getString("CEP2"));

                pedido.setRegiaoDesc1(consulta.getString("Reg1"));
                pedido.setRegiaoDesc2(consulta.getString("Reg2"));

                pedido.setQtdeOrcamento(consulta.getString("Orcamento"));

                pedido.setAzul(false);
//                System.out.println(pedido.getCodCli1()+" "+pedido.getCodCli1().charAt(3)+" "+pedido.getTipo()+" ("+consulta.getString("Guia")+") "+pedido.getClassifSrv());
                for (CxForte cxForte : cxForteList) {
                    if ((cxForte.getCodCli().equals(pedido.getCodCli1()) || pedido.getCodCli1().charAt(3) == '7')
                            && pedido.getTipo().equals("T")
                            && consulta.getString("Guia").equals("")
                            && !pedido.getClassifSrv().equals("E")) {
                        pedido.setAzul(true);
                        break;
                    }
                }

                // Consultar Guia
                GTVDao gtvDao = new GTVDao();

                pedido.setGuiasEnt("");
                pedido.setGuiasRec("");
                String GuiaTratada = "";

                if (pedido.getValor() == null) {
                    pedido.setValor(BigDecimal.ZERO);
                }

                if (pedido.getValorEnt() == null || pedido.getValorEnt().equals("")) {
                    pedido.setValorEnt("0");
                }

                if (pedido.getValorRec() == null || pedido.getValorRec().equals("")) {
                    pedido.setValorRec("0");
                }

                for (GTVPedidoOSClienteTesSaida item : gtvDao.getGuiasByPedido(pedido.getCodFil().toPlainString().replace(".0", ""), pedido.getNumero().toPlainString().replace(".0", ""), persistencia)) {
                    GuiaTratada = "";

                    if (!pedido.getGuiasEnt().equals("")) {
                        pedido.setGuiasEnt(pedido.getGuiasEnt() + ", ");
                    }

                    for (int i = item.getGtv().getGuia().toPlainString().replace(".0", "").length(); i < 7; i++) {
                        GuiaTratada += "0";
                    }

                    GuiaTratada += item.getGtv().getGuia().toPlainString().replace(".0", "");
                    pedido.setGuiasEnt(pedido.getGuiasEnt() + GuiaTratada + "-" + item.getGtv().getSerie());
                }

                retorno.add(pedido);
//                }
            }
            consulta.Close();

//            if (retorno.size() > 0) {
//                retorno.get(retorno.size() - 1).setListaPedidosComposicao(listaComposicao);
//            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoes(String numero, String codFil, Persistencia persistencia) throws Exception {
        List<PedidoDN> retorno = new ArrayList<>();
        try {
            String sql = "SELECT * FROM \n"
                    + "\n"
                    + "(SELECT PedidoDN.Numero Numero, \n"
                    + "    PedidoDN.CodFil CodFil, \n"
                    + "    PedidoDN.Codigo Codigo, \n"
                    + "    PedidoDN.Docto Docto, \n"
                    + "    PedidoDN.Qtde Qtde,\n"
                    + "    PedidoDN.Codigo * PedidoDN.Qtde Valor,\n"
                    + "    'C' Tipo \n"
                    + "FROM PedidoDN \n"
                    + "\n"
                    + "UNION  \n"
                    + "\n"
                    + "SELECT PedidoMD.Numero Numero, \n"
                    + "    PedidoMD.CodFil CodFil, \n"
                    + "    Convert(Float,PedidoMD.Codigo)/100 Codigo, \n"
                    + "    PedidoMD.Docto Docto, \n"
                    + "    PedidoMD.Qtde Qtde,\n"
                    + "    (Convert(Float,PedidoMD.Codigo)/100) * PedidoMD.Qtde Valor,\n"
                    + "    'M' Tipo \n"
                    + "FROM PedidoMD\n"
                    + ") a \n"
                    + "WHERE CodFil = ? AND Numero = ? \n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);
            consulta.setString(numero);
            consulta.select();

            PedidoDN pedidoDN;
            while (consulta.Proximo()) {
                pedidoDN = new PedidoDN();

                pedidoDN.setCodFil(consulta.getString("CodFil"));
                pedidoDN.setCodigo(consulta.getString("Codigo"));
                pedidoDN.setDocto(consulta.getString("Docto"));
                pedidoDN.setNumero(consulta.getString("Numero"));
                pedidoDN.setQtde(consulta.getString("Qtde"));
                pedidoDN.setTipo(consulta.getString("Tipo"));
                pedidoDN.setValor(consulta.getString("Valor"));

                retorno.add(pedidoDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.buscarComposicoes - " + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoesDN(String numero, String codFil, Persistencia persistencia) throws Exception {
        List<PedidoDN> retorno = new ArrayList<>();
        try {
            String sql = "SELECT PedidoDN.Numero Numero, \n"
                    + "    PedidoDN.CodFil CodFil, \n"
                    + "    PedidoDN.Codigo Codigo, \n"
                    + "    PedidoDN.Docto Docto, \n"
                    + "    PedidoDN.Qtde Qtde,\n"
                    + "    PedidoDN.Codigo * PedidoDN.Qtde Valor,\n"
                    + "    'C' Tipo \n"
                    + "FROM PedidoDN \n"
                    + "WHERE CodFil = ? AND Numero = ? \n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);
            consulta.setString(numero);
            consulta.select();

            PedidoDN pedidoDN;
            while (consulta.Proximo()) {
                pedidoDN = new PedidoDN();

                pedidoDN.setCodFil(consulta.getString("CodFil"));
                pedidoDN.setCodigo(consulta.getString("Codigo").replace(".0", ""));
                pedidoDN.setDocto(consulta.getString("Docto"));
                pedidoDN.setNumero(consulta.getString("Numero"));
                pedidoDN.setQtde(consulta.getString("Qtde"));
                pedidoDN.setTipo(consulta.getString("Tipo"));
                pedidoDN.setValor(consulta.getString("Valor"));

                retorno.add(pedidoDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.buscarComposicoes - " + e.getMessage());
        }
    }

    public List<PedidoDN> buscarComposicoesMD(String numero, String codFil, Persistencia persistencia) throws Exception {
        List<PedidoDN> retorno = new ArrayList<>();
        try {
            String sql = "SELECT PedidoMD.Numero Numero, \n"
                    + "    PedidoMD.CodFil CodFil, \n"
                    + "    Convert(Float,PedidoMD.Codigo)/100 Codigo, \n"
                    + "    PedidoMD.Docto Docto, \n"
                    + "    PedidoMD.Qtde Qtde,\n"
                    + "    (Convert(Float,PedidoMD.Codigo)/100) * PedidoMD.Qtde Valor,\n"
                    + "    'M' Tipo \n"
                    + "FROM PedidoMD\n"
                    + "WHERE CodFil = ? AND Numero = ? \n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);
            consulta.setString(numero);
            consulta.select();

            PedidoDN pedidoDN;
            while (consulta.Proximo()) {
                pedidoDN = new PedidoDN();

                pedidoDN.setCodFil(consulta.getString("CodFil"));
                pedidoDN.setCodigo(consulta.getString("Codigo"));
                pedidoDN.setDocto(consulta.getString("Docto"));
                pedidoDN.setNumero(consulta.getString("Numero"));
                pedidoDN.setQtde(consulta.getString("Qtde"));
                pedidoDN.setTipo(consulta.getString("Tipo"));
                pedidoDN.setValor(consulta.getString("Valor"));

                retorno.add(pedidoDN);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PedidoDao.buscarComposicoes - " + e.getMessage());
        }
    }

    public Integer totalPedidos(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT count(*) total "
                    + "FROM Pedido "
                    + "Left join OS_Vig  on OS_Vig.OS = Pedido.OS \n"
                    + "                  and OS_Vig.CodFil = Pedido.CodFil \n"
                    + "WHERE Pedido.codfil  is not null \n";

            Map<String, String> filtro = filtros;
            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("")) {
                sql += " and"
                        + "(OS_Vig.Cliente in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (SELECT CodCli FROM PessoaCliAut WHERE PessoaCliAut.Codigo = ? and PessoaCliAut.CodFil = Pedido.CodFil))";
            } else if (!filtro.get("CodCli").equals("")) {
                sql += " and  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) \n";
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            if (filtro.get("CodCli").equals("") && !filtro.get("CodPessoa").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else if (!filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int retorno = 0;
            if (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("PedidoDao.totalPedidos - " + e.getMessage());
        }
    }
}

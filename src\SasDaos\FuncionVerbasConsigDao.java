package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FuncionVerbasConsig;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FuncionVerbasConsigDao {
    String sql;
    
    public List<FuncionVerbasConsig> getFPMensal(String sCodFil, String sMatr, Persistencia persistencia) throws Exception {

        List<FuncionVerbasConsig> lFuncionVerbasConsig = new ArrayList();
        sql = "Select"
                + "    FUNCIONVERBASCONSIG.CodFil, "
                + "    FUNCIONVERBASCONSIG.Matr, "
                + "    FUNCIONVERBASCONSIG.Verba, "
                + "    FUNCIONVERBASCONSIG.DtValidade, "
                + "    FUNCIONVERBASCONSIG.Banco, "
                + "    FUNCIONVERBASCONSIG.Contrato, "
                + "    FUNCIONVERBASCONSIG.Obs, "
                + "    FUNCIONVERBASCONSIG.Operador "
                + "From "
                + "    FuncionverbasConsig "
                + "Where "
                + "    FUNCIONVERBASCONSIG.CodFil = ? And "
                + "    FUNCIONVERBASCONSIG.Matr = ?";

        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodFil);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                FuncionVerbasConsig oFuncionVerbasConsig = new FuncionVerbasConsig();
                oFuncionVerbasConsig.setCodFil(stm.getBigDecimal("CodFil"));
                oFuncionVerbasConsig.setMatr(stm.getBigDecimal("Matr"));
                oFuncionVerbasConsig.setVerba(stm.getString("Verba"));
                oFuncionVerbasConsig.setDtValidade(stm.getLocalDate("DtValidade"));
                oFuncionVerbasConsig.setBanco(stm.getString("Banco"));
                oFuncionVerbasConsig.setContrato(stm.getString("Contrato"));
                oFuncionVerbasConsig.setObs(stm.getString("Obs"));
                oFuncionVerbasConsig.setOperador(stm.getString("Operador"));
                lFuncionVerbasConsig.add(oFuncionVerbasConsig);

            }
            stm.Close();
            return lFuncionVerbasConsig;
        } catch (Exception e) {
            throw new Exception("FPMensalDao.getFPMensal  - " + e.getMessage() + "\r\n"
                + "Select"
                + "    FUNCIONVERBASCONSIG.CodFil, "
                + "    FUNCIONVERBASCONSIG.Matr, "
                + "    FUNCIONVERBASCONSIG.Verba, "
                + "    FUNCIONVERBASCONSIG.DtValidade, "
                + "    FUNCIONVERBASCONSIG.Banco, "
                + "    FUNCIONVERBASCONSIG.Contrato, "
                + "    FUNCIONVERBASCONSIG.Obs, "
                + "    FUNCIONVERBASCONSIG.Operador, "
                + "    FUNCIONVERBASCONSIG.Dt_Alter, "
                + "    FUNCIONVERBASCONSIG.Hr_Alter "
                + "From "
                + "    FuncionverbasConsig "
                + "Where "
                + "    FUNCIONVERBASCONSIG.CodFil = " + sCodFil + " And " 
                + "    FUNCIONVERBASCONSIG.Matr = "  + sMatr);
        }

    }

    
}

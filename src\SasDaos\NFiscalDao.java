/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CCusto;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.NFItens;
import SasBeans.NFiscal;
import SasBeans.Produtos;
import SasBeansCompostas.RegistroSaidaNotaFiscal;
import SasBeansCompostas.NFiscalCliente;
import SasBeansCompostas.BoletoCliente;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class NFiscalDao {

    /**
     * Atualiza as informações da nota
     *
     * @param NFeRetorno
     * @param Dt_Audit
     * @param Hr_Audit
     * @param Numero
     * @param Praca
     * @param persistencia
     * @throws Exception
     */
    public void atualizarNFiscal(String NFeRetorno, String Dt_Audit, String Hr_Audit, String Numero, String Praca, Persistencia persistencia)
            throws Exception {
        try {
            String sql = " Update NFiscal Set NFeRetorno = ?, Dt_Audit = ?, Hr_Audit = ? "
                    + " FROM NFiscal "
                    + " WHERE Numero = ? and Praca = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(NFeRetorno);
            consulta.setString(Dt_Audit);
            consulta.setString(Hr_Audit);
            consulta.setString(Numero);
            consulta.setString(Praca);
            consulta.update();
        } catch (Exception e) {
            throw new Exception("NFiscalDao.atualizarNFiscal - " + e.getMessage() + "\r\n"
                    + " Update NFiscal Set NFeRetorno = " + NFeRetorno + ", Dt_Audit = " + Dt_Audit + ", Hr_Audit = " + Hr_Audit
                    + " FROM NFiscal "
                    + " WHERE Numero = " + Numero + " and Praca = " + Praca);
        }
    }

    public void atualizarNFiscal(String NFeRetorno, String Dt_Audit, String Hr_Audit, String NFeChave, String Numero, String Praca, Persistencia persistencia)
            throws Exception {
        try {
            String sql = " Update NFiscal Set NFeRetorno = ?, Dt_Audit = ?, Hr_Audit = ?, NFeChave = ? "
                    + " FROM NFiscal "
                    + " WHERE Numero = ? and Praca = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(NFeRetorno);
            consulta.setString(Dt_Audit);
            consulta.setString(Hr_Audit);
            consulta.setString(NFeChave);
            consulta.setString(Numero);
            consulta.setString(Praca);
            consulta.update();
        } catch (Exception e) {
            throw new Exception("NFiscalDao.atualizarNFiscal - " + e.getMessage() + "\r\n"
                    + " Update NFiscal Set NFeRetorno = " + NFeRetorno + ", Dt_Audit = " + Dt_Audit + ", Hr_Audit = " + Hr_Audit
                    + " FROM NFiscal "
                    + " WHERE Numero = " + Numero + " and Praca = " + Praca);
        }
    }

    public List<RegistroSaidaNotaFiscal> listaFiscal(String dataInicio, String dataFim, String codFilInicio, String codFilFim, Persistencia persistencia) throws Exception {
        try {
            List<RegistroSaidaNotaFiscal> retorno = new ArrayList<>();
            String sql = "Select 'SAIDA' Tipo, NFiscal.NFEChave, NFiscal.CodFil,CCusto.CNPJ, CCusto.CCusto, CCusto.Descricao CCustoDesc, NFiscal.Numero OCompra,\n"
                    + "NFiscal.Numero NF,NFiscal.Serie, NFiscal.Data DtEmissao,Filiais.InscEst, Clientes.Nome Fornecedor, Clientes.Estado UF,  NFiscal.CFOP, Produtos.UN,  Produtos.Descricao,\n"
                    + "Case when NFiscal.ISS > 0 then 'ISS' else 'ICMS' end DescTribut,  NFiscal.AliqISS+NFiscal.AliqICMS Indice, 0 ProdSeq, NFItens.CodPro CodProd,  \n"
                    + "NFItens.Qtde, NFItens.Valor_Tot Valor,Produtos.ICMSID, \n"
                    + "NFiscal.ISS+NFiscal.ICMS Imposto, NFiscal.ICMS CredICMS, '999' IPIEnq, 0 IPI, 0 AliqIPI, '01' AS PISCST, \n"
                    + "NFiscal.AliqPIS,NFiscal.PIS, '01' AS COFINSCST, NFiscal.AliqCOFINS,NFiscal.COFINS,   '01' AS CST, 0 BCICMS\n"
                    + "from NFiscal  \n"
                    + "left join CCusto on CCusto.CCusto = NFiscal.CCusto  \n"
                    + "left join Clientes  on Clientes.Codigo = NFiscal.CliFat\n"
                    + "left join Filiais on Filiais.CodFil = NFiscal.CodFil  \n"
                    + "Left Join NFItens on  NFItens.Numero = NFiscal.Numero\n"
                    + "                 and NFItens.Praca  = NFiscal.Praca                    \n"
                    + "				and NFiscal.Serie  = NFiscal.Serie  \n"
                    + "Left Join Produtos on Produtos.Codigo = NFItens.CodPro  \n"
                    + "where NFiscal.Data >= ?  \n"
                    + "  and NFiscal.Data <= ?   \n"
                    + "  and NFiscal.CodFil >= ? \n"
                    + "  and NFiscal.CodFil <= ? \n"
                    + "  and NFiscal.Situacao = 'A'    \n"
                    + "Order by Tipo, NF, CFOP, UF ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFilInicio);
            consulta.setString(codFilFim);
            consulta.select();

            RegistroSaidaNotaFiscal regSaida;
            NFiscal nfiscal;
            CCusto ccusto;
            Clientes clientes;
            NFItens nfitens;
            Produtos produtos;
            Filiais filiais;

            while (consulta.Proximo()) {
                regSaida = new RegistroSaidaNotaFiscal();
                nfiscal = new NFiscal();
                ccusto = new CCusto();
                clientes = new Clientes();
                nfitens = new NFItens();
                produtos = new Produtos();
                filiais = new Filiais();

                nfiscal.setNFEChave(consulta.getString("NFEChave"));
                nfiscal.setCodFil(consulta.getString("CodFil"));
                nfiscal.setNumero(consulta.getString("OCompra"));
                nfiscal.setData(consulta.getLocalDate("DtEmissao"));
                nfiscal.setCFOP(consulta.getString("CFOP"));
                nfiscal.setISS(consulta.getString("DescTribut"));
                nfiscal.setICMS(consulta.getString("CredICMS"));
                nfiscal.setAliqISS(consulta.getString("Indice"));
                nfiscal.setAliqICMS(consulta.getString("Indice"));
                nfiscal.setAliqPIS(consulta.getString("AliqPIS"));
                nfiscal.setPIS(consulta.getString("PIS"));
                nfiscal.setAliqCOFINS(consulta.getString("AliqCOFINS"));
                nfiscal.setCOFINS(consulta.getString("COFINS"));
                nfiscal.setSerie(consulta.getString("Serie"));

                ccusto.setCCusto(consulta.getString("CCustoDesc"));
                ccusto.setDescricao(consulta.getString("Descricao"));
                ccusto.setCNPJ(consulta.getString("CNPJ"));

                filiais.setInscEst(consulta.getString("InscEst"));

                clientes.setNome(consulta.getString("Fornecedor"));
                clientes.setEstado(consulta.getString("UF"));

                nfitens.setQtde(consulta.getBigDecimal("Qtde"));
                nfitens.setCodPro(consulta.getBigDecimal("CodProd"));
                nfitens.setValor_Tot(consulta.getBigDecimal("Valor"));

                produtos.setUn(consulta.getString("UN"));
                produtos.setDescricao(consulta.getString("Descricao"));
                produtos.setICMSID(consulta.getString("ICMSID"));

                regSaida.setNfiscal(nfiscal);
                regSaida.setCcusto(ccusto);
                regSaida.setClientes(clientes);
                regSaida.setNfitens(nfitens);
                regSaida.setProdutos(produtos);
                regSaida.setFiliais(filiais);

                retorno.add(regSaida);
            }
            consulta.Close();
            return retorno;
        } catch (Exception ex) {
            throw new Exception("NFiscalDao.listaFiscal - " + ex.getMessage() + "\r\n"
                    + "Select 'SAIDA' Tipo, NFiscal.NFEChave, NFiscal.CodFil, CCusto.CCusto, CCusto.Descricao CCustoDesc, NFiscal.Numero OCompra,\n"
                    + "NFiscal.Numero NF, NFiscal.Data DtEmissao, Clientes.Nome Fornecedor, Clientes.Estado UF,  NFiscal.CFOP, Produtos.UN,  Produtos.Descricao,\n"
                    + "Case when NFiscal.ISS > 0 then 'ISS' else 'ICMS' end DescTribut,  NFiscal.AliqISS+NFiscal.AliqICMS Indice, 0 ProdSeq, NFItens.CodPro CodProd,  \n"
                    + "NFItens.Qtde, NFItens.Valor_Tot Valor,Produtos.ICMSID, \n"
                    + "NFiscal.ISS+NFiscal.ICMS Imposto, NFiscal.ICMS CredICMS, '999' IPIEnq, 0 IPI, 0 AliqIPI, '01' AS PISCST, \n"
                    + "NFiscal.AliqPIS,NFiscal.PIS, '01' AS COFINSCST, NFiscal.AliqCOFINS,NFiscal.COFINS,   '01' AS CST, 0 BCICMS\n"
                    + "from NFiscal  \n"
                    + "left join CCusto on CCusto.CCusto = NFiscal.CCusto  \n"
                    + "left join Clientes  on Clientes.Codigo = NFiscal.CliFat\n"
                    + "                   and Clientes.CodFil = NFiscal.CodFil  \n"
                    + "Left Join NFItens on  NFItens.Numero = NFiscal.Numero\n"
                    + "                 and NFItens.Praca  = NFiscal.Praca                    \n"
                    + "				and NFiscal.Serie  = NFiscal.Serie  \n"
                    + "Left Join Produtos on Produtos.Codigo = NFItens.CodPro  \n"
                    + "where NFiscal.Data >= " + dataInicio + "  \n"
                    + "  and NFiscal.Data <= " + dataFim + "   \n"
                    + "  and NFiscal.CodFil >= " + codFilInicio + " \n"
                    + "  and NFiscal.CodFil <= " + codFilFim + " \n"
                    + "  and NFiscal.Situacao = 'A'  \n"
                    + "Order by Tipo, NF, CFOP, UF ");
        }
    }

    public List<RegistroSaidaNotaFiscal> listaFiscalSaida(String dtInicio, String dtFim, String codFil, Persistencia persSaida) throws Exception {
        try {
            List<RegistroSaidaNotaFiscal> retornaSaida = new ArrayList<>();
            String sql = "Select 'SAIDA' Tipo, CCusto.CCusto, CCusto.Descricao CCustoDesc, "
                    + "Clientes.Estado UF,NFiscal.Numero,  NFiscal.Data,Filiais.CNPJ,Filiais.Descricao, Filiais.InscEst, Clientes.Nome, NFiscal.CFOP, NFiscal.CFOP CFOPEnt,  Sum(NFiscal.Valor) Valor, \n"
                    + " Sum(NFiscal.ISS+NFiscal.ICMS) Imposto, \n"
                    + " Sum(NFiscal.ICMS) CredICMS, 0 as IPI, Sum(NFiscal.PIS) PIS, Sum(NFiscal.COFINS) COFINS \n"
                    + " from  NFiscal \n"
                    + " left join CCusto on CCusto.CCusto = NFiscal.CCusto"
                    + " left join Clientes  on Clientes.Codigo = NFiscal.CliFat \n"
                    + "left join Filiais on Filiais.CodFil = NFiscal.CodFil"
                    + " where NFiscal.Data >= ? \n"
                    + "  and NFiscal.Data <= ? \n"
                    + "  and NFiscal.Situacao = 'A' \n"
                    + "  and NFiscal.ISS = 0 \n"
                    + " and NFiscal.CodFil = ?"
                    + " Group by CCusto.CCusto,Clientes.Nome,Filiais.CNPJ,Filiais.Descricao, Filiais.InscEst,NFiscal.Numero, NFiscal.Data , CCusto.Descricao, Clientes.Estado,  NFiscal.CFOP \n"
                    + "  Order by Tipo,CFOP, UF ";

            Consulta consulta = new Consulta(sql, persSaida);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codFil);
            consulta.select();

            RegistroSaidaNotaFiscal registroSaida;
            CCusto ccustoSaida;
            Clientes clientesSaida;
            NFiscal nfiscalSaida;
            Filiais filiais;

            while (consulta.Proximo()) {
                registroSaida = new RegistroSaidaNotaFiscal();
                ccustoSaida = new CCusto();
                clientesSaida = new Clientes();
                nfiscalSaida = new NFiscal();
                filiais = new Filiais();

                ccustoSaida.setCCusto(consulta.getString("CCusto"));
                ccustoSaida.setDescricao(consulta.getString("CCustoDesc"));

                clientesSaida.setEstado(consulta.getString("UF"));
                clientesSaida.setNome(consulta.getString("Nome"));

                nfiscalSaida.setCFOP(consulta.getString("CFOP"));
                nfiscalSaida.setCFOP(consulta.getString("CFOPEnt"));
                nfiscalSaida.setValor(consulta.getString("Valor"));
                nfiscalSaida.setISS(consulta.getString("Imposto"));
                nfiscalSaida.setISS(consulta.getString("Imposto"));
                nfiscalSaida.setICMS(consulta.getString("CredICMS"));
                nfiscalSaida.setPIS(consulta.getString("PIS"));
                nfiscalSaida.setCOFINS(consulta.getString("COFINS"));
                nfiscalSaida.setData(consulta.getLocalDate("Data"));
                filiais.setInscEst(consulta.getString("InscEst"));
                filiais.setCNPJ(consulta.getString("CNPJ"));
                filiais.setDescricao(consulta.getString("Descricao"));

                registroSaida.setCcusto(ccustoSaida);
                registroSaida.setClientes(clientesSaida);
                registroSaida.setNfiscal(nfiscalSaida);
                registroSaida.setFiliais(filiais);

                retornaSaida.add(registroSaida);

            }
            consulta.Close();
            return retornaSaida;
        } catch (Exception ex) {
            throw new Exception("NFiscalDao.ListaFisSaida - " + ex.getMessage() + ""
                    + "Select 'SAIDA' Tipo, CCusto.CCusto, CCusto.Descricao CCustoDesc, Clientes.Estado UF,  NFiscal.CFOP, NFiscal.CFOP CFOPEnt,  Sum(NFiscal.Valor) Valor, \n"
                    + " Sum(NFiscal.ISS+NFiscal.ICMS) Imposto, \n"
                    + " Sum(NFiscal.ICMS) CredICMS, 0 as IPI, Sum(NFiscal.PIS) PIS, Sum(NFiscal.COFINS) COFINS \n"
                    + " from NFiscal \n"
                    + " left join CCusto on CCusto.CCusto = NFiscal.CCusto \n"
                    + " left join Clientes  on Clientes.Codigo = NFiscal.CliFat \n"
                    + "                    and Clientes.CodFil = NFiscal.CodFil \n"
                    + " where NFiscal.Data >= " + dtInicio + "\n"
                    + "  and NFiscal.Data <= " + dtFim + "\n"
                    + "  and NFiscal.Situacao = 'A'\n"
                    + "  and NFiscal.ISS = 0 \n"
                    + "  and NFiscal.CodFil = " + codFil + " "
                    + " Group by CCusto.CCusto, CCusto.Descricao, Clientes.Estado,  NFiscal.CFOP \n"
                    + "  Order by Tipo,CFOP, UF ");

        }
    }

    /**
     * Conta o número de notas fiscais cadastrados no banco
     *
     * @param filtros
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalNFiscalMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from NFiscal "
                    + "     LEFT JOIN Clientes ON Clientes.Codigo = NFiscal.CliFat "
                    + "                       AND Clientes.CodFil = NFiscal.CodFil "
                    + " WHERE NFiscal.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar NFiscal - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de notas fiscais para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<NFiscal> listaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<NFiscal> retorno = new ArrayList();
        try {
            Map<String, String> filtro = filtros;
            String sql = "SELECT  * \n"
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY Data DESC, Numero DESC ) AS RowNum, \n"
                    + "     NFiscal.*, CONVERT(BigInt, Numero) NumeroF, Clientes.Nome, Pracas.NRed NRedPraca, '' as Link \n"
                    + "     FROM NFiscal \n"
                    + "     LEFT JOIN Clientes ON Clientes.Codigo = NFiscal.CliFat \n"
                    + "                       AND Clientes.CodFil = NFiscal.CodFil \n"
                    + "     LEFT JOIN Pracas ON Pracas.Codigo = NFiscal.Praca \n"
                    + "     WHERE NFiscal.codfil in (select filiais.codfil \n"
                    + "                          from saspw \n"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome \n"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac \n"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil \n"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) \n";
            if (filtros != null) {                

                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + " AND " + entrada.getKey() + " \n";
                    }
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            if (filtros != null) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        consulta.setString(entrada.getValue());
                    }
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            NFiscal nfiscal;
            while (consulta.Proximo()) {
                nfiscal = new NFiscal();
                nfiscal.setCodFil(consulta.getString("CodFil"));
                nfiscal.setNRed(consulta.getString("Nome"));
                nfiscal.setSerie(consulta.getString("Serie"));
                nfiscal.setPraca(consulta.getString("Praca"));
                nfiscal.setSituacao(consulta.getString("Situacao"));
                nfiscal.setNRedPraca(consulta.getString("NRedPraca"));
                nfiscal.setNumero(consulta.getString("NumeroF"));
                nfiscal.setData(consulta.getLocalDate("Data"));
                nfiscal.setDt_audit(consulta.getLocalDate("Dt_audit"));
                nfiscal.setValor(consulta.getString("Valor"));
                nfiscal.setNFERetorno(consulta.getString("NFeRetorno"));
                nfiscal.setValor(consulta.getString("Valor"));
                nfiscal.setValorLiq(consulta.getString("ValorLiq"));
                nfiscal.setLink(consulta.getString("link"));
                retorno.add(nfiscal);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("NFiscalDao.listaPaginada - " + e.getMessage());
        }
    }



    /**
     * Lista notas fiscais do cliente para o portal
     *
     * @param codPessoa - código da pessoa logada
     * @param codfil - código da filial
     * @param compet - competência (YYYYMM)
     * @param persistencia - conexão ao banco de dados local
     * @param central - conexão ao banco central
     * @return Lista de notas fiscais do cliente
     * @throws Exception
     */
    public List<NFiscalCliente> listarNotasFiscaisCliente(String codPessoa, String codfil, String compet, Persistencia persistencia, Persistencia central) throws Exception {
        try {
            List<NFiscalCliente> retorno = new ArrayList<>();
            System.out.println("Chegou na busca");
            // Primeiro, buscar os códigos de cliente no banco central
            List<String> codigosCliente = new ArrayList<>();
            try {
                String sqlCentral = "Select CodCli from GTVeAcesso where GTVeAcesso.CodPessoa = ? AND GTVeAcesso.CodFil = ?";
                Consulta consultaCentral = new Consulta(sqlCentral, central);
                consultaCentral.setString(codPessoa);
                consultaCentral.setString(codfil);
                consultaCentral.select();

                while (consultaCentral.Proximo()) {
                    codigosCliente.add(consultaCentral.getString("CodCli"));
                }
                consultaCentral.Close();
            } catch (Exception e) {
                System.out.println("Erro ao consultar GTVeAcesso no banco central: " + e.getMessage());
                return retorno; // Retorna lista vazia se não conseguir acessar o banco central
            }

            if (codigosCliente.isEmpty()) {
                System.out.println("Nenhum cliente encontrado para codPessoa: " + codPessoa + ", codfil: " + codfil);
                return retorno;
            }

            System.out.println("Códigos cliente: " + codigosCliente.size());
            // Construir a consulta com os códigos encontrados
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < codigosCliente.size(); i++) {
                if (i > 0) inClause.append(",");
                inClause.append("?");
            }

            String sql = "Select NFiscal.Praca, NFiscal.Numero, NFiscal.Serie, NFiscal.Data, NFiscal.NRed, Clientes.Nome, NFiscal.CfOP_Descr TipoServico, "
                    + "NFiscal.Valor, NFiscal.ValorLiq, NFiscal.Dt_Venc from nfiscal "
                    + "left join Clientes on Clientes.Codigo = NFiscal.Clifat "
                    + "                   and Clientes.CodFil = NFiscal.CodFil "
                    + "where NFiscal.CliFat in (" + inClause.toString() + ") "
                    + "  and NFiscal.CodFil = ? "
                    + "  and NFiscal.Compet = ? "
                    + "  And NFiscal.Situacao = 'A' "
                    + "order by NFiscal.Data desc, NFiscal.Numero desc";

            System.out.println(sql);
            Consulta consulta = new Consulta(sql, persistencia);
            
            // Adicionar os códigos de cliente como parâmetros
            for (String codCli : codigosCliente) {
                consulta.setString(codCli);
            }
            consulta.setString(codfil);
            consulta.setString(compet);
            consulta.select();

            while (consulta.Proximo()) {
                NFiscalCliente nf = new NFiscalCliente();

                // Converter praça para inteiro (remover .0)
                String praca = consulta.getString("Praca");
                if (praca != null && praca.contains(".")) {
                    praca = praca.substring(0, praca.indexOf("."));
                }
                nf.setPraca(praca);

                // Converter número para inteiro (remover .0)
                String numero = consulta.getString("Numero");
                if (numero != null && numero.contains(".")) {
                    numero = numero.substring(0, numero.indexOf("."));
                }
                nf.setNumero(numero);

                nf.setSerie(consulta.getString("Serie"));
                nf.setData(consulta.getLocalDate("Data"));
                nf.setNRed(consulta.getString("NRed"));
                nf.setNomeCliente(consulta.getString("Nome"));
                nf.setTipoServico(consulta.getString("TipoServico"));
                nf.setValor(consulta.getBigDecimal("Valor"));
                nf.setValorLiq(consulta.getBigDecimal("ValorLiq"));
                nf.setDtVenc(consulta.getLocalDate("Dt_Venc"));
                retorno.add(nf);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("NFiscalDao.listarNotasFiscaisCliente - " + e.getMessage());
        }
    }

    /**
     * Lista notas fiscais do cliente para o portal com filtros
     *
     * @param codPessoa - código da pessoa logada
     * @param codfil - código da filial
     * @param compet - competência (YYYYMM)
     * @param filtroNumero - filtro por número da NF (opcional)
     * @param filtroTipoServico - filtro por tipo de serviço (opcional)
     * @param persistencia - conexão ao banco de dados local
     * @param central - conexão ao banco central
     * @return Lista de notas fiscais do cliente filtradas
     * @throws Exception
     */
    public List<NFiscalCliente> listarNotasFiscaisClienteComFiltros(String codPessoa, String codfil, String compet,
            String filtroNumero, String filtroTipoServico, Persistencia persistencia, Persistencia central) throws Exception {
        try {
            List<NFiscalCliente> retorno = new ArrayList<>();
            System.out.println("=== listarNotasFiscaisClienteComFiltros ===");
            System.out.println("Filtro Número: " + filtroNumero);
            System.out.println("Filtro Tipo Serviço: " + filtroTipoServico);

            // Primeiro, buscar os códigos de cliente no banco central
            List<String> codigosCliente = new ArrayList<>();
            try {
                String sqlCentral = "Select CodCli from GTVeAcesso where GTVeAcesso.CodPessoa = ? AND GTVeAcesso.CodFil = ?";
                Consulta consultaCentral = new Consulta(sqlCentral, central);
                consultaCentral.setBigDecimal(new BigDecimal(codPessoa));
                consultaCentral.setString(codfil);
                consultaCentral.select();

                while (consultaCentral.Proximo()) {
                    codigosCliente.add(consultaCentral.getString("CodCli"));
                }
                consultaCentral.Close();
            } catch (Exception e) {
                System.out.println("Erro ao buscar códigos de cliente: " + e.getMessage());
                return retorno;
            }

            if (codigosCliente.isEmpty()) {
                System.out.println("Nenhum código de cliente encontrado");
                return retorno;
            }

            // Construir a cláusula IN para os códigos de cliente
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < codigosCliente.size(); i++) {
                if (i > 0) inClause.append(", ");
                inClause.append("?");
            }

            // Construir SQL com filtros opcionais
            StringBuilder sql = new StringBuilder();
            sql.append("Select NFiscal.Praca, NFiscal.Numero, NFiscal.Serie, NFiscal.Data, NFiscal.NRed, ");
            sql.append("Clientes.Nome, NFiscal.CfOP_Descr TipoServico, NFiscal.Valor, NFiscal.ValorLiq, NFiscal.Dt_Venc ");
            sql.append("from nfiscal ");
            sql.append("left join Clientes on Clientes.Codigo = NFiscal.Clifat ");
            sql.append("                   and Clientes.CodFil = NFiscal.CodFil ");
            sql.append("where NFiscal.CliFat in (" + inClause.toString() + ") ");
            sql.append("  and NFiscal.CodFil = ? ");
            sql.append("  and NFiscal.Compet = ? ");
            sql.append("  And NFiscal.Situacao = 'A' ");

            // Adicionar filtros opcionais
            List<String> parametros = new ArrayList<>();
            // Adicionar os códigos de cliente
            parametros.addAll(codigosCliente);
            parametros.add(codfil);
            parametros.add(compet);

            if (filtroNumero != null && !filtroNumero.trim().isEmpty()) {
                sql.append("  and NFiscal.Numero like ? ");
                parametros.add("%" + filtroNumero.trim() + "%");
            }

            if (filtroTipoServico != null && !filtroTipoServico.trim().isEmpty()) {
                sql.append("  and NFiscal.CfOP_Descr like ? ");
                parametros.add("%" + filtroTipoServico.trim() + "%");
            }

            sql.append("order by NFiscal.Data desc, NFiscal.Numero desc");

            System.out.println("SQL Final: " + sql.toString());
            Consulta consulta = new Consulta(sql.toString(), persistencia);

            // Adicionar todos os parâmetros
            for (String param : parametros) {
                consulta.setString(param);
            }

            consulta.select();

            while (consulta.Proximo()) {
                NFiscalCliente nf = new NFiscalCliente();
                nf.setPraca(consulta.getString("Praca"));

                String numero = consulta.getString("Numero");
                if (numero != null && numero.contains(".")) {
                    numero = numero.substring(0, numero.indexOf("."));
                }
                nf.setNumero(numero);

                nf.setSerie(consulta.getString("Serie"));
                nf.setData(consulta.getLocalDate("Data"));
                nf.setNRed(consulta.getString("NRed"));
                nf.setNomeCliente(consulta.getString("Nome"));
                nf.setTipoServico(consulta.getString("TipoServico"));
                nf.setValor(consulta.getBigDecimal("Valor"));
                nf.setValorLiq(consulta.getBigDecimal("ValorLiq"));
                nf.setDtVenc(consulta.getLocalDate("Dt_Venc"));
                retorno.add(nf);
            }
            consulta.Close();

            System.out.println("Retornando " + retorno.size() + " registros filtrados");
            return retorno;
        } catch (Exception e) {
            throw new Exception("NFiscalDao.listarNotasFiscaisClienteComFiltros - " + e.getMessage());
        }
    }

    /**
     * Lista boletos relacionados a uma nota fiscal
     *
     * @param codPessoa - código da pessoa logada
     * @param codfil - código da filial
     * @param compet - competência (YYYYMM)
     * @param numero - número da nota fiscal (opcional)
     * @param praca - praça da nota fiscal (opcional)
     * @param persistencia - conexão ao banco de dados local
     * @param central - conexão ao banco central
     * @return Lista de boletos
     * @throws Exception
     */
    public List<BoletoCliente> listarBoletosCliente(String codPessoa, String codfil, String compet, String numero, String praca, Persistencia persistencia, Persistencia central) throws Exception {
        try {
            List<BoletoCliente> retorno = new ArrayList<>();

            // Primeiro, buscar os códigos de cliente no banco central
            List<String> codigosCliente = new ArrayList<>();
            try {
                String sqlCentral = "Select CodCli from GTVeAcesso where GTVeAcesso.CodPessoa = ? AND GTVeAcesso.CodFil = ?";
                Consulta consultaCentral = new Consulta(sqlCentral, central);
                consultaCentral.setString(codPessoa);
                consultaCentral.setString(codfil);
                consultaCentral.select();

                while (consultaCentral.Proximo()) {
                    codigosCliente.add(consultaCentral.getString("CodCli"));
                }
                consultaCentral.Close();
            } catch (Exception e) {
                System.out.println("Erro ao consultar GTVeAcesso no banco central para boletos: " + e.getMessage());
                return retorno;
            }

            if (codigosCliente.isEmpty()) {
                System.out.println("Nenhum cliente encontrado para boletos - codPessoa: " + codPessoa + ", codfil: " + codfil);
                return retorno;
            }

            System.out.println("Códigos clientes: " + codigosCliente.size());
            
            // Construir a consulta com os códigos encontrados
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < codigosCliente.size(); i++) {
                if (i > 0) inClause.append(",");
                inClause.append("?");
            }
           

            StringBuilder sql = new StringBuilder();
            sql.append("Select 'https://mobile.sasw.com.br:9091/SatMobile/documentos/anexo-email/Boleto_'");
            sql.append("       +Cast((Cast(NFiscal.CliFat as Integer)*2)+32 as VarChar)+'_'");
            sql.append("       +TCobran.Banco+'_'+Right('00000000'+NFiscal.NossoNumero,8)+'.html' URL, ");
            sql.append("       NFiscal.Numero, NFiscal.Praca, NFiscal.NossoNumero, TCobran.Banco, ");
            sql.append("       NFiscal.Valor, NFiscal.Dt_Venc, NFiscal.Situacao, ");
            sql.append("       NFiscalCobElet.Sequencia, NFiscalCobElet.Ordem ");
            sql.append("from nfiscalCobElet ");
            sql.append("left join NFiscal on NFiscal.Numero = nfiscalCobElet.Numero ");
            sql.append("                  and NFiscal.Praca = nfiscalCobElet.Praca ");
            sql.append("left join TCobran on TCobran.Codigo = NFiscal.TCobran ");
            sql.append("left join Boletos on Boletos.NossoNumero = NFiscal.NossoNumero ");
            sql.append("where NFiscal.CliFat in (" + inClause.toString() + ") ");
            sql.append("  and NFiscal.CodFil = ? ");
            sql.append("  and NFiscal.Compet = ? ");

            List<String> parametros = new ArrayList<>();
            // Adicionar os códigos de cliente
            parametros.addAll(codigosCliente);
            parametros.add(codfil);
            parametros.add(compet);

            // Filtros opcionais por NF específica
            if (numero != null && !numero.trim().isEmpty()) {
                sql.append("  and NFiscal.Numero = ? ");
                parametros.add(numero);
            }
            if (praca != null && !praca.trim().isEmpty()) {
                sql.append("  and NFiscal.Praca = ? ");
                parametros.add(praca);
            }

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            for (int i = 0; i < parametros.size(); i++) {
                consulta.setString(parametros.get(i));
            }
            consulta.select();

            while (consulta.Proximo()) {
                BoletoCliente boleto = new BoletoCliente();
                boleto.setUrl(consulta.getString("URL"));

                // Converter número para inteiro (remover .0)
                String num = consulta.getString("Numero");
                if (num != null && num.contains(".")) {
                    num = num.substring(0, num.indexOf("."));
                }
                boleto.setNumero(num);

                // Converter praça para inteiro (remover .0)
                String prac = consulta.getString("Praca");
                if (prac != null && prac.contains(".")) {
                    prac = praca.substring(0, prac.indexOf("."));
                }
                boleto.setPraca(prac);

                boleto.setNossoNumero(consulta.getString("NossoNumero"));
                boleto.setBanco(consulta.getString("Banco"));
                boleto.setValor(consulta.getBigDecimal("Valor"));
                boleto.setDataVencimento(consulta.getLocalDate("Dt_Venc"));
                boleto.setSituacao(consulta.getString("Situacao"));

                // Converter sequencia para inteiro (remover .0)
                String sequencia = consulta.getString("Sequencia");
                if (sequencia != null && sequencia.contains(".")) {
                    sequencia = sequencia.substring(0, sequencia.indexOf("."));
                }
                boleto.setSequencia(sequencia);

                // Converter ordem para inteiro (remover .0)
                String ordem = consulta.getString("Ordem");
                if (ordem != null && ordem.contains(".")) {
                    ordem = ordem.substring(0, ordem.indexOf("."));
                }
                boleto.setOrdem(ordem);

                retorno.add(boleto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("NFiscalDao.listarBoletosCliente - " + e.getMessage());
        }
    }

}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigDecimal;
import utilitario.GravadorLog;

/**
 *
 * <AUTHOR>
 */
public class Lgbr_tsolicitacaoDao {

    private Persistencia persistencia;

    public Lgbr_tsolicitacaoDao() {
    }

    public Lgbr_tsolicitacaoDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Seleciona senha
     *
     * @param comandoOrg - Comando org
     * @param persistencia - Conexão ao banco
     * @return - String com senha recebida
     * @throws java.lang.Exception - pode gerar exception
     */
    public String pegaDesSenhaRecebe(String comandoOrg, Persistencia persistencia) throws Exception {
        
        String sql = "select des_senha_receb from lgbr_tsolicitacao where"
                + " cod_reg_env_ns = ?";
        String respFech = "";
        try {
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(comandoOrg);
            rs.select();
            while (rs.Proximo()) {
                respFech = rs.getString("des_senha_receb");
            }
            rs.Close();
            return respFech;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar DesSenhaRecebe - " + e.getMessage());
        }
    }

    public String pegaDesSenhaRecebeFechamento(BigDecimal codigo) throws Exception {
        String sql = "SELECT TOP 1 des_senha_receb \n"
                + "FROM lgbr_tsolicitacao \n"
                + "WHERE cod_reg_env_ns = ? \n"
                + "	AND des_senha_receb LIKE 'Fechamento Inv%'";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codigo);
            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getString("des_senha_receb");
            }
            return null;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public String pegaDesSenhaNotNull(String codigo) throws Exception {
        String sql = "SELECT TOP 1 des_senha_receb \n"
                + "FROM lgbr_tsolicitacao \n"
                + "WHERE cod_reg_env_ns = ? \n"
                + "	AND des_senha_receb IS NOT NULL";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codigo);
            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getString("des_senha_receb");
            }
            return null;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    /**
     * Atualiza solicitação senha 1
     *
     * @param codTipo - Tipo
     * @param codPessoa - Número pessoa
     * @param matricula - Número matrícula
     * @param senha - Senha
     * @param comandoOrg - Comando Org
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaSolicitacao(int codTipo, String codPessoa, String matricula, String senha,
            String comandoOrg, Persistencia persistencia) throws Exception {    
        String sql = "update lgbr_tsolicitacao set "
                + " cod_tipo_solicitacao   = ?,"
                + " des_usuario            = ?,"
                + " des_usuario_id         = ?,"
                + " des_senha_usuario      = ?,"
                + " des_senha_receb        = null" //senha de fechamento dynamic
                + " where cod_reg_env_ns   = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(codTipo);
            consulta.setString(codPessoa);
            consulta.setString(matricula);
            consulta.setString(senha);
            consulta.setString(comandoOrg);
            consulta.update();
            consulta.close();
            GravadorLog.log("Executar atualizaSolicitacao");
            GravadorLog.log(
                    "update lgbr_tsolicitacao set "
                    + " cod_tipo_solicitacao   = " + codTipo + ","
                    + " des_usuario            = " + codPessoa + ","
                    + " des_usuario_id         = " + matricula + ","
                    + " des_senha_usuario      = " + senha + ","
                    + " where cod_reg_env_ns   = " + comandoOrg);
            GravadorLog.log("---");
            
        } catch (Exception e) {
            throw new Exception("Lgbr_tsolicitacaoDao.atualizaSolicitacao - " + e.getMessage() + "\r\n"
                    + "update lgbr_tsolicitacao set "
                    + " cod_tipo_solicitacao   = "+codTipo+","
                    + " des_usuario            = "+codPessoa+","
                    + " des_usuario_id         = "+matricula+","
                    + " des_senha_usuario      = "+senha+","
                    + " des_senha_receb        = null" //senha de fechamento dynamic
                    + " where cod_reg_env_ns   = "+comandoOrg);
        }
    }

    /**
     * Atualiza solicitação senha2
     *
     * @param codTipo - Tipo
     * @param codPessoa - Número pessoa
     * @param matricula - Número matrícula
     * @param senha - Senha
     * @param desSenhaRecFecha - Senha de Fechamento Dynamic
     * @param comandoOrg - Comando Org
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void atualizaSolicitacao(int codTipo, String codPessoa, String matricula, String senha,
            String desSenhaRecFecha, String comandoOrg, Persistencia persistencia) throws Exception {
        String sql = "";
            sql = "update lgbr_tsolicitacao set "
                + " cod_tipo_solicitacao   = ?,"
                + " des_usuario            = ?,"
                + " des_usuario_id         = ?,"
                + " des_senha_usuario      = ?,"
                + " des_senha_receb        = ?," //senha de fechamento dynamic
                + " des_senha_receb_fecha  = ?" //senha de fechamento dynamic
                + " where cod_reg_env_ns   = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(codTipo);
            consulta.setString(codPessoa);
            consulta.setString(matricula);
            consulta.setString(senha);
            consulta.setString(desSenhaRecFecha); //senha de fechamento dynamic
            consulta.setString(desSenhaRecFecha); //senha de fechamento dynamic
            consulta.setString(comandoOrg);
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("Lgbr_tsolicitacaoDao.atualizaSolicitacao Fech - "+e.getMessage()+"\r\n"
                    + "update lgbr_tsolicitacao set "
                + " cod_tipo_solicitacao   = "+codTipo+","
                + " des_usuario            = "+codPessoa+","
                + " des_usuario_id         = "+matricula+","
                + " des_senha_usuario      = "+senha+","
                + " des_senha_receb        = "+desSenhaRecFecha+"," //senha de fechamento dynamic
                + " des_senha_receb_fecha  = "+desSenhaRecFecha+"" //senha de fechamento dynamic
                + " where cod_reg_env_ns   = "+comandoOrg);
        }
    }
    
    public void atualizaFechamento(String codPessoa, String matricula, String senha,
        String desSenhaRecFecha, String comandoOrg, Persistencia persistencia) throws Exception {
            //'Update lgbr_tsolicitacao set '+
            //' cod_tipo_solicitacao  = '+'10'+ ', '+  // Comando de validação do fechamento
            //' des_usuario           = '+Chr(39)+Usuario+Chr(39)+', '+
            //' des_usuario_id        = '+LbMatr.Caption+', '+
            //' des_senha_usuario     = '+Chr(39)+ArredondaStr(vSenhaAtualX,4,0)+Chr(39)+', '+
            //' des_senha_receb       = '+Chr(39)+vSenhaFech+Chr(39)+','+
            //' des_senha_receb_fecha = '+Chr(39)+vSenhaFech+Chr(39);
            //' where cod_reg_env_ns =  '+Chr(39)+vCodFechaduraX+Chr(39);        
            String sql = "update lgbr_tsolicitacao set "
                + " cod_tipo_solicitacao   = 10,"
                + " des_usuario            = ?,"
                + " des_usuario_id         = ?,"
                + " des_senha_usuario      = ?,"
                + " des_senha_receb        = ?," //senha de fechamento dynamic
                + " des_senha_receb_fecha  = ?" //senha de fechamento dynamic
                + " where cod_reg_env_ns   = ?";
            
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.setString(matricula);
            consulta.setString(senha);
            consulta.setString(desSenhaRecFecha); //senha de fechamento dynamic
            consulta.setString(desSenhaRecFecha); //senha de fechamento dynamic
            consulta.setString(comandoOrg);
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("Lgbr_tsolicitacaoDao.atualizaSolicitacao Fech - "+e.getMessage()+"\r\n"
                    + "update lgbr_tsolicitacao set "
                + " cod_tipo_solicitacao   = 10,"
                + " des_usuario            = "+codPessoa+","
                + " des_usuario_id         = "+matricula+","
                + " des_senha_usuario      = "+senha+","
                + " des_senha_receb        = "+desSenhaRecFecha+"," //senha de fechamento dynamic
                + " des_senha_receb_fecha  = "+desSenhaRecFecha+"" //senha de fechamento dynamic
                + " where cod_reg_env_ns   = "+comandoOrg);
        }

    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaPortalSrv;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaPortalSrvDao {

    public void inserirServicoAutomatico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO PessoaPortalSrv \n"
                    + " (Codigo, Servico, Ordem, Oper_Incl, Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, (SELECT ISNULL(MAX(Ordem), 0) + 1 FROM PessoaPortalSrv WHERE Codigo = ?), ?, ?, ?, ?, ?, ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaPortalSrv.getCodigo());
            consulta.setString(pessoaPortalSrv.getServico());
            consulta.setString(pessoaPortalSrv.getCodigo());
            consulta.setString(pessoaPortalSrv.getOper_Incl());
            consulta.setString(pessoaPortalSrv.getDt_Incl());
            consulta.setString(pessoaPortalSrv.getHr_Incl());
            consulta.setString(pessoaPortalSrv.getOperador());
            consulta.setString(pessoaPortalSrv.getDt_Alter());
            consulta.setString(pessoaPortalSrv.getHr_Alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaPortalSrvDao.inserirServico - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PessoaPortalSrv \n"
                    + " (Codigo, Servico, Ordem, Oper_Incl, Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, (SELECT ISNULL(MAX, Ordem), 0) + 1 FROM PessoaPortalSrv WHERE Codigo = ?), ?, ?, ?, ?, ?, ?)");
        }
    }

    public void inserirServico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO pessoaportalsrv (Codigo, Servico, Ordem, Oper_Incl, Dt_Incl, Hr_Incl, Operador, dt_Alter, Hr_Alter)\n"
                    + "SELECT TOP 1 ?, ?, ?, ?, ?, ?, ?, ?, ? FROM \n"
                    + "(SELECT COUNT(*) AS qtde_cadastrado  FROM pessoaportalsrv WHERE codigo = ? AND Servico = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaPortalSrv.getCodigo());
            consulta.setString(pessoaPortalSrv.getServico());
            consulta.setString(pessoaPortalSrv.getOrdem());
            consulta.setString(pessoaPortalSrv.getOper_Incl());
            consulta.setString(pessoaPortalSrv.getDt_Incl());
            consulta.setString(pessoaPortalSrv.getHr_Incl());
            consulta.setString(pessoaPortalSrv.getOperador());
            consulta.setString(pessoaPortalSrv.getDt_Alter());
            consulta.setString(pessoaPortalSrv.getHr_Alter());

            consulta.setString(pessoaPortalSrv.getCodigo());
            consulta.setString(pessoaPortalSrv.getServico());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaPortalSrvDao.inserirServico - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PessoaPortalSrv \n"
                    + " (Codigo, Servico, Ordem, Oper_Incl, Dt_Incl, Hr_Incl, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, (SELECT ISNULL(MAX, Ordem), 0) + 1 FROM PessoaPortalSrv WHERE Codigo = ?), ?, ?, ?, ?, ?, ?)");
        }
    }

    public void removerServico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM pessoaportalsrv WHERE codigo = ? AND Servico = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaPortalSrv.getCodigo());
            consulta.setString(pessoaPortalSrv.getServico());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaPortalSrvDao.removerServico - " + e.getMessage() + "\r\n"
                    + " DELETE FROM pessoaportalsrv WHERE codigo = " + pessoaPortalSrv.getCodigo() + " AND Servico = " + pessoaPortalSrv.getServico());
        }
    }

    /**
     * Lista os serviços de uma pessoa
     *
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PessoaPortalSrv> listarServicosUsuario(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<PessoaPortalSrv> retorno = new ArrayList<>();
            String sql = " SELECT PessoaPortalSrv.*, PortalSrv.Descricao \n"
                    + " FROM PessoaPortalSrv \n"
                    + " LEFT JOIN PortalSrv ON PortalSrv.Codigo = PessoaPortalSrv.Servico \n"
                    + " WHERE PessoaPortalSrv.Codigo = ? "
                    + " ORDER BY Ordem, Descricao";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
            consulta.select();
            PessoaPortalSrv pessoaPortalSrv;
            while (consulta.Proximo()) {
                pessoaPortalSrv = new PessoaPortalSrv();
                pessoaPortalSrv.setCodigo(consulta.getString("Codigo"));
                pessoaPortalSrv.setServico(consulta.getString("Servico"));
                pessoaPortalSrv.setOrdem(consulta.getString("Ordem"));
                pessoaPortalSrv.setOper_Incl(consulta.getString("Oper_Incl"));
                pessoaPortalSrv.setDt_Incl(consulta.getString("Dt_Incl"));
                pessoaPortalSrv.setHr_Incl(consulta.getString("Hr_Incl"));
                pessoaPortalSrv.setDescricao(consulta.getString("Descricao"));
                pessoaPortalSrv.setOperador(consulta.getString("Operador"));
                pessoaPortalSrv.setDt_Alter(consulta.getString("Dt_Alter"));
                pessoaPortalSrv.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(pessoaPortalSrv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaPortalSrvDao.listarServicosUsuário - " + e.getMessage() + "\r\n"
                    + "SELECT PessoaPortalSrv.*, PortalSrv.Descricao \n"
                    + " FROM PessoaPortalSrv \n"
                    + " LEFT JOIN PortalSrv ON PortalSrv.Codigo = PessoaPortalSrv.Servico \n"
                    + " WHERE PessoaPortalSrv.Codigo = " + codPessoa
                    + " ORDER BY Ordem, Descricao");
        }
    }
}

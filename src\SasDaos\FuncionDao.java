package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.FPRescisoes;
import SasBeans.Filiais;
import SasBeans.Fornec;
import SasBeans.Funcion;
import SasBeans.Pe_Doctos;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeans.RHEscala;
import SasBeans.RHHorario;
import SasBeans.Sindicatos;
import SasBeansCompostas.CarregarRelatorio;
import SasBeansCompostas.FuncionPstServ;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FuncionDao {

    public Funcion buscarFuncion(String matr, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT \n"
                    + "     Funcion.*, \n"
                    + "     CONVERT(VarChar, Dt_Admis, 112) Dt_AdmisC, \n"
                    + "     CONVERT(VarChar, Dt_Situac, 112) Dt_SituacC, \n"
                    + "     CONVERT(VarChar, Dt_FormIni, 112) Dt_FormIniC, \n"
                    + "     CONVERT(VarChar, Dt_FormFim, 112) Dt_FormFimC, \n"
                    + "     CONVERT(VarChar, DtValCNV, 112) DtValCNVC, \n"
                    + "     CONVERT(VarChar, DtEmissaoCNV, 112) DtEmissaoCNVC, \n"
                    + "     CONVERT(VarChar, Dt_Recicl, 112) Dt_ReciclC, \n"
                    + "     CONVERT(VarChar, Dt_VenCurs, 112) Dt_VenCursC, \n"
                    + "     CONVERT(VarChar, Dt_ExameMe, 112) Dt_ExameMeC, \n"
                    + "     CONVERT(VarChar, Dt_Psico, 112) Dt_PsicoC, \n"
                    + "     CONVERT(VarChar, Dt_Nasc, 112) Dt_NascC, \n"
                    + "     CONVERT(VarChar, Dt_VenCNH, 112) Dt_VenCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Demis, 112) Dt_DemisC, \n"
                    + "     CONVERT(VarChar, Dt_ExameCNH, 112) Dt_ExameCNHC, \n"
                    + "     CONVERT(VarChar, Funcion.Dt_Alter, 112) Dt_AlterC, \n"
                    + "     CtrItens.TipoPosto+' - '+CtrItens.Descricao Contrato, \n"
                    + "     PstServ.Local, \n"
                    + "     Cargos.Descricao DescrCargo \n"
                    + " FROM \n"
                    + "     Funcion \n"
                    + " LEFT JOIN \n"
                    + "     PstServ on PstServ.Secao = Funcion.Secao AND PstServ.Codfil = Funcion.CodFil \n"
                    + " LEFT JOIN \n"
                    + "     CtrItens on CtrItens.Codfil = PstServ.Codfil AND CtrItens.Contrato = PstServ.Contrato AND CtrItens.TipoPosto = PstServ.TipoPosto \n"
                    + " LEFT JOIN \n"
                    + "     Cargos on Funcion.Cargo = Cargos.Cargo \n"
                    + " WHERE \n"
                    + "     Matr = ?\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();
            Funcion funcion = null;
            if (consulta.Proximo()) {
                funcion = new Funcion();

                funcion.setMatr(consulta.getBigDecimal("Matr"));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getBigDecimal("CodFil"));
                funcion.setRegional(consulta.getInt("Regional"));
                funcion.setSecao(consulta.getString("Secao"));
                funcion.setCCusto(consulta.getString("CCusto"));
                funcion.setCodPonto(consulta.getString("CodPonto"));
                funcion.setDt_Admis(consulta.getString("Dt_AdmisC"));
                funcion.setCargo(consulta.getString("Cargo"));
                funcion.setCodCargo(consulta.getString("CodCargo"));
                funcion.setApresen(consulta.getString("Apresen"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setDt_Situac(consulta.getString("Dt_SituacC"));
                funcion.setEscala(consulta.getString("Escala"));
                funcion.setHorario(consulta.getInt("Horario"));
                funcion.setGrpEscala(consulta.getInt("GrpEscala"));
                funcion.setFuncao(consulta.getString("Funcao"));
                funcion.setDt_FormIni(consulta.getString("Dt_FormIniC"));
                funcion.setDt_FormFim(consulta.getString("Dt_FormFimC"));
                funcion.setLocalForm(consulta.getString("LocalForm"));
                funcion.setCertific(consulta.getString("Certific"));
                funcion.setReg_PF(consulta.getString("Reg_PF"));
                funcion.setLocal_PF(consulta.getString("Local_PF"));
                funcion.setReg_PFUF(consulta.getString("Reg_PFUF"));
                funcion.setReg_PFDt(consulta.getString("Reg_PFDt"));
                funcion.setCarNacVig(consulta.getString("CarNacVig"));
                funcion.setDtValCNV(consulta.getString("DtValCNVC"));
                funcion.setDtEmissaoCNV(consulta.getString("DtEmissaoCNVC"));
                funcion.setCadastroAFIS(consulta.getString("CadastroAFIS"));
                funcion.setReg_MT(consulta.getString("Reg_MT"));
                funcion.setDt_Recicl(consulta.getString("Dt_ReciclC"));
                funcion.setDt_VenCurs(consulta.getString("Dt_VenCursC"));
                funcion.setDt_ExameMe(consulta.getString("Dt_ExameMeC"));
                funcion.setDt_Psico(consulta.getString("Dt_PsicoC"));
                funcion.setExtensaoTV(consulta.getString("ExtensaoTV"));
                funcion.setExtSegPes(consulta.getString("ExtSegPes"));
                funcion.setExtEscolta(consulta.getString("ExtEscolta"));
                funcion.setExtGrdEventos(consulta.getString("ExtGrdEventos"));
                funcion.setExtArmasNLetais(consulta.getString("ExtArmasNLetais"));
                funcion.setGrupoSang(consulta.getString("GrupoSang"));
                funcion.setInstrucao(consulta.getString("Instrucao"));
                funcion.setRaca(consulta.getString("Raca"));
                funcion.setEstCivil(consulta.getString("EstCivil"));
                funcion.setEndereco(consulta.getString("Endereco"));
                funcion.setNumero(consulta.getString("Numero"));
                funcion.setComplemento(consulta.getString("Complemento"));
                funcion.setBairro(consulta.getString("Bairro"));
                funcion.setCidade(consulta.getString("Cidade"));
                funcion.setUF(consulta.getString("UF"));
                funcion.setCEP(consulta.getString("CEP"));
                funcion.setFone1(consulta.getString("Fone1"));
                funcion.setFone2(consulta.getString("Fone2"));
                funcion.setEmail(consulta.getString("Email"));
                funcion.setDt_Nasc(consulta.getString("Dt_NascC"));
                funcion.setSexo(consulta.getString("Sexo"));
                funcion.setNaturalid(consulta.getString("Naturalid"));
                funcion.setPai(consulta.getString("Pai"));
                funcion.setMae(consulta.getString("Mae"));
                funcion.setConjuge(consulta.getString("Conjuge"));
                funcion.setCNH(consulta.getString("CNH"));
                funcion.setDt_VenCNH(consulta.getString("Dt_VenCNHC"));
                funcion.setUF_CNH(consulta.getString("UF_CNH"));
                funcion.setCategoria(consulta.getString("Categoria"));
                funcion.setRG(consulta.getString("RG"));
                funcion.setOrgEmis(consulta.getString("OrgEmis"));
                funcion.setRgDtEmis(consulta.getString("RgDtEmis"));
                funcion.setCPF(consulta.getString("CPF"));
                funcion.setPIS(consulta.getString("PIS"));
                funcion.setReservista(consulta.getString("Reservista"));
                funcion.setReservCat(consulta.getString("ReservCat"));
                funcion.setCTPS_Nro(consulta.getString("CTPS_Nro"));
                funcion.setCTPS_Serie(consulta.getString("CTPS_Serie"));
                funcion.setCTPS_UF(consulta.getString("CTPS_UF"));
                funcion.setCTPS_Emis(consulta.getString("CTPS_Emis"));
                funcion.setTitEleit(consulta.getString("TitEleit"));
                funcion.setTitEZona(consulta.getString("TitEZona"));
                funcion.setTitSecao(consulta.getString("TitSecao"));
                funcion.setCt_Banco(consulta.getString("Ct_Banco"));
                funcion.setCt_Agencia(consulta.getString("Ct_Agencia"));
                funcion.setCt_Conta(consulta.getString("Ct_Conta"));
                funcion.setCt_CodOper(consulta.getString("Ct_CodOper"));
                funcion.setObs(consulta.getString("Obs"));
                funcion.setSalario(consulta.getString("Salario"));
                funcion.setSindicato(consulta.getString("Sindicato"));
                funcion.setCHMes(consulta.getString("CHMes"));
                funcion.setCHSeman(consulta.getString("CHSeman"));
                funcion.setHe_Periodo(consulta.getString("He_Periodo"));
                funcion.setDepIR(consulta.getString("DepIR"));
                funcion.setDepSF(consulta.getString("DepSF"));
                funcion.setFGTSOpcao(consulta.getString("FGTSOpcao"));
                funcion.setFGTSBanco(consulta.getString("FGTSBanco"));
                funcion.setFGTSAg(consulta.getString("FGTSAg"));
                funcion.setPgCtSin(consulta.getString("PgCtSin"));
                funcion.setAssMedic(consulta.getString("AssMedic"));
                funcion.setDepAssMed(consulta.getString("DepAssMed"));
                funcion.setCestaBas(consulta.getString("CestaBas"));
                funcion.setValeRef(consulta.getString("ValeRef"));
                funcion.setConvFarma(consulta.getString("ConvFarma"));
                funcion.setSegVida(consulta.getString("SegVida"));
                funcion.setTipoAdm(consulta.getString("TipoAdm"));
                funcion.setDefFis(consulta.getString("DefFis"));
                funcion.setDefFisTipo(consulta.getString("DefFisTipo"));
                funcion.setDefFisDesc(consulta.getString("DefFisDesc"));
                funcion.setNacionalid(consulta.getString("Nacionalid"));
                funcion.setAnoCheg(consulta.getString("AnoCheg"));
                funcion.setFolhaLivro(consulta.getString("FolhaLivro"));
                funcion.setPgINSS(consulta.getString("PgINSS"));
                funcion.setPgIR(consulta.getString("PgIR"));
                funcion.setSEFIPOcor(consulta.getString("SEFIPOcor"));
                funcion.setConta_Ctb(consulta.getString("Conta_Ctb"));
                funcion.setAltura(consulta.getString("Altura"));
                funcion.setPeso(consulta.getString("Peso"));
                funcion.setDt_Demis(consulta.getString("Dt_DemisC"));
                funcion.setCodCidade(consulta.getString("CodCidade"));
                funcion.setCodNaturalid(consulta.getString("CodNaturalid"));
                funcion.setExpGESP(consulta.getString("ExpGESP"));
                funcion.setVinculo(consulta.getString("Vinculo"));
                funcion.setFormaPgto(consulta.getString("FormaPgto"));
                funcion.setJornada(consulta.getString("Jornada"));
                funcion.setSegDesemp(consulta.getString("SegDesemp"));
                funcion.setFPAdiant(consulta.getString("FPAdiant"));
                funcion.setCodAlimentacao(consulta.getString("CodAlimentacao"));
                funcion.setChavebancaria(consulta.getString("Chavebancaria"));
                funcion.setCodPessoaWeb(consulta.getBigDecimal("CodPessoaWeb"));
                funcion.setInterfExt(consulta.getString("InterfExt"));
                funcion.setCod_ExameCNH(consulta.getString("Cod_ExameCNH"));
                funcion.setDt_ExameCNH(consulta.getString("Dt_ExameCNHC"));
                funcion.setCNPJ_LabExame(consulta.getString("CNPJ_LabExame"));
                funcion.setUF_ExameCNH(consulta.getString("UF_ExameCNH"));
                funcion.setCRM_ExamCNH(consulta.getString("CRM_ExamCNH"));
                funcion.setTrabParcial(consulta.getString("TrabParcial"));
                funcion.setTeletrabalho(consulta.getString("Teletrabalho"));
                funcion.setTrabIntermitente(consulta.getString("TrabIntermitente"));
                funcion.setOperador(consulta.getString("Operador"));
                funcion.setDt_Alter(consulta.getString("Dt_AlterC"));
                funcion.setHr_Alter(consulta.getString("Hr_Alter"));
                funcion.setLocalRef(consulta.getString("Local"));
                funcion.setCargoDescr(consulta.getString("DescrCargo"));
                
                funcion.setContrato(consulta.getString("Contrato"));
            }
            consulta.close();
            return funcion;
        } catch (Exception e) {
            throw new Exception("FuncionDao.buscarFuncion - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     Funcion.*, \n"
                    + "     CONVERT(VarChar, Dt_Admis, 112) Dt_AdmisC, \n"
                    + "     CONVERT(VarChar, Dt_Situac, 112) Dt_SituacC, \n"
                    + "     CONVERT(VarChar, Dt_FormIni, 112) Dt_FormIniC, \n"
                    + "     CONVERT(VarChar, Dt_FormFim, 112) Dt_FormFimC, \n"
                    + "     CONVERT(VarChar, DtValCNV, 112) DtValCNVC, \n"
                    + "     CONVERT(VarChar, DtEmissaoCNV, 112) DtEmissaoCNVC, \n"
                    + "     CONVERT(VarChar, Dt_Recicl, 112) Dt_ReciclC, \n"
                    + "     CONVERT(VarChar, Dt_VenCurs, 112) Dt_VenCursC, \n"
                    + "     CONVERT(VarChar, Dt_ExameMe, 112) Dt_ExameMeC, \n"
                    + "     CONVERT(VarChar, Dt_Psico, 112) Dt_PsicoC, \n"
                    + "     CONVERT(VarChar, Dt_Nasc, 112) Dt_NascC, \n"
                    + "     CONVERT(VarChar, Dt_VenCNH, 112) Dt_VenCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Demis, 112) Dt_DemisC, \n"
                    + "     CONVERT(VarChar, Dt_ExameCNH, 112) Dt_ExameCNHC, \n"
                    + "     CONVERT(VarChar, Funcion.Dt_Alter, 112) Dt_AlterC, \n"
                    + "     CtrItens.TipoPosto+' - '+CtrItens.Descricao Contrato \n"
                    + " FROM \n"
                    + "     Funcion \n"
                    + " LEFT JOIN \n"
                    + "     PstServ on PstServ.Secao = Funcion.Secao AND PstServ.Codfil = Funcion.CodFil \n"
                    + " LEFT JOIN \n"
                    + "     CtrItens on CtrItens.Codfil = PstServ.Codfil AND CtrItens.Contrato = PstServ.Contrato AND CtrItens.TipoPosto = PstServ.TipoPosto \n"
                    + " WHERE \n"
                    + "     Matr = " + matr + "\n");
        }
    }

    public List<Funcion> selecionarFuncion(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = " SELECT \n"
                    + "     *, \n"
                    + "     CONVERT(VarChar, Dt_Admis, 112) Dt_AdmisC, \n"
                    + "     CONVERT(VarChar, Dt_Situac, 112) Dt_SituacC, \n"
                    + "     CONVERT(VarChar, Dt_FormIni, 112) Dt_FormIniC, \n"
                    + "     CONVERT(VarChar, Dt_FormFim, 112) Dt_FormFimC, \n"
                    + "     CONVERT(VarChar, DtValCNV, 112) DtValCNVC, \n"
                    + "     CONVERT(VarChar, DtEmissaoCNV, 112) DtEmissaoCNVC, \n"
                    + "     CONVERT(VarChar, Dt_Recicl, 112) Dt_ReciclC, \n"
                    + "     CONVERT(VarChar, Dt_VenCurs, 112) Dt_VenCursC, \n"
                    + "     CONVERT(VarChar, Dt_ExameMe, 112) Dt_ExameMeC, \n"
                    + "     CONVERT(VarChar, Dt_Psico, 112) Dt_PsicoC, \n"
                    + "     CONVERT(VarChar, Dt_Nasc, 112) Dt_NascC, \n"
                    + "     CONVERT(VarChar, Dt_VenCNH, 112) Dt_VenCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Demis, 112) Dt_DemisC, \n"
                    + "     CONVERT(VarChar, Dt_ExameCNH, 112) Dt_ExameCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM \n"
                    + "     Funcion \n"
                    + " WHERE \n"
                    + "     CodFil = ?\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            Funcion funcion;
            while (consulta.Proximo()) {
                funcion = new Funcion();

                funcion.setMatr(consulta.getBigDecimal("Matr"));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getBigDecimal("CodFil"));
                funcion.setRegional(consulta.getInt("Regional"));
                funcion.setSecao(consulta.getString("Secao"));
                funcion.setCCusto(consulta.getString("CCusto"));
                funcion.setCodPonto(consulta.getString("CodPonto"));
                funcion.setDt_Admis(consulta.getString("Dt_AdmisC"));
                funcion.setCargo(consulta.getString("Cargo"));
                funcion.setCodCargo(consulta.getString("CodCargo"));
                funcion.setApresen(consulta.getString("Apresen"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setDt_Situac(consulta.getString("Dt_SituacC"));
                funcion.setEscala(consulta.getString("Escala"));
                funcion.setHorario(consulta.getInt("Horario"));
                funcion.setGrpEscala(consulta.getInt("GrpEscala"));
                funcion.setFuncao(consulta.getString("Funcao"));
                funcion.setDt_FormIni(consulta.getString("Dt_FormIniC"));
                funcion.setDt_FormFim(consulta.getString("Dt_FormFimC"));
                funcion.setLocalForm(consulta.getString("LocalForm"));
                funcion.setCertific(consulta.getString("Certific"));
                funcion.setReg_PF(consulta.getString("Reg_PF"));
                funcion.setLocal_PF(consulta.getString("Local_PF"));
                funcion.setReg_PFUF(consulta.getString("Reg_PFUF"));
                funcion.setReg_PFDt(consulta.getString("Reg_PFDt"));
                funcion.setCarNacVig(consulta.getString("CarNacVig"));
                funcion.setDtValCNV(consulta.getString("DtValCNVC"));
                funcion.setDtEmissaoCNV(consulta.getString("DtEmissaoCNVC"));
                funcion.setCadastroAFIS(consulta.getString("CadastroAFIS"));
                funcion.setReg_MT(consulta.getString("Reg_MT"));
                funcion.setDt_Recicl(consulta.getString("Dt_ReciclC"));
                funcion.setDt_VenCurs(consulta.getString("Dt_VenCursC"));
                funcion.setDt_ExameMe(consulta.getString("Dt_ExameMeC"));
                funcion.setDt_Psico(consulta.getString("Dt_PsicoC"));
                funcion.setExtensaoTV(consulta.getString("ExtensaoTV"));
                funcion.setExtSegPes(consulta.getString("ExtSegPes"));
                funcion.setExtEscolta(consulta.getString("ExtEscolta"));
                funcion.setExtGrdEventos(consulta.getString("ExtGrdEventos"));
                funcion.setExtArmasNLetais(consulta.getString("ExtArmasNLetais"));
                funcion.setGrupoSang(consulta.getString("GrupoSang"));
                funcion.setInstrucao(consulta.getString("Instrucao"));
                funcion.setRaca(consulta.getString("Raca"));
                funcion.setEstCivil(consulta.getString("EstCivil"));
                funcion.setEndereco(consulta.getString("Endereco"));
                funcion.setNumero(consulta.getString("Numero"));
                funcion.setComplemento(consulta.getString("Complemento"));
                funcion.setBairro(consulta.getString("Bairro"));
                funcion.setCidade(consulta.getString("Cidade"));
                funcion.setUF(consulta.getString("UF"));
                funcion.setCEP(consulta.getString("CEP"));
                funcion.setFone1(consulta.getString("Fone1"));
                funcion.setFone2(consulta.getString("Fone2"));
                funcion.setEmail(consulta.getString("Email"));
                funcion.setDt_Nasc(consulta.getString("Dt_NascC"));
                funcion.setSexo(consulta.getString("Sexo"));
                funcion.setNaturalid(consulta.getString("Naturalid"));
                funcion.setPai(consulta.getString("Pai"));
                funcion.setMae(consulta.getString("Mae"));
                funcion.setConjuge(consulta.getString("Conjuge"));
                funcion.setCNH(consulta.getString("CNH"));
                funcion.setDt_VenCNH(consulta.getString("Dt_VenCNHC"));
                funcion.setUF_CNH(consulta.getString("UF_CNH"));
                funcion.setCategoria(consulta.getString("Categoria"));
                funcion.setRG(consulta.getString("RG"));
                funcion.setOrgEmis(consulta.getString("OrgEmis"));
                funcion.setRgDtEmis(consulta.getString("RgDtEmis"));
                funcion.setCPF(consulta.getString("CPF"));
                funcion.setPIS(consulta.getString("PIS"));
                funcion.setReservista(consulta.getString("Reservista"));
                funcion.setReservCat(consulta.getString("ReservCat"));
                funcion.setCTPS_Nro(consulta.getString("CTPS_Nro"));
                funcion.setCTPS_Serie(consulta.getString("CTPS_Serie"));
                funcion.setCTPS_UF(consulta.getString("CTPS_UF"));
                funcion.setCTPS_Emis(consulta.getString("CTPS_Emis"));
                funcion.setTitEleit(consulta.getString("TitEleit"));
                funcion.setTitEZona(consulta.getString("TitEZona"));
                funcion.setTitSecao(consulta.getString("TitSecao"));
                funcion.setCt_Banco(consulta.getString("Ct_Banco"));
                funcion.setCt_Agencia(consulta.getString("Ct_Agencia"));
                funcion.setCt_Conta(consulta.getString("Ct_Conta"));
                funcion.setCt_CodOper(consulta.getString("Ct_CodOper"));
                funcion.setObs(consulta.getString("Obs"));
                funcion.setSalario(consulta.getString("Salario"));
                funcion.setSindicato(consulta.getString("Sindicato"));
                funcion.setCHMes(consulta.getString("CHMes"));
                funcion.setCHSeman(consulta.getString("CHSeman"));
                funcion.setHe_Periodo(consulta.getString("He_Periodo"));
                funcion.setDepIR(consulta.getString("DepIR"));
                funcion.setDepSF(consulta.getString("DepSF"));
                funcion.setFGTSOpcao(consulta.getString("FGTSOpcao"));
                funcion.setFGTSBanco(consulta.getString("FGTSBanco"));
                funcion.setFGTSAg(consulta.getString("FGTSAg"));
                funcion.setPgCtSin(consulta.getString("PgCtSin"));
                funcion.setAssMedic(consulta.getString("AssMedic"));
                funcion.setDepAssMed(consulta.getString("DepAssMed"));
                funcion.setCestaBas(consulta.getString("CestaBas"));
                funcion.setValeRef(consulta.getString("ValeRef"));
                funcion.setConvFarma(consulta.getString("ConvFarma"));
                funcion.setSegVida(consulta.getString("SegVida"));
                funcion.setTipoAdm(consulta.getString("TipoAdm"));
                funcion.setDefFis(consulta.getString("DefFis"));
                funcion.setDefFisTipo(consulta.getString("DefFisTipo"));
                funcion.setDefFisDesc(consulta.getString("DefFisDesc"));
                funcion.setNacionalid(consulta.getString("Nacionalid"));
                funcion.setAnoCheg(consulta.getString("AnoCheg"));
                funcion.setFolhaLivro(consulta.getString("FolhaLivro"));
                funcion.setPgINSS(consulta.getString("PgINSS"));
                funcion.setPgIR(consulta.getString("PgIR"));
                funcion.setSEFIPOcor(consulta.getString("SEFIPOcor"));
                funcion.setConta_Ctb(consulta.getString("Conta_Ctb"));
                funcion.setAltura(consulta.getString("Altura"));
                funcion.setPeso(consulta.getString("Peso"));
                funcion.setDt_Demis(consulta.getString("Dt_DemisC"));
                funcion.setCodCidade(consulta.getString("CodCidade"));
                funcion.setCodNaturalid(consulta.getString("CodNaturalid"));
                funcion.setExpGESP(consulta.getString("ExpGESP"));
                funcion.setVinculo(consulta.getString("Vinculo"));
                funcion.setFormaPgto(consulta.getString("FormaPgto"));
                funcion.setJornada(consulta.getString("Jornada"));
                funcion.setSegDesemp(consulta.getString("SegDesemp"));
                funcion.setFPAdiant(consulta.getString("FPAdiant"));
                funcion.setCodAlimentacao(consulta.getString("CodAlimentacao"));
                funcion.setChavebancaria(consulta.getString("Chavebancaria"));
                funcion.setCodPessoaWeb(consulta.getBigDecimal("CodPessoaWeb"));
                funcion.setInterfExt(consulta.getString("InterfExt"));
                funcion.setCod_ExameCNH(consulta.getString("Cod_ExameCNH"));
                funcion.setDt_ExameCNH(consulta.getString("Dt_ExameCNHC"));
                funcion.setCNPJ_LabExame(consulta.getString("CNPJ_LabExame"));
                funcion.setUF_ExameCNH(consulta.getString("UF_ExameCNH"));
                funcion.setCRM_ExamCNH(consulta.getString("CRM_ExamCNH"));
                funcion.setTrabParcial(consulta.getString("TrabParcial"));
                funcion.setTeletrabalho(consulta.getString("Teletrabalho"));
                funcion.setTrabIntermitente(consulta.getString("TrabIntermitente"));
                funcion.setOperador(consulta.getString("Operador"));
                funcion.setDt_Alter(consulta.getString("Dt_AlterC"));
                funcion.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(funcion);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.selecionarFuncion - " + e.getMessage() + "\r\n"
                    + "SELECT \n"
                    + "     *, \n"
                    + "     CONVERT(VarChar, Dt_Admis, 112) Dt_AdmisC, \n"
                    + "     CONVERT(VarChar, Dt_Situac, 112) Dt_SituacC, \n"
                    + "     CONVERT(VarChar, Dt_FormIni, 112) Dt_FormIniC, \n"
                    + "     CONVERT(VarChar, Dt_FormFim, 112) Dt_FormFimC, \n"
                    + "     CONVERT(VarChar, DtValCNV, 112) DtValCNVC, \n"
                    + "     CONVERT(VarChar, DtEmissaoCNV, 112) DtEmissaoCNVC, \n"
                    + "     CONVERT(VarChar, Dt_Recicl, 112) Dt_ReciclC, \n"
                    + "     CONVERT(VarChar, Dt_VenCurs, 112) Dt_VenCursC, \n"
                    + "     CONVERT(VarChar, Dt_ExameMe, 112) Dt_ExameMeC, \n"
                    + "     CONVERT(VarChar, Dt_Psico, 112) Dt_PsicoC, \n"
                    + "     CONVERT(VarChar, Dt_Nasc, 112) Dt_NascC, \n"
                    + "     CONVERT(VarChar, Dt_VenCNH, 112) Dt_VenCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Demis, 112) Dt_DemisC, \n"
                    + "     CONVERT(VarChar, Dt_ExameCNH, 112) Dt_ExameCNHC, \n"
                    + "     CONVERT(VarChar, Dt_Alter, 112) Dt_AlterC \n"
                    + " FROM \n"
                    + "     Funcion \n"
                    + " WHERE \n"
                    + "     CodFil = " + codFil + "\n");
        }
    }

    /**
     * INSERT completo pra tabela funcion
     *
     * @param funcion
     * @param persistencia
     * @throws Exception
     */
    public void inserirFuncion(Funcion funcion, Persistencia persistencia) throws Exception {
        try {

            /**
             * ********************************************
             * // TRATAMENTO DE CAMPOS OBRIGATÓRIOS - Inicio
             * ********************************************
             */
            if (null == funcion.getSituacao() || funcion.getSituacao().isEmpty()) {
                funcion.setSituacao("A");
            }

            if (null == funcion.getVinculo() || funcion.getVinculo().isEmpty()) {
                funcion.setVinculo("F");
            }

            if (null == funcion.getSindicato() || funcion.getSindicato().isEmpty()) {
                funcion.setSindicato("0001");
            }

            if (funcion.getHorario() == 0) {
                funcion.setHorario(1);
            }
            /* FIM *****************************************/

            String sql = " INSERT INTO Funcion (Matr, Nome, Nome_Guer, CodFil, Regional, Secao, CCusto, CodPonto, \n"
                    + "Dt_Admis, Cargo, CodCargo, Apresen, Situacao, Dt_Situac, Escala, Horario, GrpEscala, Funcao, \n"
                    + "Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Local_PF, Reg_PFUF, Reg_PFDt, CarNacVig, \n"
                    + "DtValCNV, DtEmissaoCNV, CadastroAFIS, Reg_MT, Dt_Recicl, Dt_VenCurs, Dt_ExameMe, Dt_Psico, ExtensaoTV, \n"
                    + "ExtSegPes, ExtEscolta, ExtGrdEventos, ExtArmasNLetais, GrupoSang, Instrucao, Raca, EstCivil, Endereco, \n"
                    + "Numero, Complemento, Bairro, Cidade, UF, CEP, Fone1, Fone2, Email, Dt_Nasc, Sexo, Naturalid, Pai, Mae, \n"
                    + "Conjuge, CNH, Dt_VenCNH, UF_CNH, Categoria, RG, OrgEmis, RgDtEmis, CPF, PIS, Reservista, ReservCat, \n"
                    + "CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, TitEleit, TitEZona, TitSecao, Ct_Banco, Ct_Agencia, Ct_Conta, \n"
                    + "Ct_CodOper, Obs, Salario, Sindicato, CHMes, CHSeman, He_Periodo, DepIR, DepSF, FGTSOpcao, FGTSBanco, FGTSAg, \n"
                    + "PgCtSin, AssMedic, DepAssMed, CestaBas, ValeRef, ConvFarma, SegVida, TipoAdm, DefFis, DefFisTipo, DefFisDesc, \n"
                    + "Nacionalid, AnoCheg, FolhaLivro, PgINSS, PgIR, SEFIPOcor, Conta_Ctb, Altura, Peso, Dt_Demis, CodCidade, \n"
                    + "CodNaturalid, ExpGESP, Vinculo, FormaPgto, Jornada, SegDesemp, FPAdiant, CodAlimentacao, Chavebancaria, \n"
                    + "CodPessoaWeb, InterfExt, Cod_ExameCNH, Dt_ExameCNH, CNPJ_LabExame, UF_ExameCNH, CRM_ExamCNH, TrabParcial, \n"
                    + "Teletrabalho, TrabIntermitente, Operador, Dt_Alter, Hr_Alter)"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, \n"
                    + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(funcion.getMatr());
            consulta.setString(funcion.getNome());
            consulta.setString(funcion.getNome_Guer());
            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setInt(funcion.getRegional());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCCusto());
            consulta.setString(funcion.getCodPonto());
            consulta.setString(funcion.getDt_Admis());
            consulta.setString(funcion.getCargo());
            consulta.setBigDecimal(funcion.getCodCargo());
            consulta.setString(funcion.getApresen());
            consulta.setString(funcion.getSituacao());
            consulta.setString(funcion.getDt_Situac());
            consulta.setString(funcion.getEscala());
            consulta.setInt(funcion.getHorario());
            consulta.setInt(funcion.getGrpEscala());
            consulta.setString(funcion.getFuncao());
            consulta.setString(funcion.getDt_FormIni());
            consulta.setString(funcion.getDt_FormFim());
            consulta.setString(funcion.getLocalForm());
            consulta.setString(funcion.getCertific());
            consulta.setString(funcion.getReg_PF());
            consulta.setString(funcion.getLocal_PF());
            consulta.setString(funcion.getReg_PFUF());
            consulta.setString(funcion.getReg_PFDt());
            consulta.setString(funcion.getCarNacVig());
            consulta.setString(funcion.getDtValCNV());
            consulta.setString(funcion.getDtEmissaoCNV());
            consulta.setString(funcion.getCadastroAFIS());
            consulta.setString(funcion.getReg_MT());
            consulta.setString(funcion.getDt_Recicl());
            consulta.setString(funcion.getDt_VenCurs());
            consulta.setString(funcion.getDt_ExameMe());
            consulta.setString(funcion.getDt_Psico());
            consulta.setString(funcion.getExtensaoTV());
            consulta.setString(funcion.getExtSegPes());
            consulta.setString(funcion.getExtEscolta());
            consulta.setString(funcion.getExtGrdEventos());
            consulta.setString(funcion.getExtArmasNLetais());
            consulta.setString(funcion.getGrupoSang());
            consulta.setString(funcion.getInstrucao());
            consulta.setString(funcion.getRaca());
            consulta.setString(funcion.getEstCivil());
            consulta.setString(funcion.getEndereco());
            consulta.setString(funcion.getNumero());
            consulta.setString(funcion.getComplemento());
            consulta.setString(funcion.getBairro());
            consulta.setString(funcion.getCidade());
            consulta.setString(funcion.getUF());
            consulta.setString(funcion.getCEP());
            consulta.setString(funcion.getFone1());
            consulta.setString(funcion.getFone2());
            consulta.setString(funcion.getEmail());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getSexo());
            consulta.setString(funcion.getNaturalid());
            consulta.setString(funcion.getPai());
            consulta.setString(funcion.getMae());
            consulta.setString(funcion.getConjuge());
            consulta.setString(funcion.getCNH());
            consulta.setString(funcion.getDt_VenCNH());
            consulta.setString(funcion.getUF_CNH());
            consulta.setString(funcion.getCategoria());
            consulta.setString(funcion.getRG());
            consulta.setString(funcion.getOrgEmis());
            consulta.setString(funcion.getRgDtEmis());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getPIS());
            consulta.setString(funcion.getReservista());
            consulta.setString(funcion.getReservCat());
            consulta.setString(funcion.getCTPS_Nro());
            consulta.setString(funcion.getCTPS_Serie());
            consulta.setString(funcion.getCTPS_UF());
            consulta.setString(funcion.getCTPS_Emis());
            consulta.setString(funcion.getTitEleit());
            consulta.setString(funcion.getTitEZona());
            consulta.setString(funcion.getTitSecao());
            consulta.setString(funcion.getCt_Banco());
            consulta.setString(funcion.getCt_Agencia());
            consulta.setString(funcion.getCt_Conta());
            consulta.setString(funcion.getCt_CodOper());
            consulta.setString(funcion.getObs());
            consulta.setBigDecimal(funcion.getSalario());
            consulta.setString(funcion.getSindicato());
            consulta.setBigDecimal(funcion.getCHMes());
            consulta.setBigDecimal(funcion.getCHSeman());
            consulta.setBigDecimal(funcion.getHe_Periodo());
            consulta.setString(funcion.getDepIR());
            consulta.setString(funcion.getDepSF());
            consulta.setString(funcion.getFGTSOpcao());
            consulta.setString(funcion.getFGTSBanco());
            consulta.setString(funcion.getFGTSAg());
            consulta.setString(funcion.getPgCtSin());
            consulta.setString(funcion.getAssMedic());
            consulta.setString(funcion.getDepAssMed());
            consulta.setString(funcion.getCestaBas());
            consulta.setString(funcion.getValeRef());
            consulta.setString(funcion.getConvFarma());
            consulta.setString(funcion.getSegVida());
            consulta.setString(funcion.getTipoAdm());
            consulta.setString(funcion.getDefFis());
            consulta.setString(funcion.getDefFisTipo());
            consulta.setString(funcion.getDefFisDesc());
            consulta.setString(funcion.getNacionalid());
            consulta.setString(funcion.getAnoCheg());
            consulta.setString(funcion.getFolhaLivro());
            consulta.setString(funcion.getPgINSS());
            consulta.setString(funcion.getPgIR());
            consulta.setString(funcion.getSEFIPOcor());
            consulta.setString(funcion.getConta_Ctb());
            consulta.setBigDecimal(funcion.getAltura());
            consulta.setBigDecimal(funcion.getPeso());
            consulta.setString(funcion.getDt_Demis());
            consulta.setBigDecimal(funcion.getCodCidade());
            consulta.setBigDecimal(funcion.getCodNaturalid());
            consulta.setString(funcion.getExpGESP());
            consulta.setString(funcion.getVinculo());
            consulta.setString(funcion.getFormaPgto());
            consulta.setBigDecimal(funcion.getJornada());
            consulta.setString(funcion.getSegDesemp());
            consulta.setString(funcion.getFPAdiant());
            consulta.setString(funcion.getCodAlimentacao());
            consulta.setString(funcion.getChavebancaria());
            consulta.setBigDecimal(funcion.getCodPessoaWeb());
            consulta.setString(funcion.getInterfExt());
            consulta.setString(funcion.getCod_ExameCNH());
            consulta.setString(funcion.getDt_ExameCNH());
            consulta.setString(funcion.getCNPJ_LabExame());
            consulta.setString(funcion.getUF_ExameCNH());
            consulta.setString(funcion.getCRM_ExamCNH());
            consulta.setString(funcion.getTrabParcial());
            consulta.setString(funcion.getTeletrabalho());
            consulta.setString(funcion.getTrabIntermitente());
            consulta.setString(funcion.getOperador());
            consulta.setString(funcion.getDt_Alter());
            consulta.setString(funcion.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.inserirFuncion - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Funcion (Matr, Nome, Nome_Guer, CodFil, Regional, Secao, CCusto, CodPonto, \n"
                    + "Dt_Admis, Cargo, CodCargo, Apresen, Situacao, Dt_Situac, Escala, Horario, GrpEscala, Funcao, \n"
                    + "Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Local_PF, Reg_PFUF, Reg_PFDt, CarNacVig, \n"
                    + "DtValCNV, DtEmissaoCNV, CadastroAFIS, Reg_MT, Dt_Recicl, Dt_VenCurs, Dt_ExameMe, Dt_Psico, ExtensaoTV, \n"
                    + "ExtSegPes, ExtEscolta, ExtGrdEventos, ExtArmasNLetais, GrupoSang, Instrucao, Raca, EstCivil, Endereco, \n"
                    + "Numero, Complemento, Bairro, Cidade, UF, CEP, Fone1, Fone2, Email, Dt_Nasc, Sexo, Naturalid, Pai, Mae, \n"
                    + "Conjuge, CNH, Dt_VenCNH, UF_CNH, Categoria, RG, OrgEmis, RgDtEmis, CPF, PIS, Reservista, ReservCat, \n"
                    + "CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, TitEleit, TitEZona, TitSecao, Ct_Banco, Ct_Agencia, Ct_Conta, \n"
                    + "Ct_CodOper, Obs, Salario, Sindicato, CHMes, CHSeman, He_Periodo, DepIR, DepSF, FGTSOpcao, FGTSBanco, FGTSAg, \n"
                    + "PgCtSin, AssMedic, DepAssMed, CestaBas, ValeRef, ConvFarma, SegVida, TipoAdm, DefFis, DefFisTipo, DefFisDesc, \n"
                    + "Nacionalid, AnoCheg, FolhaLivro, PgINSS, PgIR, SEFIPOcor, Conta_Ctb, Altura, Peso, Dt_Demis, CodCidade, \n"
                    + "CodNaturalid, ExpGESP, Vinculo, FormaPgto, Jornada, SegDesemp, FPAdiant, CodAlimentacao, Chavebancaria, \n"
                    + "CodPessoaWeb, InterfExt, Cod_ExameCNH, Dt_ExameCNH, CNPJ_LabExame, UF_ExameCNH, CRM_ExamCNH, TrabParcial, \n"
                    + "Teletrabalho, TrabIntermitente, Operador, Dt_Alter, Hr_Alter)"
                    + " VALUES (" + funcion.getMatr() + ", " + funcion.getNome() + ", " + funcion.getNome_Guer() + ", " + funcion.getCodFil() + ", "
                    + funcion.getRegional() + ", " + funcion.getSecao() + ", " + funcion.getCCusto() + ", " + funcion.getCodPonto() + ", "
                    + funcion.getDt_Admis() + ", " + funcion.getCargo() + ", " + funcion.getCodCargo() + ", " + funcion.getApresen() + ", "
                    + funcion.getSituacao() + ", " + funcion.getDt_Situac() + ", " + funcion.getEscala() + ", " + funcion.getHorario() + ", "
                    + funcion.getGrpEscala() + ", " + funcion.getFuncao() + ", " + funcion.getDt_FormIni() + ", " + funcion.getDt_FormFim() + ", "
                    + funcion.getLocalForm() + ", " + funcion.getCertific() + ", " + funcion.getReg_PF() + ", " + funcion.getLocal_PF() + ", "
                    + funcion.getReg_PFUF() + ", " + funcion.getReg_PFDt() + ", " + funcion.getCarNacVig() + ", " + funcion.getDtValCNV() + ", "
                    + funcion.getDtEmissaoCNV() + ", " + funcion.getCadastroAFIS() + ", " + funcion.getReg_MT() + ", " + funcion.getDt_Recicl() + ", "
                    + funcion.getDt_VenCurs() + ", " + funcion.getDt_ExameMe() + ", " + funcion.getDt_Psico() + ", " + funcion.getExtensaoTV() + ", "
                    + funcion.getExtSegPes() + ", " + funcion.getExtEscolta() + ", " + funcion.getExtGrdEventos() + ", " + funcion.getExtArmasNLetais() + ", "
                    + funcion.getGrupoSang() + ", " + funcion.getInstrucao() + ", " + funcion.getRaca() + ", " + funcion.getEstCivil() + ", "
                    + funcion.getEndereco() + ", " + funcion.getNumero() + ", " + funcion.getComplemento() + ", " + funcion.getBairro() + ", "
                    + funcion.getCidade() + ", " + funcion.getUF() + ", " + funcion.getCEP() + ", " + funcion.getFone1() + ", " + funcion.getFone2() + ", "
                    + funcion.getEmail() + ", " + funcion.getDt_Nasc() + ", " + funcion.getSexo() + ", " + funcion.getNaturalid() + ", " + funcion.getPai() + ", "
                    + funcion.getMae() + ", " + funcion.getConjuge() + ", " + funcion.getCNH() + ", " + funcion.getDt_VenCNH() + ", " + funcion.getUF_CNH() + ", "
                    + funcion.getCategoria() + ", " + funcion.getRG() + ", " + funcion.getOrgEmis() + ", " + funcion.getRgDtEmis() + ", " + funcion.getCPF() + ", "
                    + funcion.getPIS() + ", " + funcion.getReservista() + ", " + funcion.getReservCat() + ", " + funcion.getCTPS_Nro() + ", "
                    + funcion.getCTPS_Serie() + ", " + funcion.getCTPS_UF() + ", " + funcion.getCTPS_Emis() + ", " + funcion.getTitEleit() + ", "
                    + funcion.getTitEZona() + ", " + funcion.getTitSecao() + ", " + funcion.getCt_Banco() + ", " + funcion.getCt_Agencia() + ", "
                    + funcion.getCt_Conta() + ", " + funcion.getCt_CodOper() + ", " + funcion.getObs() + ", " + funcion.getSalario() + ", "
                    + funcion.getSindicato() + ", " + funcion.getCHMes() + ", " + funcion.getCHSeman() + ", " + funcion.getHe_Periodo() + ", "
                    + funcion.getDepIR() + ", " + funcion.getDepSF() + ", " + funcion.getFGTSOpcao() + ", " + funcion.getFGTSBanco() + ", "
                    + funcion.getFGTSAg() + ", " + funcion.getPgCtSin() + ", " + funcion.getAssMedic() + ", " + funcion.getDepAssMed() + ", "
                    + funcion.getCestaBas() + ", " + funcion.getValeRef() + ", " + funcion.getConvFarma() + ", " + funcion.getSegVida() + ", "
                    + funcion.getTipoAdm() + ", " + funcion.getDefFis() + ", " + funcion.getDefFisTipo() + ", " + funcion.getDefFisDesc() + ", "
                    + funcion.getNacionalid() + ", " + funcion.getAnoCheg() + ", " + funcion.getFolhaLivro() + ", " + funcion.getPgINSS() + ", "
                    + funcion.getPgIR() + ", " + funcion.getSEFIPOcor() + ", " + funcion.getConta_Ctb() + ", " + funcion.getAltura() + ", " + funcion.getPeso() + ", "
                    + funcion.getDt_Demis() + ", " + funcion.getCodCidade() + ", " + funcion.getCodNaturalid() + ", " + funcion.getExpGESP() + ", "
                    + funcion.getVinculo() + ", " + funcion.getFormaPgto() + ", " + funcion.getJornada() + ", " + funcion.getSegDesemp() + ", "
                    + funcion.getFPAdiant() + ", " + funcion.getCodAlimentacao() + ", " + funcion.getChavebancaria() + ", " + funcion.getCodPessoaWeb() + ", "
                    + funcion.getInterfExt() + ", " + funcion.getCod_ExameCNH() + ", " + funcion.getDt_ExameCNH() + ", " + funcion.getCNPJ_LabExame() + ", "
                    + funcion.getUF_ExameCNH() + ", " + funcion.getCRM_ExamCNH() + ", " + funcion.getTrabParcial() + ", " + funcion.getTeletrabalho() + ", "
                    + funcion.getTrabIntermitente() + ", " + funcion.getOperador() + ", " + funcion.getDt_Alter() + ", " + funcion.getHr_Alter());
        }
    }

    public void atualizarFuncion(Funcion funcion, Persistencia persistencia) throws Exception {
        try {
            /**
             * ********************************************
             * // TRATAMENTO DE CAMPOS OBRIGATÓRIOS - Inicio
             * ********************************************
             */
            if (null == funcion.getSituacao() || funcion.getSituacao().isEmpty()) {
                funcion.setSituacao("A");
            }

            if (null == funcion.getVinculo() || funcion.getVinculo().isEmpty()) {
                funcion.setVinculo("F");
            }

            if (null == funcion.getSindicato() || funcion.getSindicato().isEmpty()) {
                funcion.setSindicato("0001");
            }

            if (funcion.getHorario() == 0) {
                funcion.setHorario(1);
            }
            /* FIM *****************************************/

            String sql = "UPDATE \n"
                    + "    Funcion \n"
                    + "SET Nome = ?, Nome_Guer = ?, CodFil = ?, Regional = ?, Secao = ?, CCusto = ?, CodPonto = ?, \n"
                    + "    Dt_Admis = ?, Cargo = ?, CodCargo = ?, Apresen = ?, Situacao = ?, Dt_Situac = ?, Escala = ?,\n"
                    + "    Horario = ?, GrpEscala = ?, Funcao = ?, Dt_FormIni = ?, Dt_FormFim = ?, LocalForm = ?, \n"
                    + "    Certific = ?, Reg_PF = ?, Local_PF = ?, Reg_PFUF = ?, Reg_PFDt = ?, CarNacVig = ?, \n"
                    + "    DtValCNV = ?, DtEmissaoCNV = ?, CadastroAFIS = ?, Reg_MT = ?, Dt_Recicl = ?, Dt_VenCurs = ?,\n"
                    + "    Dt_ExameMe = ?, Dt_Psico = ?, ExtensaoTV = ?, ExtSegPes = ?, ExtEscolta = ?, ExtGrdEventos = ?,\n"
                    + "    ExtArmasNLetais = ?, GrupoSang = ?, Instrucao = ?, Raca = ?, EstCivil = ?, Endereco = ?, \n"
                    + "    Numero = ?, Complemento = ?, Bairro = ?, Cidade = ?, UF = ?, CEP = ?, Fone1 = ?, Fone2 = ?,\n"
                    + "    Email = ?, Dt_Nasc = ?, Sexo = ?, Naturalid = ?, Pai = ?, Mae = ?, Conjuge = ?, CNH = ?,\n"
                    + "    Dt_VenCNH = ?, UF_CNH = ?, Categoria = ?, RG = ?, OrgEmis = ?, RgDtEmis = ?, CPF = ?, \n"
                    + "    PIS = ?, Reservista = ?, ReservCat = ?, CTPS_Nro = ?, CTPS_Serie = ?, CTPS_UF = ?, \n"
                    + "    CTPS_Emis = ?, TitEleit = ?, TitEZona = ?, TitSecao = ?, Ct_Banco = ?, Ct_Agencia = ?, \n"
                    + "    Ct_Conta = ?, Ct_CodOper = ?, Obs = ?, Salario = ?, Sindicato = ?, CHMes = ?, CHSeman = ?,\n"
                    + "    He_Periodo = ?, DepIR = ?, DepSF = ?, FGTSOpcao = ?, FGTSBanco = ?, FGTSAg = ?, PgCtSin = ?,\n"
                    + "    AssMedic = ?, DepAssMed = ?, CestaBas = ?, ValeRef = ?, ConvFarma = ?, SegVida = ?, TipoAdm = ?,\n"
                    + "    DefFis = ?, DefFisTipo = ?, DefFisDesc = ?, Nacionalid = ?, AnoCheg = ?, FolhaLivro = ?,\n"
                    + "    PgINSS = ?, PgIR = ?, SEFIPOcor = ?, Conta_Ctb = ?, Altura = ?, Peso = ?, Dt_Demis = ?,\n"
                    + "    CodCidade = ?, CodNaturalid = ?, ExpGESP = ?, Vinculo = ?, FormaPgto = ?, Jornada = ?, \n"
                    + "    SegDesemp = ?, FPAdiant = ?, CodAlimentacao = ?, Chavebancaria = ?, CodPessoaWeb = ?, \n"
                    + "    InterfExt = ?, Cod_ExameCNH = ?, Dt_ExameCNH = ?, CNPJ_LabExame = ?, UF_ExameCNH = ?, \n"
                    + "    CRM_ExamCNH = ?, TrabParcial = ?, Teletrabalho = ?, TrabIntermitente = ?, Operador = ?, \n"
                    + "    Dt_Alter = ?, Hr_Alter = ?\n"
                    + "WHERE\n"
                    + "    Matr = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            // SET
            consulta.setString(funcion.getNome());
            consulta.setString(funcion.getNome_Guer());
            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setInt(funcion.getRegional());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCCusto());
            consulta.setString(funcion.getCodPonto());
            consulta.setString(funcion.getDt_Admis());
            consulta.setString(funcion.getCargo());
            consulta.setBigDecimal(funcion.getCodCargo());
            consulta.setString(funcion.getApresen());
            consulta.setString(funcion.getSituacao());
            consulta.setString(funcion.getDt_Situac());
            consulta.setString(funcion.getEscala());
            consulta.setInt(funcion.getHorario());
            consulta.setInt(funcion.getGrpEscala());
            consulta.setString(funcion.getFuncao());
            consulta.setString(funcion.getDt_FormIni());
            consulta.setString(funcion.getDt_FormFim());
            consulta.setString(funcion.getLocalForm());
            consulta.setString(funcion.getCertific());
            consulta.setString(funcion.getReg_PF());
            consulta.setString(funcion.getLocal_PF());
            consulta.setString(funcion.getReg_PFUF());
            consulta.setString(funcion.getReg_PFDt());
            consulta.setString(funcion.getCarNacVig());
            consulta.setString(funcion.getDtValCNV());
            consulta.setString(funcion.getDtEmissaoCNV());
            consulta.setString(funcion.getCadastroAFIS());
            consulta.setString(funcion.getReg_MT());
            consulta.setString(funcion.getDt_Recicl());
            consulta.setString(funcion.getDt_VenCurs());
            consulta.setString(funcion.getDt_ExameMe());
            consulta.setString(funcion.getDt_Psico());
            consulta.setString(funcion.getExtensaoTV());
            consulta.setString(funcion.getExtSegPes());
            consulta.setString(funcion.getExtEscolta());
            consulta.setString(funcion.getExtGrdEventos());
            consulta.setString(funcion.getExtArmasNLetais());
            consulta.setString(funcion.getGrupoSang());
            consulta.setString(funcion.getInstrucao());
            consulta.setString(funcion.getRaca());
            consulta.setString(funcion.getEstCivil());
            consulta.setString(funcion.getEndereco());
            consulta.setString(funcion.getNumero());
            consulta.setString(funcion.getComplemento());
            consulta.setString(funcion.getBairro());
            consulta.setString(funcion.getCidade());
            consulta.setString(funcion.getUF());
            consulta.setString(funcion.getCEP());
            consulta.setString(funcion.getFone1());
            consulta.setString(funcion.getFone2());
            consulta.setString(funcion.getEmail());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getSexo());
            consulta.setString(funcion.getNaturalid());
            consulta.setString(funcion.getPai());
            consulta.setString(funcion.getMae());
            consulta.setString(funcion.getConjuge());
            consulta.setString(funcion.getCNH());
            consulta.setString(funcion.getDt_VenCNH());
            consulta.setString(funcion.getUF_CNH());
            consulta.setString(funcion.getCategoria());
            consulta.setString(funcion.getRG());
            consulta.setString(funcion.getOrgEmis());
            consulta.setString(funcion.getRgDtEmis());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getPIS());
            consulta.setString(funcion.getReservista());
            consulta.setString(funcion.getReservCat());
            consulta.setString(funcion.getCTPS_Nro());
            consulta.setString(funcion.getCTPS_Serie());
            consulta.setString(funcion.getCTPS_UF());
            consulta.setString(funcion.getCTPS_Emis());
            consulta.setString(funcion.getTitEleit());
            consulta.setString(funcion.getTitEZona());
            consulta.setString(funcion.getTitSecao());
            consulta.setString(funcion.getCt_Banco());
            consulta.setString(funcion.getCt_Agencia());
            consulta.setString(funcion.getCt_Conta());
            consulta.setString(funcion.getCt_CodOper());
            consulta.setString(funcion.getObs());
            consulta.setBigDecimal(funcion.getSalario());
            consulta.setString(funcion.getSindicato());
            consulta.setBigDecimal(funcion.getCHMes());
            consulta.setBigDecimal(funcion.getCHSeman());
            consulta.setBigDecimal(funcion.getHe_Periodo());
            consulta.setString(funcion.getDepIR());
            consulta.setString(funcion.getDepSF());
            consulta.setString(funcion.getFGTSOpcao());
            consulta.setString(funcion.getFGTSBanco());
            consulta.setString(funcion.getFGTSAg());
            consulta.setString(funcion.getPgCtSin());
            consulta.setString(funcion.getAssMedic());
            consulta.setString(funcion.getDepAssMed());
            consulta.setString(funcion.getCestaBas());
            consulta.setString(funcion.getValeRef());
            consulta.setString(funcion.getConvFarma());
            consulta.setString(funcion.getSegVida());
            consulta.setString(funcion.getTipoAdm());
            consulta.setString(funcion.getDefFis());
            consulta.setString(funcion.getDefFisTipo());
            consulta.setString(funcion.getDefFisDesc());
            consulta.setString(funcion.getNacionalid());
            consulta.setString(funcion.getAnoCheg());
            consulta.setString(funcion.getFolhaLivro());
            consulta.setString(funcion.getPgINSS());
            consulta.setString(funcion.getPgIR());
            consulta.setString(funcion.getSEFIPOcor());
            consulta.setString(funcion.getConta_Ctb());
            consulta.setBigDecimal(funcion.getAltura());
            consulta.setBigDecimal(funcion.getPeso());
            consulta.setString(funcion.getDt_Demis());
            consulta.setBigDecimal(funcion.getCodCidade());
            consulta.setBigDecimal(funcion.getCodNaturalid());
            consulta.setString(funcion.getExpGESP());
            consulta.setString(funcion.getVinculo());
            consulta.setString(funcion.getFormaPgto());
            consulta.setBigDecimal(funcion.getJornada());
            consulta.setString(funcion.getSegDesemp());
            consulta.setString(funcion.getFPAdiant());
            consulta.setString(funcion.getCodAlimentacao());
            consulta.setString(funcion.getChavebancaria());
            consulta.setBigDecimal(funcion.getCodPessoaWeb());
            consulta.setString(funcion.getInterfExt());
            consulta.setString(funcion.getCod_ExameCNH());
            consulta.setString(funcion.getDt_ExameCNH());
            consulta.setString(funcion.getCNPJ_LabExame());
            consulta.setString(funcion.getUF_ExameCNH());
            consulta.setString(funcion.getCRM_ExamCNH());
            consulta.setString(funcion.getTrabParcial());
            consulta.setString(funcion.getTeletrabalho());
            consulta.setString(funcion.getTrabIntermitente());
            consulta.setString(funcion.getOperador());
            consulta.setString(funcion.getDt_Alter());
            consulta.setString(funcion.getHr_Alter());
            // Where
            consulta.setBigDecimal(funcion.getMatr());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.atualizarFuncion - " + e.getMessage() + "\r\n"
                    + "UPDATE \n"
                    + "    Funcion \n"
                    + "SET Nome = " + funcion.getNome() + ", Nome_Guer = " + funcion.getNome_Guer() + ", CodFil = " + funcion.getCodFil() + ", Regional = " + funcion.getRegional() + ", Secao = " + funcion.getSecao() + ", CCusto = " + funcion.getCCusto() + ", CodPonto = " + funcion.getCodPonto() + ", \n"
                    + "    Dt_Admis = " + funcion.getDt_Admis() + ", Cargo = " + funcion.getCargo() + ", CodCargo = " + funcion.getCodCargo() + ", Apresen = " + funcion.getApresen() + ", Situacao = " + funcion.getSituacao() + ", Dt_Situac = " + funcion.getDt_Situac() + ", Escala = " + funcion.getEscala() + ",\n"
                    + "    Horario = " + funcion.getHorario() + ", GrpEscala = " + funcion.getGrpEscala() + ", Funcao = " + funcion.getFuncao() + ", Dt_FormIni = " + funcion.getDt_FormIni() + ", Dt_FormFim = " + funcion.getDt_FormFim() + ", LocalForm = " + funcion.getLocalForm() + ", \n"
                    + "    Certific = " + funcion.getCertific() + ", Reg_PF = " + funcion.getReg_PF() + ", Local_PF = " + funcion.getLocal_PF() + ", Reg_PFUF = " + funcion.getReg_PFUF() + ", Reg_PFDt = " + funcion.getReg_PFDt() + ", CarNacVig = " + funcion.getCarNacVig() + ", \n"
                    + "    DtValCNV = " + funcion.getDtValCNV() + ", DtEmissaoCNV = " + funcion.getDtEmissaoCNV() + ", CadastroAFIS = " + funcion.getCadastroAFIS() + ", Reg_MT = " + funcion.getReg_MT() + ", Dt_Recicl = " + funcion.getDt_Recicl() + ", Dt_VenCurs = " + funcion.getDt_VenCurs() + ",\n"
                    + "    Dt_ExameMe = " + funcion.getDt_ExameMe() + ", Dt_Psico = " + funcion.getDt_Psico() + ", ExtensaoTV = " + funcion.getExtensaoTV() + ", ExtSegPes = " + funcion.getExtSegPes() + ", ExtEscolta = " + funcion.getExtEscolta() + ", ExtGrdEventos = " + funcion.getExtGrdEventos() + ",\n"
                    + "    ExtArmasNLetais = " + funcion.getExtArmasNLetais() + ", GrupoSang = " + funcion.getGrupoSang() + ", Instrucao = " + funcion.getInstrucao() + ", Raca = " + funcion.getRaca() + ", EstCivil = " + funcion.getEstCivil() + ", Endereco = " + funcion.getEndereco() + ", \n"
                    + "    Numero = " + funcion.getNumero() + ", Complemento = " + funcion.getComplemento() + ", Bairro = " + funcion.getBairro() + ", Cidade = " + funcion.getCidade() + ", UF = " + funcion.getUF() + ", CEP = " + funcion.getCEP() + ", Fone1 = " + funcion.getFone1() + ", Fone2 = " + funcion.getFone2() + ",\n"
                    + "    Email = " + funcion.getEmail() + ", Dt_Nasc = " + funcion.getDt_Nasc() + ", Sexo = " + funcion.getSexo() + ", Naturalid = " + funcion.getNaturalid() + ", Pai = " + funcion.getPai() + ", Mae = " + funcion.getMae() + ", Conjuge = " + funcion.getConjuge() + ", CNH = " + funcion.getCNH() + ",\n"
                    + "    Dt_VenCNH = " + funcion.getDt_VenCNH() + ", UF_CNH = " + funcion.getUF_CNH() + ", Categoria = " + funcion.getCategoria() + ", RG = " + funcion.getRG() + ", OrgEmis = " + funcion.getOrgEmis() + ", RgDtEmis = " + funcion.getRgDtEmis() + ", CPF = " + funcion.getCPF() + ", \n"
                    + "    PIS = " + funcion.getPIS() + ", Reservista = " + funcion.getReservista() + ", ReservCat = " + funcion.getReservCat() + ", CTPS_Nro = " + funcion.getCTPS_Nro() + ", CTPS_Serie = " + funcion.getCTPS_Serie() + ", CTPS_UF = " + funcion.getCTPS_UF() + ", \n"
                    + "    CTPS_Emis = " + funcion.getCTPS_Emis() + ", TitEleit = " + funcion.getTitEleit() + ", TitEZona = " + funcion.getTitEZona() + ", TitSecao = " + funcion.getTitSecao() + ", Ct_Banco = " + funcion.getCt_Banco() + ", Ct_Agencia = " + funcion.getCt_Agencia() + ", \n"
                    + "    Ct_Conta = " + funcion.getCt_Conta() + ", Ct_CodOper = " + funcion.getCt_CodOper() + ", Obs = " + funcion.getObs() + ", Salario = " + funcion.getSalario() + ", Sindicato = " + funcion.getSindicato() + ", CHMes = " + funcion.getCHMes() + ", CHSeman = " + funcion.getCHSeman() + ",\n"
                    + "    He_Periodo = " + funcion.getHe_Periodo() + ", DepIR = " + funcion.getDepIR() + ", DepSF = " + funcion.getDepSF() + ", FGTSOpcao = " + funcion.getFGTSOpcao() + ", FGTSBanco = " + funcion.getFGTSBanco() + ", FGTSAg = " + funcion.getFGTSAg() + ", PgCtSin = " + funcion.getPgCtSin() + ",\n"
                    + "    AssMedic = " + funcion.getAssMedic() + ", DepAssMed = " + funcion.getDepAssMed() + ", CestaBas = " + funcion.getCestaBas() + ", ValeRef = " + funcion.getValeRef() + ", ConvFarma = " + funcion.getConvFarma() + ", SegVida = " + funcion.getSegVida() + ", TipoAdm = " + funcion.getTipoAdm() + ",\n"
                    + "    DefFis = " + funcion.getDefFis() + ", DefFisTipo = " + funcion.getDefFisTipo() + ", DefFisDesc = " + funcion.getDefFisDesc() + ", Nacionalid = " + funcion.getNacionalid() + ", AnoCheg = " + funcion.getAnoCheg() + ", FolhaLivro = " + funcion.getFolhaLivro() + ",\n"
                    + "    PgINSS = " + funcion.getPgINSS() + ", PgIR = " + funcion.getPgIR() + ", SEFIPOcor = " + funcion.getSEFIPOcor() + ", Conta_Ctb = " + funcion.getConta_Ctb() + ", Altura = " + funcion.getAltura() + ", Peso = " + funcion.getPeso() + ", Dt_Demis = " + funcion.getDt_Demis() + ",\n"
                    + "    CodCidade = " + funcion.getCodCidade() + ", CodNaturalid = " + funcion.getCodNaturalid() + ", ExpGESP = " + funcion.getExpGESP() + ", Vinculo = " + funcion.getVinculo() + ", FormaPgto = " + funcion.getFormaPgto() + ", Jornada = " + funcion.getJornada() + ", \n"
                    + "    SegDesemp = " + funcion.getSegDesemp() + ", FPAdiant = " + funcion.getFPAdiant() + ", CodAlimentacao = " + funcion.getCodAlimentacao() + ", Chavebancaria = " + funcion.getChavebancaria() + ", CodPessoaWeb = " + funcion.getCodPessoaWeb() + ", \n"
                    + "    InterfExt = " + funcion.getInterfExt() + ", Cod_ExameCNH = " + funcion.getCod_ExameCNH() + ", Dt_ExameCNH = " + funcion.getDt_ExameCNH() + ", CNPJ_LabExame = " + funcion.getCNPJ_LabExame() + ", UF_ExameCNH = " + funcion.getUF_ExameCNH() + ", \n"
                    + "    CRM_ExamCNH = " + funcion.getCRM_ExamCNH() + ", TrabParcial = " + funcion.getTrabParcial() + ", Teletrabalho = " + funcion.getTeletrabalho() + ", TrabIntermitente = " + funcion.getTrabIntermitente() + ", Operador = " + funcion.getOperador() + ", \n"
                    + "    Dt_Alter = " + funcion.getDt_Alter() + ", Hr_Alter = " + funcion.getHr_Alter() + "\n"
                    + "WHERE\n"
                    + "    Matr = " + funcion.getMatr() + "");
        }
    }

    /**
     * Retorna os funcionários de folga dado um conjunto de matrículas.
     *
     * @param matrs
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Funcion> folgas(List<String> matrs, String data, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = "Select RhPonto.*, Funcion.Nome from RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " where DtCompet =? \n"
                    + "   and Motivo Like '%FOLGA%' \n"
                    + "   and RHPonto.Matr in (";
            sql = matrs.stream().map((_item) -> " ?, ").reduce(sql, String::concat);
            sql = sql.substring(0, sql.length() - 2);
            sql += ")";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            for (String matr : matrs) {
                consulta.setString(matr);
            }
            consulta.select();
            Funcion funcion;
            while (consulta.Proximo()) {
                funcion = new Funcion();
                funcion.setMatr(consulta.getString("matr"));
                funcion.setNome_Guer(consulta.getString("Nome"));
                retorno.add(funcion);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.folgas - " + e.getMessage() + "\r\n"
                    + "Select RhPonto.*, Funcion.Nome from RHPonto \n"
                    + " Left Join Funcion on Funcion.Matr = RHPonto.Matr \n"
                    + " where DtCompet =? \n"
                    + "   and Motivo Like '%FOLGA%' \n"
                    + "   and RHPonto.Matr in (");
        }
    }

    /**
     * Busca nome do funcionário
     *
     * @param Matr - matricula
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Funcion> BuscaNome(String Matr, Persistencia persistencia) throws Exception {
        List<Funcion> retorno = new ArrayList();
        try {
            String sql = "select CodFil, Nome from funcion where matr = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Matr);
            consult.select();
            while (consult.Proximo()) {
                Funcion funcion = new Funcion();
                funcion.setCodFil(consult.getString("CodFil"));
                funcion.setMatr(Matr);
                funcion.setNome(consult.getString("Nome"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados do funcionário - " + e.getMessage());
        }
    }

    /**
     * Busca funcionário por pessoa
     *
     * @param pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Funcion> BuscaPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        List<Funcion> retorno = new ArrayList<>();
        try {
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.orgemis, "
                    + " funcion.dt_nasc, funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, "
                    + " funcion.bairro, funcion.cidade , funcion.UF, funcion.CEP "
                    + " from funcion "
                    + " where codpessoaweb = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(pessoa.getCodigo());
            consult.select();
            Funcion f;
            while (consult.Proximo()) {
                f = new Funcion();
                f.setOrgEmis(consult.getString("orgemis"));
                f.setMatr(consult.getString("matr"));
                f.setCodFil(consult.getString("codfil"));
                f.setNome(consult.getString("nome"));
                f.setNome_Guer(consult.getString("nome_guer"));
                f.setCPF(consult.getString("cpf"));
                f.setRG(consult.getString("rg"));
                f.setDt_Nasc(consult.getString("dt_nasc"));
                f.setSituacao(consult.getString("situacao"));
                f.setDt_Situac(consult.getString("dt_situac"));
                f.setDt_Admis(consult.getString("dt_admis"));
                f.setSecao(consult.getString("secao"));
                f.setInterfExt(consult.getString("interfext"));
                f.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                f.setOperador(consult.getString("operador"));
                f.setDt_Alter(consult.getString("dt_alter"));
                f.setHr_Alter(consult.getString("hr_alter"));
                f.setBairro(consult.getString("bairro"));
                f.setCidade(consult.getString("cidade"));
                f.setUF(consult.getString("UF"));
                f.setCEP(consult.getString("CEP"));
                retorno.add(f);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados do funcionário - " + e.getMessage());
        }
    }

    /**
     * Busca a próxima maior matr de funcionário em um banco
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getMaxMatr(Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = BigDecimal.ZERO;
            String sql = " select convert(bigint,isnull(max(matr)+1,0)) matr from funcion ";
            //+ " where matr between 80000 and 90000 "; ----- OBS: Comentado porque o Marcos disse que a regra é Matr + 1, somente.
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("matr");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getMaxMatr - " + e.getMessage() + "\r\n"
                    + " select convert(bigint,isnull(max(matr)+1,0)) matr from funcion ");
        }
    }

    /**
     * Busca funcionário por cpf
     *
     * @param cpf
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Funcion> BuscaCPF(String cpf, Persistencia persistencia) throws Exception {
        List<Funcion> retorno = new ArrayList<>();
        try {
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.orgemis, "
                    + " funcion.dt_nasc, funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, "
                    + " funcion.bairro, funcion.cidade , funcion.UF, funcion.CEP "
                    + " from funcion "
                    + " where cpf = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cpf);
            consult.select();
            Funcion f;
            while (consult.Proximo()) {
                f = new Funcion();
                f.setOrgEmis(consult.getString("orgemis"));
                f.setMatr(consult.getString("matr"));
                f.setCodFil(consult.getString("codfil"));
                f.setNome(consult.getString("nome"));
                f.setNome_Guer(consult.getString("nome_guer"));
                f.setCPF(consult.getString("cpf"));
                f.setRG(consult.getString("rg"));
                f.setDt_Nasc(consult.getString("dt_nasc"));
                f.setSituacao(consult.getString("situacao"));
                f.setDt_Situac(consult.getString("dt_situac"));
                f.setDt_Admis(consult.getString("dt_admis"));
                f.setSecao(consult.getString("secao"));
                f.setInterfExt(consult.getString("interfext"));
                f.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                f.setOperador(consult.getString("operador"));
                f.setDt_Alter(consult.getString("dt_alter"));
                f.setHr_Alter(consult.getString("hr_alter"));
                f.setBairro(consult.getString("bairro"));
                f.setCidade(consult.getString("cidade"));
                f.setUF(consult.getString("UF"));
                f.setCEP(consult.getString("CEP"));
                retorno.add(f);
                retorno.add(f);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados do funcionário - " + e.getMessage());
        }
    }

    /**
     * Verifica a existência da matricula na base de dados
     *
     * @param matricula matricula do funcionário
     * @param persistencia
     * @return se já existe a matricula
     * @throws java.lang.Exception
     */
    public boolean matriculaExiste(String matricula, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT isnull(count(*),0) quantidade FROM funcion WHERE matr = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("quantidade");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir funcion - \r\n" + e.getMessage());
        }
        return existe;
    }

    /**
     * Inserção de funcionário simples
     *
     * @param funcion - objeto funcion, campos: codfil, matr, nome, secao, cpf,
     * dt_nasc, operador
     * @param persistencia
     * @throws Exception
     */
    public void InserirFuncionSimples(Funcion funcion, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into funcion "
                    + " (codfil, matr, nome, secao, cpf, dt_nasc, operador, dt_alter, hr_alter, situacao, dt_situac) "
                    + " values (?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setBigDecimal(funcion.getMatr());
            consulta.setString(funcion.getNome());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getOperador());
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString("A");
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir funcion - \r\n" + e.getMessage());
        }
    }

    public void inserirFuncionSimples(Funcion funcion, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into funcion "
                    + " (codfil, matr, nome, secao, cpf, dt_nasc, operador, dt_alter, hr_alter, situacao, dt_situac) "
                    + " values (?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setBigDecimal(funcion.getMatr());
            consulta.setString(funcion.getNome());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getOperador());
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString("A");
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir funcion - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de funcionários ativos com posto de serviço pela filial
     *
     * @param codfil - codigo da filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaFuncionFilial(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<FuncionPstServ> retorno = new ArrayList();
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, funcion.email, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, funcion.orgemis, "
                    + " funcion.bairro, funcion.cidade, funcion.UF, funcion.CEP "
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + "                 and pstserv.codfil = funcion.codfil"
                    + " where funcion.codfil = ? and funcion.situacao like 'A'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setOrgEmis(consult.getString("orgemis"));
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setEmail(consult.getString("email"));
                funcion.setBairro(consult.getString("bairro"));
                funcion.setCidade(consult.getString("cidade"));
                funcion.setUF(consult.getString("UF"));
                funcion.setCEP(consult.getString("CEP"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de funcionarios -\r\n" + e.getMessage());
        }
    }

    /**
     * Busca posto de serviço de um funcionário
     *
     * @param codfil - codigo da filial
     * @param dtCompet
     * @param matr - matricula
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public FuncionPstServ listaFuncionFilialMatr(String codfil, String dtCompet, String matr, Persistencia persistencia) throws Exception {
        try {
            String sql = " select clientes.email, clientes.contato, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, "
                    + " funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, pstserv.codcli, "
                    + " funcion.bairro, funcion.cidade, funcion.UF, funcion.CEP "
                    + " from funcion "
                    + " Left Join Rh_Horas on Rh_Horas.Matr = Funcion.Matr "
                    + "                    and Rh_Horas.data = ? "
                    + " Left Join PstServ on PstServ.Secao = Rh_Horas.Secao "
                    + "                   and PstServ.Codfil = Funcion.CodFil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "         and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where funcion.codfil = ? "
                    + " and funcion.matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(codfil);
            consulta.setString(matr);
            consulta.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp = new FuncionPstServ();
            while (consulta.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                funcion.setDt_Admis(consulta.getString("dt_admis"));
                funcion.setHr_Alter(consulta.getString("hr_alter"));
                funcion.setDt_Alter(consulta.getString("dt_alter"));
                funcion.setOperador(consulta.getString("operador"));
                funcion.setCodFil(consulta.getString("codfil"));
                funcion.setMatr(consulta.getString("matr"));
                funcion.setNome(consulta.getString("nome"));
                funcion.setNome_Guer(consulta.getString("nome_guer"));
                funcion.setCPF(consulta.getString("cpf"));
                funcion.setRG(consulta.getString("rg"));
                funcion.setDt_Nasc(consulta.getString("dt_nasc"));
                funcion.setSituacao(consulta.getString("situacao"));
                funcion.setDt_Situac(consulta.getString("dt_situac"));
                funcion.setSecao(consulta.getString("secao"));
                funcion.setInterfExt(consulta.getString("interfext"));
                funcion.setCodPessoaWeb(consulta.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consulta.getString("local"));
                pstserv.setCodCli(consulta.getString("codcli"));
                pstserv.setDescContrato(consulta.getString("descricao"));
                pstserv.setSecao(consulta.getString("secao"));
                pstserv.setCodFil(consulta.getString("codfil"));
                /**
                 * Cliente.Email
                 */
                funcion.setEmail(consulta.getString("email"));
                /**
                 * Cliente.Contato
                 */
                pstserv.setOperador(consulta.getString("contato"));
                funcion.setBairro(consulta.getString("bairro"));
                funcion.setCidade(consulta.getString("cidade"));
                funcion.setUF(consulta.getString("UF"));
                funcion.setCEP(consulta.getString("CEP"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
            }
            consulta.Close();
            return fp;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listaFuncionFilialMatr - " + e.getMessage() + "\r\n"
                    + " select clientes.email, clientes.contato, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, "
                    + " funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, pstserv.codcli "
                    + " from funcion "
                    + " Left Join Rh_Horas on Rh_Horas.Matr = Funcion.Matr "
                    + "                    and Rh_Horas.data = " + dtCompet
                    + " Left Join PstServ on PstServ.Secao = Rh_Horas.Secao "
                    + "                   and PstServ.Codfil = Funcion.CodFil "
                    + " left join pstserv on pstserv.secao = funcion.secao "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "         and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where funcion.codfil = " + codfil
                    + " and funcion.matr = " + matr);
        }
    }

    /**
     * Busca posto de serviço de um funcionário
     *
     * @param codfil - codigo da filial=
     * @param matr - matricula
     * @param secao
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public FuncionPstServ buscarFuncionSecao(String codfil, String matr, String secao, Persistencia persistencia) throws Exception {
        try {
            String sql = " select clientes.email, clientes.contato, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, \n"
                    + " funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, \n"
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, \n"
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, pstserv.codcli \n"
                    + " from funcion \n"
                    + " Left Join PstServ on PstServ.Secao = ? \n"
                    + "                   and PstServ.Codfil = Funcion.CodFil \n"
                    + " left join clientes on clientes.codigo = pstserv.codcli \n"
                    + "         and clientes.codfil = pstserv.codfil \n"
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil \n"
                    + "         and ctritens.contrato = pstserv.contrato \n"
                    + "         and ctritens.tipoposto = pstserv.tipoposto \n"
                    + " where funcion.codfil = ? \n"
                    + " and funcion.matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codfil);
            consulta.setString(matr);
            consulta.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp = new FuncionPstServ();
            while (consulta.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                funcion.setDt_Admis(consulta.getString("dt_admis"));
                funcion.setHr_Alter(consulta.getString("hr_alter"));
                funcion.setDt_Alter(consulta.getString("dt_alter"));
                funcion.setOperador(consulta.getString("operador"));
                funcion.setCodFil(consulta.getString("codfil"));
                funcion.setMatr(consulta.getString("matr"));
                funcion.setNome(consulta.getString("nome"));
                funcion.setNome_Guer(consulta.getString("nome_guer"));
                funcion.setCPF(consulta.getString("cpf"));
                funcion.setRG(consulta.getString("rg"));
                funcion.setDt_Nasc(consulta.getString("dt_nasc"));
                funcion.setSituacao(consulta.getString("situacao"));
                funcion.setDt_Situac(consulta.getString("dt_situac"));
                funcion.setSecao(consulta.getString("secao"));
                funcion.setInterfExt(consulta.getString("interfext"));
                funcion.setCodPessoaWeb(consulta.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consulta.getString("local"));
                pstserv.setCodCli(consulta.getString("codcli"));
                pstserv.setDescContrato(consulta.getString("descricao"));
                pstserv.setSecao(consulta.getString("secao"));
                pstserv.setCodFil(consulta.getString("codfil"));
                /**
                 * Cliente.Email
                 */
                funcion.setEmail(consulta.getString("email"));
                /**
                 * Cliente.Contato
                 */
                pstserv.setOperador(consulta.getString("contato"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
            }
            consulta.Close();
            return fp;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listaFuncionFilialMatr - " + e.getMessage() + "\r\n"
                    + " select clientes.email, clientes.contato, ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, "
                    + " funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, pstserv.codcli "
                    + " from funcion "
                    + " Left Join PstServ on PstServ.Secao = " + secao + " \n"
                    + "                   and PstServ.Codfil = Funcion.CodFil "
                    + " left join pstserv on pstserv.secao = funcion.secao "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "         and clientes.codfil = pstserv.codfil "
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "         and ctritens.contrato = pstserv.contrato "
                    + "         and ctritens.tipoposto = pstserv.tipoposto "
                    + " where funcion.codfil = " + codfil
                    + " and funcion.matr = " + matr);
        }
    }

    /**
     * Listagem de clientes pela filial e nome
     *
     * @param codfil - codigo da filial
     * @param nome - nome do funcinoario
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaFuncionFilialNome(BigDecimal codfil, String nome, Persistencia persistencia) throws Exception {
        try {
            List<FuncionPstServ> retorno = new ArrayList();
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local"
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + " where funcion.codfil = ?"
                    + " and nome like ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString('%' + nome + '%');
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de funcionarios -\r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de clientes pela filial e nred
     *
     * @param codfil - codigo da filial
     * @param nome_guer - nome de guerra do funcionário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaFuncionFilialNome_guer(BigDecimal codfil, String nome_guer, Persistencia persistencia) throws Exception {
        try {
            List<FuncionPstServ> retorno = new ArrayList();
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local"
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + " where funcion.codfil = ?"
                    + " and funcion.nome_guer like ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString("%" + nome_guer + "%");
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de funcionarios -\r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de clientes pela filial
     *
     * @param codfil - codigo da filial
     * @param posto - posto de trabalha
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaFuncionFilialPosto(BigDecimal codfil, String posto, Persistencia persistencia) throws Exception {
        try {
            List<FuncionPstServ> retorno = new ArrayList();
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local"
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + " where funcion.codfil = ?"
                    + " and pstserv.local = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(posto);
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de funcionarios -\r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de clientes pela filial
     *
     * @param codfil - codigo da filial
     * @param secao - código do posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaFuncionFilialSecao(BigDecimal codfil, String secao, Persistencia persistencia) throws Exception {
        try {
            List<FuncionPstServ> retorno = new ArrayList();
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local"
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + " where funcion.codfil = ?"
                    + " and funcion.secao = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(secao);
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar lista de funcionarios -\r\n" + e.getMessage());
        }
    }

    /**
     * Lista os funcionários do posto no dia
     *
     * @param codfil
     * @param secao
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Funcion> listaFuncionariosPosto(String codfil, String secao, String data, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = " select cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, funcion.secao, RHEscala.Descricao Escala "
                    + " from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " left join cargos on cargos.cargo = funcion.cargo "
                    + " left join rh_horas on rh_horas.matr = funcion.matr "
                    + "                 and rh_horas.codfil = funcion.codfil "
                    + " WHERE rh_horas.data = ? and rh_horas.codfil = ? and rh_horas.Secao = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data);
            consult.setString(codfil);
            consult.setString(secao);
            consult.select();
            Funcion funcion;
            while (consult.Proximo()) {
                funcion = new Funcion();
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setCargo(consult.getString("cargo"));
                funcion.setEscala(consult.getString("Escala"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listaFuncionSecaoData - " + e.getMessage() + "\r\n"
                    + " select count(RHPonto.batida) batidas, cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, funcion.secao "
                    + " left join rh_horas on rh_horas.matr = funcion.matr "
                    + "                 and rh_horas.codfil = funcion.codfil "
                    + " left join pstserv on pstserv.secao = rh_horas.secao "
                    + "                 and pstserv.codfil = rh_horas.codfil "
                    + " WHERE PstServ.situacao = 'A' and rh_horas.data = " + data
                    + "                 and pstserv.codfil = " + codfil + " and pstserv.Secao = " + secao
                    + " group by funcion.matr, funcion.codfil, funcion.nome, funcion.secao, cargos.descricao ");
        }
    }

    /**
     * Listagem de funcionarios com ponto para determinado posto em determinada
     * data
     *
     * @param codfil - codigo da filial
     * @param secao - código do posto
     * @param data
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Funcion> listaFuncionSecaoData(String codfil, String secao, String data, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = " select count(RHPonto.batida) batidas, cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, funcion.secao, RHEscala.Descricao Escala "
                    + " from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " left join cargos on cargos.cargo = funcion.cargo "
                    + " left join rh_horas on rh_horas.matr = funcion.matr "
                    + "                 and rh_horas.codfil = funcion.codfil "
                    + " left join pstserv on pstserv.secao = rh_horas.secao "
                    + "                 and pstserv.codfil = rh_horas.codfil "
                    + " left join RHPonto on RHPonto.matr = funcion.matr "
                    + "                    and RHPonto.DtCompet = rh_horas.data"
                    + " WHERE PstServ.situacao = 'A' and rh_horas.data = ? "
                    + "                 and pstserv.codfil = ? and pstserv.Secao = ? "
                    + " group by funcion.matr, funcion.codfil, funcion.nome, funcion.secao, cargos.descricao, RHEscala.Descricao";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data);
            consult.setString(codfil);
            consult.setString(secao);
            consult.select();
            Funcion funcion;
            while (consult.Proximo()) {
                funcion = new Funcion();
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setNumero(consult.getString("batidas"));
                funcion.setCargo(consult.getString("cargo"));
                funcion.setEscala(consult.getString("Escala"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listaFuncionSecaoData - " + e.getMessage() + "\r\n"
                    + " select count(RHPonto.batida) batidas, cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, funcion.secao "
                    + " left join rh_horas on rh_horas.matr = funcion.matr "
                    + "                 and rh_horas.codfil = funcion.codfil "
                    + " left join pstserv on pstserv.secao = rh_horas.secao "
                    + "                 and pstserv.codfil = rh_horas.codfil "
                    + " WHERE PstServ.situacao = 'A' and rh_horas.data = " + data
                    + "                 and pstserv.codfil = " + codfil + " and pstserv.Secao = " + secao
                    + " group by funcion.matr, funcion.codfil, funcion.nome, funcion.secao, cargos.descricao ");
        }
    }

    /**
     * Inserção de funcionário simples
     *
     * @param funcion - objeto funcion, campos: codfil, matr, nome, secao, cpf,
     * dt_nasc, operador, rg, dt_admis, interfext, codpessoaweb
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void InserirFuncionSatMobWeb(Funcion funcion, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "DECLARE @CodIbge AS BIGINT;\n"
                    + " SET @CodIbge = (SELECT TOP 1 CONVERT(BIGINT, CodIBGE) FROM Municipios where Nome = '" + funcion.getCidade() + "' AND UF = '" + funcion.getUF() + "');";

            sql += "insert into funcion "
                    + " (codfil, matr, CodCidade, nome, secao, cpf, dt_nasc, operador, dt_alter, hr_alter, situacao, "
                    + " dt_situac, rg, dt_admis, interfext, codpessoaweb, nome_guer, "
                    + " email, cep, endereco, bairro, cidade, uf, obs, altura, peso, sexo, fone1, fone2, orgemis, vinculo, cargo, codcargo, Funcao, horario, escala) "
                    + " values (?,?,@CodIbge,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'F',?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setBigDecimal(funcion.getMatr());
            consulta.setString(funcion.getNome().toUpperCase());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getOperador().toUpperCase());
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString("A");
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.setString(funcion.getRG());
            consulta.setString(funcion.getDt_Admis());
            consulta.setString(funcion.getInterfExt());
            consulta.setBigDecimal(funcion.getCodPessoaWeb());
            consulta.setString(funcion.getNome_Guer().toUpperCase());
            consulta.setString(funcion.getEmail());
            consulta.setString(funcion.getCEP().replace(".", "").replace("-", ""));
            consulta.setString(funcion.getEndereco());
            consulta.setString(funcion.getBairro());
            consulta.setString(funcion.getCidade());
            consulta.setString(funcion.getUF());
            consulta.setString(funcion.getObs());
            consulta.setBigDecimal(funcion.getAltura());
            consulta.setBigDecimal(funcion.getPeso());
            consulta.setString(funcion.getSexo());
            consulta.setString(funcion.getFone1());
            consulta.setString(funcion.getFone2());
            consulta.setString(funcion.getOrgEmis());
            consulta.setString(funcion.getCargo());
            consulta.setBigDecimal(funcion.getCodCargo());
            consulta.setString(funcion.getFuncao());
            consulta.setInt(funcion.getHorario());
            consulta.setString(funcion.getEscala());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir funcion - \r\n" + e.getMessage());
        }
    }

    /**
     * Grava alterações de funcionários
     *
     * @param funcion - objetos funcion Dados a serem gravados: matr, nome,
     * secao, cpf, dt_nasc, operador, dt_alter, hr_alter, situacao, dt_situac,
     * rg, dt_admis, interfext e nome_guer; e orgemis, email, fone1, fone2, cep,
     * endereco, bairro, cidade, uf, obs, funcao, altura, peso, sexo Parametros:
     * matr e codpessoaweb
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravaFuncionSatMobWeb(Funcion funcion, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DECLARE @CodIbge AS BIGINT;\n"
                    + " SET @CodIbge = (SELECT TOP 1 CONVERT(BIGINT, CodIBGE) FROM Municipios where Nome = '" + funcion.getCidade() + "' AND UF = '" + funcion.getUF() + "');";

            sql += "update funcion "
                    + "set "
                    + "codfil = ?, nome = ?, secao = ?, cpf = ?, "
                    + "dt_nasc = ?, operador = ?, dt_alter = ?, hr_alter = ?, situacao = ?, "
                    + "dt_situac = ?, rg = ?, dt_admis = ?, interfext = ?, nome_guer = ?, "
                    + " orgemis = ?, email = ?, fone1 = ?, fone2 = ?, cep = ?, endereco = ?, CodCidade = @CodIbge, "
                    + " bairro = ?, cidade = ?, uf = ?, obs = ?, altura = ?, peso = ?, sexo = ?, codcargo = ?, cargo = ?, "
                    + " horario = ?, mae = ?, CNH = ?, Dt_VenCNH = ?, PIS = ?, Funcao = ?, escala = ?, "
                    + " Nacionalid = ?, anoCheg = ?, Naturalid = ?, raca = ?, Instrucao = ?, estcivil = ?, Conjuge = ?, Pai = ?, CTPS_Nro = ?, CTPS_Serie = ?, CTPS_UF = ?, CTPS_Emis = ?, "
                    + " Categoria = ?, UF_CNH = ?, Reservista = ?, ReservCat = ?, TitEleit = ?, TitEZona = ?, TitSecao = ?, GrupoSang = ? "
                    + "where "
                    + "matr = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            funcion.setCEP(funcion.getCEP().replace("-", ""));

            consulta.setBigDecimal(funcion.getCodFil());
            consulta.setString(funcion.getNome());
            consulta.setString(funcion.getSecao());
            consulta.setString(funcion.getCPF());
            consulta.setString(funcion.getDt_Nasc());
            consulta.setString(funcion.getOperador());
            consulta.setDate(DataAtual.LC2Date(LocalDate.now()));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(funcion.getSituacao());
            consulta.setString(funcion.getDt_Situac());
            consulta.setString(funcion.getRG());
            consulta.setString(funcion.getDt_Admis());
            consulta.setString(funcion.getInterfExt());
            consulta.setString(funcion.getNome_Guer());
            consulta.setString(funcion.getOrgEmis());
            consulta.setString(funcion.getEmail());
            consulta.setString(funcion.getFone1());
            consulta.setString(funcion.getFone2());
            consulta.setString(funcion.getCEP());
            consulta.setString(funcion.getEndereco());
            consulta.setString(funcion.getBairro());
            consulta.setString(funcion.getCidade());
            consulta.setString(funcion.getUF());
            consulta.setString(funcion.getObs());
            consulta.setBigDecimal(funcion.getAltura());
            consulta.setBigDecimal(funcion.getPeso());
            consulta.setString(funcion.getSexo());
            consulta.setBigDecimal(funcion.getCodCargo());
            consulta.setString(funcion.getCargo());
            consulta.setInt(funcion.getHorario());
            consulta.setString(funcion.getMae());
            consulta.setString(funcion.getCNH());
            consulta.setString(funcion.getDt_VenCNH());
            consulta.setString(funcion.getPIS());
            consulta.setString(funcion.getFuncao());
            consulta.setString(funcion.getEscala());

            consulta.setString(funcion.getNacionalid());
            consulta.setString(funcion.getAnoCheg());
            consulta.setString(funcion.getNaturalid());
            consulta.setString(funcion.getRaca());
            consulta.setString(funcion.getInstrucao());
            consulta.setString(funcion.getEstCivil());
            consulta.setString(funcion.getConjuge());
            consulta.setString(funcion.getPai());
            consulta.setString(funcion.getCTPS_Nro());
            consulta.setString(funcion.getCTPS_Serie());
            consulta.setString(funcion.getCTPS_UF());
            consulta.setString(funcion.getCTPS_Emis());
            consulta.setString(funcion.getCategoria());
            consulta.setString(funcion.getUF_CNH());
            consulta.setString(funcion.getReservista());
            consulta.setString(funcion.getReservCat());
            consulta.setString(funcion.getTitEleit());
            consulta.setString(funcion.getTitEZona());
            consulta.setString(funcion.getTitSecao());
            consulta.setString(funcion.getGrupoSang());

            consulta.setBigDecimal(funcion.getMatr());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar dados do funcionário - \r\n" + e.getMessage());
        }
    }

    public List<FuncionPstServ> pesquisaFuncionSatMobWeb(FuncionPstServ funcionPstServ, String codfil,
            Persistencia persistencia) throws Exception {
        List<FuncionPstServ> retorno = new ArrayList();
        try {
            String sql = "select funcion.codfil, funcion.matr, funcion.nome, funcion.nome_guer, funcion.cpf, funcion.rg, funcion.dt_nasc, "
                    + " funcion.situacao, funcion.dt_situac, funcion.dt_admis, funcion.secao, funcion.interfext, funcion.email, "
                    + " funcion.codpessoaweb, funcion.operador, funcion.dt_alter, funcion.hr_alter, pstserv.local, funcion.orgemis"
                    + " from funcion"
                    + " left join pstserv on pstserv.secao = funcion.secao"
                    + "                 and pstserv.codfil = funcion.codfil"
                    + " where funcion.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)";
            if (!codfil.equals("")) {
                sql = sql + " and funcion.codfil = ?";
            }
            if (!funcionPstServ.getFuncion().getNome().equals("")) {
                sql = sql + " and funcion.nome like ?";
            }
            if (!funcionPstServ.getFuncion().getNome_Guer().equals("")) {
                sql = sql + " and funcio.nome_guer like ?";
            }
            if (null != funcionPstServ.getFuncion().getSituacao()) {
                sql = sql + " and funcion.situacao like ?";
            }
            if (null != funcionPstServ.getPstserv().getLocal()) {
                sql = sql + " and pstserv.local like ?";
            }
            sql = sql + " order by funcion.codfil";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(funcionPstServ.getFuncion().getCodPessoaWeb().toPlainString());
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            if (!codfil.equals("")) {
                consult.setString(codfil);
            }
            if (!funcionPstServ.getFuncion().getNome().equals("")) {
                consult.setString("%" + funcionPstServ.getFuncion().getNome().toUpperCase() + "%");
            }
            if (!funcionPstServ.getFuncion().getNome_Guer().equals("")) {
                consult.setString("%" + funcionPstServ.getFuncion().getNome_Guer().toUpperCase() + "%");
            }
            if (null != funcionPstServ.getFuncion().getSituacao()) {
                consult.setString("%" + funcionPstServ.getFuncion().getSituacao() + "%");
            }
            if (null != funcionPstServ.getPstserv().getLocal()) {
                consult.setString("%" + funcionPstServ.getPstserv().getLocal().toUpperCase() + "%");
            }
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setOrgEmis(consult.getString("orgemis"));
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setEmail(consult.getString("email"));
                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de funcionarios - \r\n" + e.getMessage());
        }
    }

    public List<Funcion> consultaSimples(String codfil, Persistencia persistencia) throws Exception {
        List<Funcion> retorno = new ArrayList();
        try {
            String sql = "select matr, nome from funcion where situacao = 'A' and codfil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.select();
            Funcion funcion;
            while (consult.Proximo()) {
                funcion = new Funcion();
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de funcionarios - \r\n" + e.getMessage());
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de funcionários cadastrados no banco
     *
     * @param filtros filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalFuncionMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from funcion "
                    + "left join pstserv on pstserv.secao = funcion.secao"
                    + "                 and pstserv.codfil = funcion.codfil"
                    + " WHERE funcion.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "matr IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar funcionários - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<FuncionPstServ> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<FuncionPstServ> retorno = new ArrayList();
        try {
            String sql = "SELECT  *\n"
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY nome ) AS RowNum, funcion.*, pstserv.local, CONCAT(RHPonto.DtCompet,';',convert(Bigint,RHPonto.Matr),';',(SELECT MAX(X.Batida) FROM RHPonto X  Where X.matr = RHPonto.matr AND X.dtCompet = RHPonto.dtCompet),';',Funcion.codfil,';') Chave\n"
                    + "          FROM      funcion\n"
                    + " left join pstserv on pstserv.secao = funcion.secao\n"
                    + "                 and pstserv.codfil = funcion.codfil\n"
                    + " left join(SELECT \n"
                    + "           MAX(DtCompet) DtCompet,\n"
                    + "           Matr\n"
                    + "           FROM RHPonto\n"
                    + "           WHERE CHARINDEX('MOB', Operador) > 0\n"
                    + "           GROUP BY Matr) RHPonto\n"
                    + "   on funcion.matr = RHPonto.matr\n"
                    + " WHERE funcion.codfil in (select filiais.codfil\n"
                    + "                          from saspw\n"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome\n"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac\n"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil\n"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND\n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND\n";
                }
            }
            sql = sql + "funcion.matr IS NOT null) AS RowConstrainedResult\n"
                    + "WHERE   RowNum >= ?\n"
                    + "    AND RowNum < ?\n"
                    + "ORDER BY RowNum\n";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setTIpo("1");
                funcion.setChave(consult.getString("Chave"));
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setEmail(consult.getString("email"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setCargo(consult.getString("cargo"));
                funcion.setHorario(consult.getInt("horario"));
                funcion.setOrgEmis(consult.getString("orgemis"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setEndereco(consult.getString("endereco"));
                funcion.setNumero(consult.getString("numero"));
                funcion.setBairro(consult.getString("bairro"));
                funcion.setCidade(consult.getString("cidade"));
                funcion.setUF(consult.getString("UF"));
                funcion.setCEP(consult.getString("CEP"));
                funcion.setFone1(consult.getString("Fone1"));
                funcion.setFone2(consult.getString("Fone2"));
                funcion.setSexo(consult.getString("Sexo"));
                funcion.setFuncao(consult.getString("Funcao"));
                funcion.setObs(consult.getString("obs"));
                funcion.setEscala(consult.getString("escala"));

                funcion.setAltura(consult.getString("Altura"));
                funcion.setPeso(consult.getString("Peso"));
                funcion.setNacionalid(consult.getString("Nacionalid"));
                funcion.setAnoCheg(consult.getString("anoCheg"));
                funcion.setInterfExt(consult.getString("InterfExt"));
                funcion.setNaturalid(consult.getString("Naturalid"));
                funcion.setRaca(consult.getString("raca"));
                funcion.setInstrucao(consult.getString("Instrucao"));
                funcion.setEstCivil(consult.getString("estcivil"));
                funcion.setConjuge(consult.getString("Conjuge"));
                funcion.setPai(consult.getString("Pai"));
                funcion.setMae(consult.getString("Mae"));
                funcion.setPIS(consult.getString("PIS"));
                funcion.setCTPS_Nro(consult.getString("CTPS_Nro"));
                funcion.setCTPS_Serie(consult.getString("CTPS_Serie"));
                funcion.setCTPS_UF(consult.getString("CTPS_UF"));
                funcion.setCTPS_Emis(consult.getString("CTPS_Emis"));
                funcion.setCNH(consult.getString("CNH"));
                funcion.setDt_VenCNH(consult.getString("Dt_VenCNH"));
                funcion.setCategoria(consult.getString("Categoria"));
                funcion.setUF_CNH(consult.getString("UF_CNH"));
                funcion.setReservista(consult.getString("Reservista"));
                funcion.setReservCat(consult.getString("ReservCat"));
                funcion.setTitEleit(consult.getString("TitEleit"));
                funcion.setTitEZona(consult.getString("TitEZona"));
                funcion.setTitSecao(consult.getString("TitSecao"));
                funcion.setGrupoSang(consult.getString("GrupoSang"));

                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de funcionarios - \r\n" + e.getMessage());
        }
    }

    public FuncionPstServ FuncionarioEdicao(String codFil, String matr, Persistencia persistencia) throws Exception {
        List<FuncionPstServ> retorno = new ArrayList();
        try {
            String sql = "  SELECT funcion.*, pstserv.local\n"
                    + "          FROM      funcion\n"
                    + " left join pstserv on pstserv.secao = funcion.secao\n"
                    + "                 and pstserv.codfil = funcion.codfil\n"
                    + " WHERE funcion.codfil = ?\n"
                    + " AND   funcion.matr   = ?";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.setString(matr);

            consult.select();
            Funcion funcion;
            PstServ pstserv;
            FuncionPstServ fp;
            while (consult.Proximo()) {
                funcion = new Funcion();
                pstserv = new PstServ();
                fp = new FuncionPstServ();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setEmail(consult.getString("email"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setCargo(consult.getString("cargo"));
                funcion.setHorario(consult.getInt("horario"));
                funcion.setOrgEmis(consult.getString("orgemis"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                pstserv.setLocal(consult.getString("local"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setEndereco(consult.getString("endereco"));
                funcion.setNumero(consult.getString("numero"));
                funcion.setBairro(consult.getString("bairro"));
                funcion.setCidade(consult.getString("cidade"));
                funcion.setUF(consult.getString("UF"));
                funcion.setCEP(consult.getString("CEP"));
                funcion.setFone1(consult.getString("Fone1"));
                funcion.setFone2(consult.getString("Fone2"));
                funcion.setSexo(consult.getString("Sexo"));
                funcion.setFuncao(consult.getString("Funcao"));
                funcion.setObs(consult.getString("obs"));
                funcion.setEscala(consult.getString("escala"));

                funcion.setAltura(consult.getString("Altura"));
                funcion.setPeso(consult.getString("Peso"));
                funcion.setNacionalid(consult.getString("Nacionalid"));
                funcion.setAnoCheg(consult.getString("anoCheg"));
                funcion.setInterfExt(consult.getString("InterfExt"));
                funcion.setNaturalid(consult.getString("Naturalid"));
                funcion.setRaca(consult.getString("raca"));
                funcion.setInstrucao(consult.getString("Instrucao"));
                funcion.setEstCivil(consult.getString("estcivil"));
                funcion.setConjuge(consult.getString("Conjuge"));
                funcion.setPai(consult.getString("Pai"));
                funcion.setMae(consult.getString("Mae"));
                funcion.setPIS(consult.getString("PIS"));
                funcion.setCTPS_Nro(consult.getString("CTPS_Nro"));
                funcion.setCTPS_Serie(consult.getString("CTPS_Serie"));
                funcion.setCTPS_UF(consult.getString("CTPS_UF"));
                funcion.setCTPS_Emis(consult.getString("CTPS_Emis"));
                funcion.setCNH(consult.getString("CNH"));
                funcion.setDt_VenCNH(consult.getString("Dt_VenCNH"));
                funcion.setCategoria(consult.getString("Categoria"));
                funcion.setUF_CNH(consult.getString("UF_CNH"));
                funcion.setReservista(consult.getString("Reservista"));
                funcion.setReservCat(consult.getString("ReservCat"));
                funcion.setTitEleit(consult.getString("TitEleit"));
                funcion.setTitEZona(consult.getString("TitEZona"));
                funcion.setTitSecao(consult.getString("TitSecao"));
                funcion.setGrupoSang(consult.getString("GrupoSang"));

                fp.setFuncion(funcion);
                fp.setPstserv(pstserv);
                retorno.add(fp);
            }
            consult.Close();

            return retorno.get(0);
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de funcionarios - \r\n" + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
    public Funcion BuscaFuncionCodPessoa(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        Funcion funcion = new Funcion();
        try {
            String sql = "SELECT * from funcion where codpessoaweb = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codpessoa);
            consult.select();
            while (consult.Proximo()) {
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setSexo(consult.getString("sexo"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.BuscaFuncionCodPessoa - " + e.getMessage() + "\r\n"
                    + "SELECT * from funcion where codpessoaweb = " + codpessoa);
        }
        return funcion;
    }

    public String gerarRG(Persistencia persistencia) throws Exception {
        String sql = "";
        String RG = "";

        try {
            sql = "SELECT \n"
                    + " ISNULL((SELECT \n"
                    + "         ISNULL(MAX(RG), 20010000) + 1\n"
                    + "         from Funcion\n"
                    + "         WHERE OrgEmis = 'MSL'\n"
                    + "         AND LEFT(RG,4) = '2001'), '20010001') RG";

            Consulta consult = new Consulta(sql, persistencia);
            consult.select();

            while (consult.Proximo()) {
                RG = consult.getString("RG");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.gerarRG - " + e.getMessage() + "\r\n" + sql);
        }
        return RG;
    }

    public List<Funcion> getFuncionNomeGuer(String nome_guer, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = "SELECT * from funcion where nome_guer = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(nome_guer);
            consult.select();
            Funcion funcion;
            while (consult.Proximo()) {
                funcion = new Funcion();
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setSexo(consult.getString("sexo"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getFuncionNomeGuer - " + e.getMessage() + "\r\n"
                    + " SELECT * from funcion where nome_guer = " + nome_guer);
        }
    }

    public List<Funcion> buscarFuncionNome(String nome, Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList();
            String sql = "select nome, nome_guer, codfil, fone1, fone2, matr from funcion \n"
                    + " where (nome like ? or nome_guer like ? )";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString('%' + nome.toUpperCase() + '%');
            consult.setString('%' + nome.toUpperCase() + '%');
            consult.select();
            Funcion funcion;

            while (consult.Proximo()) {
                funcion = new Funcion();

                funcion.setNome(consult.getString("nome"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setFone1(consult.getString("fone1"));
                funcion.setFone2(consult.getString("fone2"));
                funcion.setMatr(consult.getString("matr"));

                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.buscarFuncionNome - " + e.getMessage() + "\r\n"
                    + "select nome, nome_guer, codfil, fone1, fone2 from funcion \n"
                    + " where (nome like " + nome + " or nome_guer like " + nome + " )");
        }
    }

    /**
     * Busca DiuTurno da escala de um funcionário
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Excetpion
     */
    public String getDiuTurnoFuncion(String matricula, Persistencia persistencia) throws Exception {
        try {
            String retorno = "00000000";
            String sql = " select RHEscala.diuturno from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " where funcion.matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("DiuTurno");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getDiuTurnoFuncion - " + e.getMessage() + "\r\n"
                    + " select RHEscala.diuturno from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " where funcion.matr = = " + matricula);
        }
    }

    /**
     * Busca DiuTurno da escala de um funcionário
     *
     * @param matricula
     * @param data
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public Map<String, String> getDiuTurnoFuncion(String matricula, String data, Persistencia persistencia) throws Exception {
        try {
            Map retorno = new HashMap<String, String>();
            retorno.put("DiuTurno", "00000000");
            retorno.put("Batidas", "0");
            String sql = " select RHEscala.diuturno, (select count(*) from rhponto where dtcompet = ? and rhponto.matr = funcion.matr ) Batidas\n"
                    + " from funcion \n"
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil \n"
                    + " where funcion.matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(matricula);
            consulta.select();
            if (consulta.Proximo()) {
                retorno.replace("DiuTurno", consulta.getString("DiuTurno"));
                retorno.replace("Batidas", consulta.getString("Batidas"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getDiuTurnoFuncion - " + e.getMessage() + "\r\n"
                    + " select RHEscala.diuturno, (select count(*) from rhponto where dtcompet = " + data + " and rhponto.matr = funcion.matr ) Batidas\n"
                    + " from funcion \n"
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil \n"
                    + " where funcion.matr = " + matricula);
        }
    }

    /**
     * Busca DiuTurno da escala de uma pessoa
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public String getDiuTurnoPessoa(String codigo, Persistencia persistencia) throws Exception {
        try {
            String retorno = "00000000";
            String sql = " select RHEscala.diuturno from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " Left Join Pessoa on pessoa.matr = funcion.matr "
                    + " where pessoa.codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("DiuTurno");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getDiuTurnoPessoa - " + e.getMessage() + "\r\n"
                    + " select RHEscala.diuturno from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " Left Join Pessoa on pessoa.matr = funcion.matr "
                    + " where pessoa.codigo = " + codigo);
        }
    }

    /**
     * Busca DiuTurno da escala de uma pessoa
     *
     * @param matr
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public String getSecao(String matr, Persistencia persistencia) throws Exception {
        try {
            String retorno = null;
            String sql = " SELECT secao FROM funcion "
                    + " WHERE matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = consulta.getString("secao");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getSecao - " + e.getMessage() + "\r\n"
                    + " SELECT secao FROM funcion "
                    + " WHERE matr = " + matr);
        }
    }

    public Funcion getFuncion(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM funcion "
                    + " left join pessoa on pessoa.matr = funcion.matr "
                    + " where codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codPessoa);
            Funcion funcion = new Funcion();
            consult.select();
            while (consult.Proximo()) {
                funcion.setOrgEmis(consult.getString("orgemis"));
                funcion.setDt_Admis(consult.getString("dt_admis"));
                funcion.setHr_Alter(consult.getString("hr_alter"));
                funcion.setDt_Alter(consult.getString("dt_alter"));
                funcion.setOperador(consult.getString("operador"));
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setCPF(consult.getString("cpf"));
                funcion.setRG(consult.getString("rg"));
                funcion.setDt_Nasc(consult.getString("dt_nasc"));
                funcion.setSituacao(consult.getString("situacao"));
                funcion.setDt_Situac(consult.getString("dt_situac"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setInterfExt(consult.getString("interfext"));
                funcion.setCodPessoaWeb(consult.getBigDecimal("codpessoaweb"));
                funcion.setNome_Guer(consult.getString("nome_guer"));
                funcion.setEmail(consult.getString("email"));
            }
            consult.Close();
            return funcion;
        } catch (Exception e) {
            throw new Exception("FuncionDao.getFuncion - " + e.getMessage() + "\r\n"
                    + " Select * from funcion where codPessoa = " + codPessoa);
        }
    }

    public List<Funcion> listaFuncionariosRHPontoDet(String codfil, String secao, String dataInicio, String dataFim,
            Persistencia persistencia) throws Exception {
        try {
            List<Funcion> retorno = new ArrayList<>();
            String sql = " select cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, rhpontodet.secao, RHEscala.Descricao Escala "
                    + " from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " left join cargos on cargos.cargo = funcion.cargo "
                    + " left join rhpontodet on rhpontodet.matr = funcion.matr "
                    + " where funcion.codfil = ? and rhpontodet.secao = ? and rhpontodet.dtCompet between ? and ? "
                    + " group by funcion.matr, funcion.nome, cargos.descricao, funcion.codfil, rhpontodet.secao, RHEscala.Descricao; ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(secao);
            consult.setString(dataInicio);
            consult.setString(dataFim);
            consult.select();
            Funcion funcion;
            while (consult.Proximo()) {
                funcion = new Funcion();
                funcion.setCodFil(consult.getString("codfil"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                funcion.setSecao(consult.getString("secao"));
                funcion.setCargo(consult.getString("cargo"));
                funcion.setEscala(consult.getString("Escala"));
                retorno.add(funcion);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listaFuncionSecaoData - " + e.getMessage() + "\r\n"
                    + " select cargos.descricao cargo, "
                    + " funcion.matr, funcion.codfil, funcion.nome, rhpontodet.secao, RHEscala.Descricao Escala "
                    + " from funcion "
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala "
                    + "                    and RHEscala.CodFil = Funcion.CodFil "
                    + " left join cargos on cargos.cargo = funcion.cargo "
                    + " left join rhpontodet on rhpontodet.matr = funcion.matr "
                    + " where funcion.codfil = " + codfil + " and rhpontodet.secao = " + secao
                    + " and rhpontodet.dtCompet between " + dataInicio + " and " + dataFim
                    + " group by funcion.matr, funcion.nome, cargos.descricao, funcion.codfil, rhpontodet.secao, RHEscala.Descricao; ");
        }
    }

    public List<CarregarRelatorio> listaConsultas(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<CarregarRelatorio> retorno = new ArrayList<>();
            String sql = "Select Distinct Cargos.Descricao Cargo, Funcion.Salario, Funcion.Dt_Admis, Funcion.CodFil, Funcion.Jornada, \n"
                    + " Fornec.Endereco SindicatoEndereco, Fornec.Bairro SindicatoBairro,Fornec.CNPJ SindicatoCNPJ,Fornec.CEP SindicatoCep,Fornec.Cidade SindicatoCidade, \n"
                    + " Fornec.UF SindicatoUF, RHEscala.Descricao EscalaDescricao,Funcion.Hr_Alter,Funcion.CCusto, RHHorario.Descricao DescHorario, Funcion.Nome, Funcion.RG, Funcion.Numero, Funcion.CPF,  \n"
                    + " Funcion.OrgEmis, Funcion.Endereco, Funcion.Bairro, Funcion.Cidade, Funcion.UF, Funcion.CEP, Filiais.RazaoSocial, Funcion.Matr, \n"
                    + " Funcion.CTPS_Nro, Funcion.CTPS_Serie CTPSSerie, RHHorario.*, FPRescisoes.DtDemissao, Fornec.Empresa SindicatoNome, \n"
                    + " Filiais.Endereco EnderecoFilial, Filiais.Bairro BairroFilial, Filiais.Cidade CidadeFilial, Filiais.UF UFFilial, Filiais.CEP CEPFilial \n"
                    + " from Funcion \n"
                    + " left join Cargos      on  Cargos.Cargo      = Funcion.Cargo      \n"
                    + " left join RHHorario   on  RHHorario.Codigo  = Funcion.Horario    \n"
                    + "                       and RHHorario.CodFIl  = Funcion.CodFil     \n"
                    + " left join RHEscala    on  RHEscala.Codigo   = Funcion.Escala     \n"
                    + "                       and RHEscala.CodFil   = Funcion.CodFil     \n"
                    + " left join Sindicatos  on  Sindicatos.Codigo = Funcion.Sindicato  \n"
                    + " left join Fornec      on  Fornec.Codigo     = Sindicatos.CodForn \n"
                    + " left Join FPRescisoes on  FPRescisoes.Matr  = Funcion.Matr       \n"
                    + " left Join Filiais     on  Filiais.CodFil    = Funcion.CodFil     \n"
                    + " where Funcion.Matr =  ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();

            CarregarRelatorio cm;
            Funcion funcion;
            Cargos cargos;
            RHHorario rhHorario;
            RHEscala rhEscala;
            Sindicatos sindicatos;
            Fornec fornec;
            FPRescisoes fpRescisoes;
            Filiais filiais;

            while (consulta.Proximo()) {
                cm = new CarregarRelatorio();
                funcion = new Funcion();
                cargos = new Cargos();
                rhHorario = new RHHorario();
                rhEscala = new RHEscala();
                sindicatos = new Sindicatos();
                fornec = new Fornec();
                fpRescisoes = new FPRescisoes();
                filiais = new Filiais();

                cargos.setDescricao(consulta.getString("Cargo"));

                funcion.setSalario(consulta.getString("Salario"));
                funcion.setDt_Admis(consulta.getString("Dt_Admis"));
                funcion.setCodFil(consulta.getBigDecimal("CodFil"));
                funcion.setJornada(consulta.getString("Jornada"));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setRG(consulta.getString("RG"));
                funcion.setNumero(consulta.getString("Numero"));
                funcion.setCPF(consulta.getString("CPF"));
                funcion.setOrgEmis(consulta.getString("OrgEmis"));
                funcion.setEndereco(consulta.getString("Endereco"));
                funcion.setBairro(consulta.getString("Bairro"));
                funcion.setCidade(consulta.getString("Cidade"));
                funcion.setUF(consulta.getString("UF"));
                funcion.setCCusto(consulta.getString("CCusto"));
                funcion.setCEP(consulta.getString("CEP"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setCTPS_Nro(consulta.getString("CTPS_Nro"));
                funcion.setCTPS_Serie(consulta.getString("CTPSSerie"));
                funcion.setHr_Alter(consulta.getString("Hr_Alter"));

                fornec.setEndereco(consulta.getString("SindicatoEndereco"));
                fornec.setCNPJ(consulta.getString("SindicatoCNPJ"));
                fornec.setCEP(consulta.getString("SindicatoCEP"));
                fornec.setBairro(consulta.getString("SindicatoBairro"));
                fornec.setCidade(consulta.getString("SindicatoCidade"));
                fornec.setUF(consulta.getString("SindicatoUF"));
                fornec.setEmpresa(consulta.getString("SindicatoNome"));

                rhEscala.setDescricao(consulta.getString("EscalaDescricao"));

                rhHorario.setDescricao(consulta.getString("DescHorario"));

                filiais.setRazaoSocial(consulta.getString("RazaoSocial"));
                filiais.setEndereco(consulta.getString("EnderecoFilial"));
                filiais.setBairro(consulta.getString("BairroFilial"));
                filiais.setCidade(consulta.getString("CidadeFilial"));
                filiais.setUF(consulta.getString("UFFilial"));
                filiais.setCEP(consulta.getString("CEPFilial"));

                fpRescisoes.setDtDemissao(consulta.getLocalDate("DtDemissao"));

                cm.setFuncion(funcion);
                cm.setCargos(cargos);
                cm.setRhhorario(rhHorario);
                cm.setRhEscala(rhEscala);
                cm.setSindicatos(sindicatos);
                cm.setFornec(fornec);
                cm.setFpRescisoes(fpRescisoes);
                cm.setFiliais(filiais);

                retorno.add(cm);

            }

            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("FuncionDao.ListaConsultas - " + e.getMessage()
                    + "Select Distinct Cargos.Descricao Cargo, Funcion.Salario, Funcion.Dt_Admis, Funcion.CodFil, Funcion.Jornada, \n"
                    + " Fornec.Endereco SindicatoEndereco, Fornec.Bairro SindicatoBairro, Fornec.Cidade SindicatoCidade, \n"
                    + " Fornec.UF SindicatoUF, RHEscala.Descricao EscalaDescricao, RHHorario.Descricao DescHorario, Funcion.Nome, Funcion.RG, Funcion.Numero, Funcion.CPF,  \n"
                    + " Funcion.OrgEmis, Funcion.Endereco, Funcion.Bairro, Funcion.Cidade, Funcion.UF, Funcion.CEP, Filiais.RazaoSocial, Funcion.Matr, \n"
                    + " Funcion.CTPS_Nro, Funcion.CTPS_Serie CTPSSerie, RHHorario.*, FPRescisoes.DtDemissao, Fornec.Empresa SindicatoNome, \n"
                    + " Filiais.Endereco EnderecoFilial, Filiais.Bairro BairroFilial, Filiais.Cidade CidadeFilial, Filiais.UF UFFilial, Filiais.CEP CEPFilial \n"
                    + " from Funcion \n"
                    + " left join Cargos      on  Cargos.Cargo      = Funcion.Cargo      \n"
                    + " left join RHHorario   on  RHHorario.Codigo  = Funcion.Horario    \n"
                    + "                       and RHHorario.CodFIl  = Funcion.CodFil     \n"
                    + " left join RHEscala    on  RHEscala.Codigo   = Funcion.Escala     \n"
                    + "                       and RHEscala.CodFil   = Funcion.CodFil     \n"
                    + " left join Sindicatos  on  Sindicatos.Codigo = Funcion.Sindicato  \n"
                    + " left join Fornec      on  Fornec.Codigo     = Sindicatos.CodForn \n"
                    + " left Join FPRescisoes on  FPRescisoes.Matr  = Funcion.Matr       \n"
                    + " left Join Filiais     on  Filiais.CodFil    = Funcion.CodFil     \n"
                    + " where Funcion.Matr =  " + codigo + "");
        }
    }

    public String retornaUltimoCodigoPeDoctos(String codPessoa, Persistencia persistencia) throws Exception {
        String sql = "", retorno = "";
        try {
            sql = "SELECT\n"
                    + "(ISNULL(MAX(Ordem),0) + 1) Ordem\n"
                    + "FROM Pe_Doctos\n"
                    + "WHERE Codigo = " + codPessoa;

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getString("Ordem");
            }

            consulta.Close();

            return retorno;

        } catch (Exception e) {
            throw new Exception("FuncionDao.retornaUltimoCodigoPeDoctos - " + e.getMessage() + sql);
        }
    }

    public void inserePeDoctos(Pe_Doctos peDoctos, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "INSERT INTO Pe_Doctos (Codigo, Ordem, Descricao, Dt_Alter, Hr_Alter, Operador)\n"
                    + "VALUES(?,?,?,?,?,?);";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(peDoctos.getCodigo());
            consulta.setString(peDoctos.getOrdem());
            consulta.setString(peDoctos.getDescricao());
            consulta.setString(peDoctos.getDt_alter());
            consulta.setString(peDoctos.getHr_Alter());
            consulta.setString(peDoctos.getOperador());

            consulta.insert();

            sql = "UPDATE Pe_Doctos \n"
                    + "SET descricao = REPLACE(descricao,'-jpeg','-.jpeg')\n"
                    + "WHERE descricao <> REPLACE(descricao,'-jpeg','')";
            
            consulta = new Consulta(sql, persistencia);
            consulta.update();
            
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.inserePeDoctos - " + e.getMessage() + sql);
        }
    }

    public void excluiPeDoctos(String CodPessoa, String ordem, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "delete from Pe_Doctos\n"
                    + "where codigo = ?\n"
                    + "and   ordem  = ?;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(CodPessoa);
            consulta.setString(ordem);

            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("FuncionDao.excluiPeDoctos - " + e.getMessage() + sql);
        }
    }

    public List<Pe_Doctos> listarDocumentos(String codPessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Pe_Doctos> Retorno = new ArrayList<>();
            Pe_Doctos pe_Doctos;

            sql = "SELECT\n"
                    + " Pe_Doctos.Codigo, \n"
                    + " Pe_Doctos.Ordem,\n"
                    + " Pe_Doctos.Descricao,\n"
                    + " DATEDIFF(minute, Pe_Doctos.dt_alter + ' ' + Pe_Doctos.hr_alter, getdate()) Minutos\n"
                    + " FROM Pe_Doctos\n"                   
                    + " Left Join Pessoa on Pessoa.Codigo = Pe_Doctos.Codigo \n"
                    + " WHERE Pessoa.Matr = " + codPessoa //Estava Codigo
                    + " ORDER BY Pe_Doctos.Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                pe_Doctos = new Pe_Doctos();

                pe_Doctos.setCodigo(consulta.getString("Codigo"));
                pe_Doctos.setOrdem(consulta.getString("Ordem"));
                pe_Doctos.setDescricao(consulta.getString("Descricao"));
                pe_Doctos.setMinutos(consulta.getString("Minutos"));

                Retorno.add(pe_Doctos);
            }

            consulta.Close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("FuncionDao.listarDocumentos - " + e.getMessage() + sql);
        }
    }
}

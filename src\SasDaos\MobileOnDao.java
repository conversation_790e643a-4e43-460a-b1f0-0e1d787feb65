/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileContatos;
import SasBeans.MobileOn;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MobileOnDao {

    /**
     * Insere os valores do usuário que acabou de fazer login no mobile
     *
     * @param mobileOn Objeto a ser inserido no banco
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void InsereLogin(MobileOn mobileOn, Persistencia persistencia) throws Exception {
        String sql = "INSERT INTO mobileon "
                + "(CodFil, CodPessoa, IMEI, ApiKEY, Dt_Alter, Hr_Alter)"
                + " VALUES (?, ?, ?, ?, ?, ?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(mobileOn.getCodfil());
            consulta.setBigDecimal(mobileOn.getCodPessoa());
            consulta.setString(mobileOn.getIMEI());
            consulta.setString(mobileOn.getApiKEY());
            consulta.setString(mobileOn.getDt_Alter());
            consulta.setString(mobileOn.getHr_Alter());

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha MobileOn: " + e.getMessage());
        }
    }

    /**
     * Exclui o registro de Logon uma vez que o o usuário tenha feito Logout do
     * mobile
     *
     * @param CodPessoa Código da pessoa que fez o Logout
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void ExcluiLogin(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        String sql = "DELETE FROM mobileon "
                + " WHERE CodPessoa = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(CodPessoa);

            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha MobileOn: " + e.getMessage());
        }
    }

    /**
     * Lista todos os funcionários que fizeram Login no mobile
     *
     * @param codfil Número da filial
     * @param persistencia Conexão com o banco
     * @return
     * @throws Exception
     */
    public List<MobileOn> ListaLogados(BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<MobileOn> retorno = null;
        MobileOn mobileOn;

        String sql = "SELECT * FROM mobileon"
                + " WHERE codfil = ?";

        Consulta consulta = new Consulta(sql, persistencia);
        try {

            consulta.setBigDecimal(codfil);
            consulta.select();
            retorno = new ArrayList<>();

            while (consulta.Proximo()) {
                mobileOn = new MobileOn();

                mobileOn.setCodfil(consulta.getBigDecimal("CodFil"));
                mobileOn.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobileOn.setIMEI(consulta.getString("IMEI"));
                mobileOn.setApiKEY(consulta.getString("ApiKey"));
                mobileOn.setDt_Alter(consulta.getString("Dt_Alter"));
                mobileOn.setHr_Alter(consulta.getString("Hr_Alter"));

                retorno.add(mobileOn);

            }
        } catch (Exception e) {
            throw new Exception("Falha MobileOn: " + e.getMessage());
        }

        consulta.Close();
        return retorno;
    }

    /**
     * Verifica se o usuário ja está online
     *
     * @param codpessoa Código da pessoa
     * @param persistencia Conexão com o banco de dados
     * @return True se o usuário já está online false se está offline
     * @throws Exception
     */
    public boolean verificaLogado(String codpessoa, Persistencia persistencia) throws Exception {
        boolean retorno = false;
        List<MobileOn> list;
        MobileOn mobileOn;
        String sql = "SELECT * FROM mobileon WHERE codpessoa = ?";

        Consulta consulta = new Consulta(sql, persistencia);
        try {

            consulta.setString(codpessoa);
            consulta.select();

            list = new ArrayList<>();

            while (consulta.Proximo()) {
                mobileOn = new MobileOn();

                mobileOn.setCodfil(consulta.getBigDecimal("CodFil"));
                mobileOn.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobileOn.setIMEI(consulta.getString("IMEI"));
                mobileOn.setApiKEY(consulta.getString("ApiKey"));
                mobileOn.setDt_Alter(consulta.getString("Dt_Alter"));
                mobileOn.setHr_Alter(consulta.getString("Hr_Alter"));

                list.add(mobileOn);

            }

            if (!list.isEmpty()) {
                retorno = true;
            } else {
                retorno = false;
            }

        } catch (Exception e) {
            throw new Exception("Falha MobileOn: " + e.getMessage());
        }

        consulta.Close();
        return retorno;
    }

    public void atualizaLogon(String codpessoa, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {

        String sql = "UPDATE mobileon "
                + "SET Dt_Alter = ?, Hr_Alter = ? "
                + "WHERE codpessoa = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(codpessoa);
            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha MobileOn: " + e.getMessage());
        }
    }

    public MobileContatos vistoPorUltimo(BigDecimal codPessoa, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Pessoa.Nome, MobileOn.Dt_Alter Data, MobileOn.Hr_Alter Hora, Escala.Rota "
                    + " FROM MobileOn "
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = mobileon.CodPessoa "
                    + " LEFT JOIN Escala ON Escala.MatrChe = Pessoa.Matr "
                    + "                 AND Escala.CodFil  = MobileOn.CodFil "
                    + " WHERE Pessoa.CodPessoaWeb = ? and Escala.Data = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
            consulta.setString(data);
            consulta.select();
            MobileContatos retorno = new MobileContatos();
            while (consulta.Proximo()) {
                retorno.setNome(consulta.getString("nome"));
                retorno.setCodContato(codPessoa);
                retorno.setData(consulta.getString("data"));
                retorno.setHora(consulta.getString("hora"));
                retorno.setRota(consulta.getString("rota"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(" Falha ao buscar ultima vez online - " + e.getMessage());
        }
    }
}

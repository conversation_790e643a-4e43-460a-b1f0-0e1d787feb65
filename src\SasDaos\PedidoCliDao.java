/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PedidoCli;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PedidoCliDao {

    // create
    /**
     * gravaPedidoCli
     *
     * @param pedidocli
     * @param persistencia
     * @return
     */
    public boolean gravaPedidoCli(PedidoCli pedidocli, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into pedidocli(Numero,CodFil,Data,CodCli,Tipo,Arq,Origem,CNPJOri,"
                + "Transp<PERSON><PERSON>,CodExt,QtdeMalote,NumMalote,Banco,PedidoCli,PontoAtend) "
                + "Values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pedidocli.getNumero());
            consulta.setBigDecimal(pedidocli.getCodFil());
            consulta.setString(pedidocli.getData().toString());
            consulta.setString(pedidocli.getCodCli());
            consulta.setString(pedidocli.getTipo());
            consulta.setString(pedidocli.getArq());
            consulta.setString(pedidocli.getOrigem());
            consulta.setString(pedidocli.getCNPJOri());
            consulta.setString(pedidocli.getTranspOri());
            consulta.setString(pedidocli.getCodExt());
            consulta.setInt(pedidocli.getQtdeMalote());
            consulta.setInt(pedidocli.getNumMalote());
            consulta.setString(pedidocli.getBanco());
            consulta.setString(pedidocli.getPedidoCli());
            consulta.setString(pedidocli.getPontoAtend());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    /**
     * buscaPedidoCli
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<PedidoCli> buscaPedidoCli(Persistencia persistencia) throws Exception {
        List<PedidoCli> listPedidoCli;
        try {
            PedidoCli pedidocli;
            Consulta consult = new Consulta("select Numero,CodFil,Data,CodCli,Tipo,Arq,Origem,"
                    + "CNPJOri,TranspOri,CodExt,QtdeMalote,NumMalote,Banco,PedidoCli,PontoAtend "
                    + "from pedidocli", persistencia);
            consult.select();
            listPedidoCli = new ArrayList();
            while (consult.Proximo()) {
                pedidocli = new PedidoCli();
                pedidocli.setNumero(consult.getString("Numero"));
                pedidocli.setCodFil(consult.getString("CodFil"));
                pedidocli.setData(consult.getDate("Data").toLocalDate());
                pedidocli.setCodCli(consult.getString("CodCli"));
                pedidocli.setTipo(consult.getString("Tipo"));
                pedidocli.setArq(consult.getString("Arq"));
                pedidocli.setOrigem(consult.getString("Origem"));
                pedidocli.setCNPJOri(consult.getString("CNPJOri"));
                pedidocli.setTranspOri(consult.getString("TranspOri"));
                pedidocli.setCodExt(consult.getString("CodExt"));
                pedidocli.setQtdeMalote(consult.getInt("QtdeMalote"));
                pedidocli.setNumMalote(consult.getInt("NumMalote"));
                pedidocli.setBanco(consult.getString("Banco"));
                pedidocli.setPedidoCli(consult.getString("PedidoCli"));
                pedidocli.setPontoAtend(consult.getString("PontoAtend"));
                listPedidoCli.add(pedidocli);
            }
            consult.Close();
        } catch (Exception e) {
            listPedidoCli = null;
            throw new Exception("Falha ao executar buscaPedidoCli " + e.getMessage());
        }
        return listPedidoCli;
    }

    // update
    /**
     * atualizarPedidoCli
     *
     * @param pedidocli
     * @param persistencia
     * @throws Exception
     */
    public void atualizarPedidoCli(PedidoCli pedidocli, Persistencia persistencia) throws Exception {
        String sql = "update pedidocli set Numero=?,CodFil=?,Data=?,CodCli=?,Tipo=?,Arq=?,"
                + "Origem=?,CNPJOri=?,TranspOri=?,CodExt=?,QtdeMalote=?,NumMalote=?,Banco=?,"
                + "PedidoCli=?,PontoAtend=? "
                + "where Numero=? and CodFil=? and Data=? and CodCli=? and Tipo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pedidocli.getNumero());
            consulta.setBigDecimal(pedidocli.getCodFil());
            consulta.setString(pedidocli.getData().toString());
            consulta.setString(pedidocli.getCodCli());
            consulta.setString(pedidocli.getTipo());
            consulta.setString(pedidocli.getArq());
            consulta.setString(pedidocli.getOrigem());
            consulta.setString(pedidocli.getCNPJOri());
            consulta.setString(pedidocli.getTranspOri());
            consulta.setString(pedidocli.getCodExt());
            consulta.setInt(pedidocli.getQtdeMalote());
            consulta.setInt(pedidocli.getNumMalote());
            consulta.setString(pedidocli.getBanco());
            consulta.setString(pedidocli.getPedidoCli());
            consulta.setString(pedidocli.getPontoAtend());
            consulta.setBigDecimal(pedidocli.getNumero());
            consulta.setBigDecimal(pedidocli.getCodFil());
            consulta.setString(pedidocli.getData().toString());
            consulta.setString(pedidocli.getCodCli());
            consulta.setString(pedidocli.getTipo());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao executar atualizarPedidoCli " + e.getMessage());
        }
    }

    // delete
    /**
     * excluirPedidoCli
     *
     * @param pedidocli
     * @param persistencia
     * @throws Exception
     */
    public void excluirPedidoCli(PedidoCli pedidocli, Persistencia persistencia) throws Exception {
        String sql = "delete from pedidocli "
                + "where Numero=? and CodFil=? and Data=? and CodCli=? and Tipo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pedidocli.getNumero());
            consulta.setBigDecimal(pedidocli.getCodFil());
            consulta.setString(pedidocli.getData().toString());
            consulta.setString(pedidocli.getCodCli());
            consulta.setString(pedidocli.getTipo());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao executar excluirPedidoCli " + e.getMessage());
        }
    }

}

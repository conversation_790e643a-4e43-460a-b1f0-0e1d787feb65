package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ContasFin;
import SasBeans.NFiscal;
import SasBeansCompostas.NFiscalContasFin;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class NFiscalContasFinDao {

    /**
     * Apresenta receita vinda da nfiscal por data, agrupado por receita
     *
     * @param inicio - inicio do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<NFiscalContasFin> ReceitaPeriodo(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<NFiscalContasFin> retorno = new ArrayList();
        try {
            String sql = "Select Year(NFiscal.Data) Ano,  Month(NFiscal.Data) Me<PERSON>, Sum(Valor) <PERSON>or, Sum(ValorLiq) ValorLiq "
                    + " from NFiscal"
                    + " left join ContasFin on ContasFin.Codigo = Nfiscal.ContaFin"
                    + " where Data >= ?" //'20160101'" 
                    + " and Data <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " and NFIscal.Situacao = 'A'"
                    + " group by Year(NFiscal.Data) ,  Month(NFiscal.Data)"
                    + " order by Ano, Mes";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            NFiscalContasFin nfisc;
            NFiscal nfiscal;
            while (consult.Proximo()) {
                nfisc = new NFiscalContasFin();
                nfiscal = new NFiscal();
                nfiscal.setData(LocalDate.of(consult.getInt("Ano"), consult.getInt("Mes"), 01));
                nfiscal.setValor(consult.getString("Valor"));
                nfiscal.setValorLiq(consult.getString("ValorLiq"));
                nfisc.setNfiscal(nfiscal);
                retorno.add(nfisc);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar informacoes de contas a receber\r\n" + e.getMessage());
        }
    }

    /**
     * Apresenta receita vinda da nfiscal por data, agrupado por receita
     * mostrando a conta financeira
     *
     * @param inicio - inicio do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<NFiscalContasFin> ReceitaPeriodoContaFin(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<NFiscalContasFin> retorno = new ArrayList();
        try {
            String sql = "Select Year(NFiscal.Data) Ano,  Month(NFiscal.Data) Mes, ContasFin.Descricao, Sum(Valor) Valor, Sum(ValorLiq) ValorLiq from NFiscal"
                    + " left join ContasFin on ContasFin.Codigo = Nfiscal.ContaFin"
                    + " where Data >= ?" //'20160101'" 
                    + " and Data <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " and NFIscal.Situacao = 'A'"
                    + " group by Year(NFiscal.Data) ,  Month(NFiscal.Data), ContasFin.Descricao "
                    + " order by Ano, Mes";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            NFiscalContasFin nfisc;
            NFiscal nfiscal;
            ContasFin contasfin;
            while (consult.Proximo()) {
                nfisc = new NFiscalContasFin();
                nfiscal = new NFiscal();
                contasfin = new ContasFin();
                nfiscal.setData(LocalDate.of(consult.getInt("Ano"), consult.getInt("Mes"), 1));
                nfiscal.setValor(consult.getString("Valor"));
                nfiscal.setValorLiq(consult.getString("ValorLiq"));
                contasfin.setDescricao(consult.getString("Descricao"));
                nfisc.setNfiscal(nfiscal);
                nfisc.setContasfin(contasfin);
                nfisc.setDescricao(consult.getString("Descricao"));
                nfisc.setReceitaLiquida(FuncoesString.formatarStringMoeda(consult.getString("ValorLiq"), false));
                retorno.add(nfisc);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar informacoes de contas a receber\r\n" + e.getMessage());
        }
    }

    /**
     * Apresenta receita vinda da nfiscal liquido por , agrupado por receita
     * mostrando a conta financeira
     *
     * @param inicio - inicio do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<NFiscalContasFin> ReceitaContaFinLiquida(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<NFiscalContasFin> retorno = new ArrayList();
        try {
            String sql = "Select ContasFin.Descricao, Sum(Valor) Valor, Sum(ValorLiq) ValorLiq from NFiscal"
                    + "left join ContasFin on ContasFin.Codigo = Nfiscal.ContaFin"
                    + " where Data >= ?" //'20160101'"
                    + " and Data <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " and NFIscal.Situacao = 'A'"
                    + " group by ContasFin.Descricao";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            NFiscalContasFin nfisc;
            NFiscal nfiscal;
            ContasFin contasfin;
            while (consult.Proximo()) {
                nfisc = new NFiscalContasFin();
                nfiscal = new NFiscal();
                contasfin = new ContasFin();
                nfiscal.setValor(consult.getString("Valor"));
                nfiscal.setValorLiq(consult.getString("ValorLiq"));
                contasfin.setDescricao(consult.getString("Descricao"));
                nfisc.setNfiscal(nfiscal);
                nfisc.setContasfin(contasfin);
                retorno.add(nfisc);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar informacoes de contas a receber\r\n" + e.getMessage());
        }
    }

    /**
     * Apresenta receita vinda da nfiscal recebido por , agrupado por receita
     * mostrando a conta financeira
     *
     * @param inicio - inicio do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<NFiscalContasFin> ReceitaContaFinRec(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<NFiscalContasFin> retorno = new ArrayList();
        try {
            String sql = "Select ContasFin.Descricao, Sum(Valor) Valor, Sum(ValorPago) ValorPago from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= ?" //'20160101'"
                    + " and DtPagto <= ?" //'20160630'"
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by ContasFin.Descricao"
                    + " order by ContasFin.Descricao";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            NFiscalContasFin nfisc;
            NFiscal nfiscal;
            ContasFin contasfin;
            while (consult.Proximo()) {
                nfisc = new NFiscalContasFin();
                nfiscal = new NFiscal();
                contasfin = new ContasFin();
                nfiscal.setValor(consult.getString("Valor"));
                nfiscal.setValorPago(consult.getString("ValorPago"));
                contasfin.setDescricao(consult.getString("Descricao"));
                nfisc.setNfiscal(nfiscal);
                nfisc.setContasfin(contasfin);
                retorno.add(nfisc);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar informacoes de contas a receber\r\n" + e.getMessage());
        }
    }

    /**
     * Receita Liquida total do período
     *
     * @param inicio
     * @param fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public NFiscalContasFin ReceitaLiqPeriodo(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        NFiscalContasFin retorno = new NFiscalContasFin();
        try {
            String sql = "Select  Sum(ValorLiq) ValorLiq "
                    + " from NFiscal"
                    + " left join ContasFin on ContasFin.Codigo = Nfiscal.ContaFin"
                    + " where Data >= ?" //'20160101'" 
                    + " and Data <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " and NFIscal.Situacao = 'A'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            while (consult.Proximo()) {
                retorno.setReceitaLiquida(consult.getString("ValorLiq"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar informacoes de contas a receber\r\n" + e.getMessage());
        }
    }
}

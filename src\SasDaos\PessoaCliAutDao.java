/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaCliAut;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaCliAutDao {

    public void inserirPessoaCliAut(PessoaCliAut pessoaCliAut, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE PessoaCliAut\n"
                    + "SET Operador = ?,\n"
                    + "    Dt_alter = ?,\n"
                    + "    Hr_Alter = ?,\n"
                    + "    Flag_excl = ?\n"
                    + "WHERE Codigo = ? AND CodFil = ? AND CodCli = ?\n"
                    + "\n"
                    + "INSERT INTO PessoaCliAut (Operador, Dt_alter, Hr_Alter, Flag_excl, Codigo, CodFil, CodCli)\n"
                    + "SELECT TOP 1 ?, ?, ?, ?, ?, ?, ?\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado\n"
                    + "    FROM PessoaCliAut\n"
                    + "    WHERE Codigo = ? AND CodFil = ? AND CodCli = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaCliAut.getOperador());
            consulta.setString(pessoaCliAut.getDt_Alter());
            consulta.setString(pessoaCliAut.getHr_Alter());
            consulta.setString(pessoaCliAut.getFlag_Excl());
            consulta.setBigDecimal(pessoaCliAut.getCodigo());
            consulta.setString(pessoaCliAut.getCodFil());
            consulta.setString(pessoaCliAut.getCodCli());

            consulta.setString(pessoaCliAut.getOperador());
            consulta.setString(pessoaCliAut.getDt_Alter());
            consulta.setString(pessoaCliAut.getHr_Alter());
            consulta.setString(pessoaCliAut.getFlag_Excl());
            consulta.setBigDecimal(pessoaCliAut.getCodigo());
            consulta.setString(pessoaCliAut.getCodFil());
            consulta.setString(pessoaCliAut.getCodCli());

            consulta.setBigDecimal(pessoaCliAut.getCodigo());
            consulta.setString(pessoaCliAut.getCodFil());
            consulta.setString(pessoaCliAut.getCodCli());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.inserirPessoaCliAut - " + e.getMessage() + "\r\n"
                    + "UPDATE PessoaCliAut\n"
                    + "SET Operador = " + pessoaCliAut.getOperador() + ",\n"
                    + "    Dt_alter = " + pessoaCliAut.getDt_Alter() + ",\n"
                    + "    Hr_Alter = " + pessoaCliAut.getHr_Alter() + ",\n"
                    + "    Flag_excl = " + pessoaCliAut.getFlag_Excl() + "\n"
                    + "WHERE Codigo = " + pessoaCliAut.getCodigo() + " AND CodFil = " + pessoaCliAut.getCodFil() + " AND CodCli = "
                    + pessoaCliAut.getCodCli() + "\n"
                    + "\n"
                    + "INSERT INTO PessoaCliAut (Operador, Dt_alter, Hr_Alter, Flag_excl, Codigo, CodFil, CodCli)\n"
                    + "SELECT TOP 1 " + pessoaCliAut.getOperador() + ", " + pessoaCliAut.getDt_Alter() + ", " + pessoaCliAut.getHr_Alter() + ", "
                    + pessoaCliAut.getFlag_Excl() + ", " + pessoaCliAut.getCodigo() + ", " + pessoaCliAut.getCodFil() + ", " + pessoaCliAut.getCodCli() + "\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado\n"
                    + "    FROM PessoaCliAut\n"
                    + "    WHERE Codigo = " + pessoaCliAut.getCodigo() + " AND CodFil = " + pessoaCliAut.getCodFil() + " AND CodCli = "
                    + pessoaCliAut.getCodCli() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0");
        }
    }

    public List<PessoaCliAut> buscarUsuarios(String query, String CodCli, String CodFil, Persistencia persistencia) throws Exception {
        try {
            List<PessoaCliAut> retorno = new ArrayList<>();
            String sql = "select pessoa.nome, pessoa.email, pessoa.codigo, pessoa.pwweb \n"
                    + "from pessoa\n"
                    + "left join saspw on saspw.codpessoa = pessoa.codigo\n"
                    + "where saspw.codpessoaweb is not null and (pessoa.nome like ? OR pessoa.email like ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setNome(consulta.getString("nome"));
                c.setEmail(consulta.getString("email"));
                c.setCodigo(consulta.getString("codigo"));
                c.setPwweb(consulta.getString("pwweb"));
                c.setCodFil(CodFil);
                c.setCodCli(CodCli);
                retorno.add(c);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarUsuarios - " + e.getMessage() + "\r\n"
                    + " SELECT Pessoa.nome, Pessoa.email, PessoaCliAut.* \n"
                    + " FROM PessoaCliAut \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaCliAut.Codigo \n"
                    + " WHERE PessoaCliAut.CodCli = " + CodCli + " AND CodFil = " + CodFil);
        }
    }

    public List<PessoaCliAut> buscarUsuariosPessoa(String query, String CodCli, String CodFil, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<PessoaCliAut> retorno = new ArrayList<>();
            sql = "select pessoa.nome, pessoa.email, pessoa.codigo, pessoa.pwweb \n"
                    + "from pessoa\n"
                    + "where (pessoa.nome like ? OR pessoa.email like ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setNome(consulta.getString("nome"));
                c.setEmail(consulta.getString("email"));
                c.setCodigo(consulta.getString("codigo"));
                c.setPwweb(consulta.getString("pwweb"));
                c.setCodFil(CodFil);
                c.setCodCli(CodCli);
                retorno.add(c);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.buscarUsuariosPessoa - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<PessoaCliAut> listarUsuarios(String CodCli, String CodFil, Persistencia persistencia) throws Exception {
        try {
            List<PessoaCliAut> retorno = new ArrayList<>();
            String sql = " SELECT Pessoa.nome, Pessoa.email, PessoaCliAut.* \n"
                    + " FROM PessoaCliAut \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaCliAut.Codigo \n"
                    + " WHERE PessoaCliAut.CodCli = ? AND CodFil = ? AND Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodCli);
            consulta.setString(CodFil);
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setNome(consulta.getString("nome"));
                c.setEmail(consulta.getString("email"));
                c.setCodigo(consulta.getString("codigo"));
                c.setCodFil(consulta.getString("CodFil"));
                c.setCodCli(consulta.getString("codcli"));
                c.setFlag_Excl(consulta.getString("Flag_Excl"));
                c.setOperador(consulta.getString("operador"));
                c.setDt_Alter(consulta.getString("Dt_Alter"));
                c.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(c);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarUsuarios - " + e.getMessage() + "\r\n"
                    + " SELECT Pessoa.nome, Pessoa.email, PessoaCliAut.* \n"
                    + " FROM PessoaCliAut \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaCliAut.Codigo \n"
                    + " WHERE PessoaCliAut.CodCli = " + CodCli + " AND CodFil = " + CodFil);
        }
    }

    public List<PessoaCliAut> listarUsuarios(String Codigo, Persistencia persistencia) throws Exception {
        try {
            List<PessoaCliAut> retorno = new ArrayList<>();
            String sql = " SELECT Pessoa.nome, Pessoa.email, PessoaCliAut.* \n"
                    + " FROM PessoaCliAut \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaCliAut.Codigo \n"
                    + " WHERE PessoaCliAut.Codigo = ? AND Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Codigo);
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setNome(consulta.getString("nome"));
                c.setEmail(consulta.getString("email"));
                c.setCodigo(consulta.getString("codigo"));
                c.setCodFil(consulta.getString("CodFil"));
                c.setCodCli(consulta.getString("codcli"));
                c.setFlag_Excl(consulta.getString("Flag_Excl"));
                c.setOperador(consulta.getString("operador"));
                c.setDt_Alter(consulta.getString("Dt_Alter"));
                c.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(c);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarUsuarios - " + e.getMessage() + "\r\n"
                    + " SELECT Pessoa.nome, Pessoa.email, PessoaCliAut.* \n"
                    + " FROM PessoaCliAut \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = PessoaCliAut.Codigo \n"
                    + " WHERE PessoaCliAut.Codigo = " + Codigo + " AND Flag_Excl <> '*'");
        }
    }

    /**
     * Lista registros de clientes autorizados. SALVA O NOME DA EMPRESA NO CAMPO
     * OPERADOR.
     *
     * @param cliente - campos utilizazdos: codigo e codfil
     * @param excluido
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<PessoaCliAut> listarClientes(PessoaCliAut cliente, Boolean excluido, Persistencia persistencia) throws Exception {
        List<PessoaCliAut> clientes = new ArrayList<>();
        try {
            String sql = "Select distinct Filiais.Descricao Empresa, Clientes.Nred Cliente, Clientes.CodFil, \n"
                    + " Clientes.CodCofre, \n"
                    + " Clientes.Agencia, Clientes.SubAgencia, "
                    + " PessoaCliAut.CodCli, SasPW.CodGrupo, OS_Vig.CliDst "
                    + " from PessoaCliAut "
                    + " Left join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli "
                    + "                    and Clientes.CodFil = PessoaCliAut.CodFil "
                    + " Left join Filiais   on Filiais.CodFil  = PessoaCliAut.CodFil "
                    + " Left join SasPW on SasPW.CodPessoa = PessoaCliAut.Codigo "
                    + " Left join OS_Vig on OS_Vig.Cliente = Clientes.Codigo "
                    + "                 and OS_Vig.CodFil  = Clientes.CodFil "
                    + "                 and Substring(OS_Vig.CliDst,1,4)  = '9997' "
                    + "                 and OS_Vig.Situacao  = 'A' "
                    + " where PessoaCliAut.Codigo = ? ";
            if (!excluido) {
                sql = sql + " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*') ";
            }
            sql = sql + " ORDER BY Clientes.SubAgencia asc, Clientes.Nred asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setCodCli(consulta.getString("codcli"));
                if (c.getCodCli().contains("6074001")) {
                    c.setNomeCli("TESTE PRESERVE");
                } else {
                    c.setNomeCli(consulta.getString("cliente"));
                }
//                c.setNomeCli(consulta.getString("cliente"));
                c.setOperador(consulta.getString("empresa"));
                c.setCodFil(consulta.getString("codfil"));
                c.setCodGrupo(consulta.getString("CodGrupo"));
                c.setAgencia(consulta.getString("Agencia"));
                c.setSubAgencia(consulta.getString("SubAgencia"));
                c.setCodCofre(consulta.getString("CodCofre"));
                c.setCliDst(consulta.getString("CliDst"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarClientes - " + e.getMessage() + "\r\n"
                    + "Select Filiais.Descricao Empresa, Clientes.Nred Cliente, Clientes.CodFil, Clientes.Agencia, Clientes.SubAgencia, "
                    + " PessoaCliAut.CodCli from PessoaCliAut "
                    + " Left join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli "
                    + "                   and Clientes.CodFil = PessoaCliAut.CodFil "
                    + " Left join Filiais   on Filiais.CodFil  = PessoaCliAut.CodFil "
                    + " where PessoaCliAut.Codigo = " + cliente.getCodigo()
                    + (!excluido ? " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*')" : ""));
        }
        return clientes;
    }

    public List<PessoaCliAut> listarClientesGTVe(String codCliente, Boolean excluido, Persistencia persistencia) throws Exception {
        List<PessoaCliAut> clientes = new ArrayList<>();
        String sql = "";
        try {
            sql = "Select top 1 Filiais.Descricao Empresa, Clientes.Nred Cliente, Clientes.CodFil, \n"
                    + " Clientes.CodCofre, \n"
                    + " Clientes.Agencia, Clientes.SubAgencia, "
                    + " PessoaCliAut.CodCli, SasPW.CodGrupo "
                    + " from PessoaCliAut "
                    + " Left join Clientes  on Clientes.Codigo = PessoaCliAut.CodCli "
                    + "                    and Clientes.CodFil = PessoaCliAut.CodFil "
                    + " Left join Filiais   on Filiais.CodFil  = PessoaCliAut.CodFil "
                    + " Left join SasPW on SasPW.CodPessoa = PessoaCliAut.Codigo "
                    + " where Clientes.Codigo = ? ";
            if (!excluido) {
                sql = sql + " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*') ";
            }
            sql = sql + " ORDER BY Clientes.SubAgencia asc, Clientes.Nred asc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codCliente);
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setCodCli(consulta.getString("codcli"));
                if (c.getCodCli().contains("6074001")) {
                    c.setNomeCli("TESTE PRESERVE");
                } else {
                    c.setNomeCli(consulta.getString("cliente"));
                }
//                c.setNomeCli(consulta.getString("cliente"));
                c.setOperador(consulta.getString("empresa"));
                c.setCodFil(consulta.getString("codfil"));
                c.setCodGrupo(consulta.getString("CodGrupo"));
                c.setAgencia(consulta.getString("Agencia"));
                c.setSubAgencia(consulta.getString("SubAgencia"));
                c.setCodCofre(consulta.getString("CodCofre"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarClientesGTVe - " + e.getMessage() + "\r\n" + sql);
        }
        return clientes;
    }
    
    public List<PessoaCliAut> listarClientesGTVeChave(String codCliente, String codFil, Boolean excluido, Persistencia persistencia) throws Exception {
        List<PessoaCliAut> clientes = new ArrayList<>();
        String sql = "";
        try {
            sql = "Select top 1 Filiais.Descricao Empresa, Clientes.Nred Cliente, Clientes.CodFil, \n"
                    + " Clientes.CodCofre, \n"
                    + " Clientes.Agencia, Clientes.SubAgencia, "
                    + " Clientes.Codigo codcli, 9 CodGrupo "
                    + " from Clientes "
                    + " Left join Filiais   on Filiais.CodFil  = Clientes.CodFil "
                    + " where Clientes.Codigo = ?\n"
                    + " and   Clientes.codfil = ? ";
            
            sql = sql + " ORDER BY Clientes.SubAgencia asc, Clientes.Nred desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codCliente);
            consulta.setBigDecimal(codFil);
            consulta.select();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setCodCli(consulta.getString("codcli"));
                if (c.getCodCli().contains("6074001")) {
                    c.setNomeCli("TESTE PRESERVE");
                } else {
                    c.setNomeCli(consulta.getString("cliente"));
                }
//                c.setNomeCli(consulta.getString("cliente"));
                c.setOperador(consulta.getString("empresa"));
                c.setCodFil(consulta.getString("codfil"));
                c.setCodGrupo(consulta.getString("CodGrupo"));
                c.setAgencia(consulta.getString("Agencia"));
                c.setSubAgencia(consulta.getString("SubAgencia"));
                c.setCodCofre(consulta.getString("CodCofre"));
                clientes.add(c);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaCliAutDao.listarClientesGTVeChave - " + e.getMessage() + "\r\n" + sql);
        }
        return clientes;
    }
}

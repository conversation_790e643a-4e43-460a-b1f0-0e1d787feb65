package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.GTVeAcesso;
import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaLoginDao {

    public void inserirPessoaLogin(PessoaLogin pessoaLogin, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE PessoaLogin\n"
                    + "SET Nivel = ?,\n"
                    + "    CodPessoaBD = ?,\n"
                    + "    Operador = ?,\n"
                    + "    Dt_alter = ?,\n"
                    + "    Hr_Alter = ?\n"
                    + "WHERE Codigo = ? AND BancoDados = ?\n"
                    + "\n"
                    + "INSERT INTO PessoaLogin (Nivel, CodPessoaBD, Operador, Dt_alter, Hr_Alter, Codigo, BancoDados)\n"
                    + "SELECT TOP 1 ?, ?, ?, ?, ?, ?, ?\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado\n"
                    + "    FROM PessoaLogin\n"
                    + "    WHERE Codigo = ? AND BancoDados = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoaLogin.getNivel());
            consulta.setBigDecimal(pessoaLogin.getCodPessoaBD());
            consulta.setString(pessoaLogin.getOperador());
            consulta.setString(pessoaLogin.getDt_alter());
            consulta.setString(pessoaLogin.getHr_Alter());
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());

            consulta.setString(pessoaLogin.getNivel());
            consulta.setBigDecimal(pessoaLogin.getCodPessoaBD());
            consulta.setString(pessoaLogin.getOperador());
            consulta.setString(pessoaLogin.getDt_alter());
            consulta.setString(pessoaLogin.getHr_Alter());
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());

            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.inserirPessoaLogin - " + e.getMessage() + "\r\n"
                    + "UPDATE PessoaLogin\n"
                    + "SET Nivel = " + pessoaLogin.getNivel() + ",\n"
                    + "    CodPessoaBD = " + pessoaLogin.getCodPessoaBD() + ",\n"
                    + "    Operador = " + pessoaLogin.getOperador() + ",\n"
                    + "    Dt_alter = " + pessoaLogin.getDt_alter() + ",\n"
                    + "    Hr_Alter = " + pessoaLogin.getHr_Alter() + "\n"
                    + "WHERE Codigo = " + pessoaLogin.getCodigo() + " AND BancoDados = " + pessoaLogin.getCodigo() + "\n"
                    + "\n"
                    + "INSERT INTO PessoaLogin (Nivel, CodPessoaBD, Operador, Dt_alter, Hr_Alter, Codigo, BancoDados)\n"
                    + "SELECT TOP 1 " + pessoaLogin.getNivel() + ", " + pessoaLogin.getCodPessoaBD() + ", " + pessoaLogin.getOperador() + ", "
                    + pessoaLogin.getDt_alter() + ", " + pessoaLogin.getHr_Alter() + ", " + pessoaLogin.getCodigo() + ", "
                    + pessoaLogin.getBancoDados() + "\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado\n"
                    + "    FROM PessoaLogin\n"
                    + "    WHERE Codigo = " + pessoaLogin.getCodigo() + " AND BancoDados = " + pessoaLogin.getBancoDados() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0");
        }
    }

    public Boolean deletaPessoaLogin(PessoaLogin pessoaLogin, Persistencia persistencia) throws Exception {
        Boolean resultado = true;
        String sql = "delete from pessoalogin where codigo = ? and bancodados = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());
            resultado = consulta.delete() > 0;
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.deletaPessoaLogin - " + e.getMessage() + "\r\n"
                    + "delete from pessoalogin where codigo =" + pessoaLogin.getCodigo() + " and bancodados = " + pessoaLogin.getBancoDados());
        }
        return resultado;
    }

    public void gravaPessoaLogin(PessoaLogin pessoaLogin, Persistencia persistencia) throws Exception {

        String sql = "insert into pessoalogin(codigo, bancodados, nivel, codpessoabd, operador, dt_alter, hr_alter) values(?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());
            consulta.setString(String.valueOf(pessoaLogin.getNivel()));
            consulta.setBigDecimal(pessoaLogin.getCodPessoaBD());
            consulta.setString(pessoaLogin.getOperador());
            consulta.setString(pessoaLogin.getDt_alter());
            consulta.setString(pessoaLogin.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.gravaPessoaLogin - " + e.getMessage() + "\r\n"
                    + "insert into pessoalogin(codigo, bancodados, nivel, codpessoabd, operador, dt_alter, hr_alter) \n"
                    + " values(" + pessoaLogin.getCodigo() + "," + pessoaLogin.getBancoDados() + "," + String.valueOf(pessoaLogin.getNivel()) + ","
                    + pessoaLogin.getCodPessoaBD() + "," + pessoaLogin.getOperador() + "," + pessoaLogin.getDt_alter() + "," + pessoaLogin.getHr_Alter() + ")");
        }
    }

    public void gravaPessoaLoginNaoExistente(PessoaLogin pessoaLogin, Persistencia persistencia) throws Exception {

        String sql = "INSERT INTO PessoaLogin SELECT TOP 1\n"
                + " ?,\n"
                + " ?,\n"
                + " ?,\n"
                + " ?,\n"
                + " ?,\n"
                + " ?,\n"
                + " ?\n"
                + " FROM PessoaLogin      A\n"
                + " LEFT JOIN PessoaLogin B\n"
                + "   ON A.codigo = B.codigo\n"
                + "  AND B.BancoDados = ?\n"
                + " WHERE A.Codigo    = ?\n"
                + " AND   B.Codigo IS NULL\n";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());
            consulta.setString(String.valueOf(pessoaLogin.getNivel()));
            consulta.setBigDecimal(pessoaLogin.getCodPessoaBD());
            consulta.setString(pessoaLogin.getOperador());
            consulta.setString(pessoaLogin.getDt_alter());
            consulta.setString(pessoaLogin.getHr_Alter());
            consulta.setString(pessoaLogin.getBancoDados());
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.gravaPessoaLogin - " + e.getMessage() + "\r\n"
                    + "insert into pessoalogin(codigo, bancodados, nivel, codpessoabd, operador, dt_alter, hr_alter) \n"
                    + " values(" + pessoaLogin.getCodigo() + "," + pessoaLogin.getBancoDados() + "," + String.valueOf(pessoaLogin.getNivel()) + ","
                    + pessoaLogin.getCodPessoaBD() + "," + pessoaLogin.getOperador() + "," + pessoaLogin.getDt_alter() + "," + pessoaLogin.getHr_Alter() + ")");
        }
    }

    public void atualizaPessoaLogin(PessoaLogin pessoaLogin, Persistencia persistencia) throws Exception {
        String sql = "update pessoalogin set codpessoabd = ?, nivel = ?, operador = ?, dt_alter = ?, hr_alter = ? \n"
                + " where codigo = ? and bancodados like ? ";

        gravaPessoaLoginNaoExistente(pessoaLogin, persistencia);
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaLogin.getCodPessoaBD());
            consulta.setString(String.valueOf(pessoaLogin.getNivel()));
            consulta.setString(pessoaLogin.getOperador());
            consulta.setString(pessoaLogin.getDt_alter());
            consulta.setString(pessoaLogin.getHr_Alter());
            consulta.setBigDecimal(pessoaLogin.getCodigo());
            consulta.setString(pessoaLogin.getBancoDados());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.atualizaPessoaLogin - " + e.getMessage() + "\r\n"
                    + " update pessoalogin set codpessoabd = " + pessoaLogin.getCodPessoaBD() + ", nivel = " + String.valueOf(pessoaLogin.getNivel()) + ","
                    + " operador = " + pessoaLogin.getOperador() + ", dt_alter = " + pessoaLogin.getDt_alter() + ", hr_alter = " + pessoaLogin.getHr_Alter()
                    + " where codigo = " + pessoaLogin.getCodigo() + " and bancodados like " + pessoaLogin.getBancoDados());
        }
    }

    public List<PessoaLogin> getCodigoPessoaLogin(BigDecimal CodPessoa, String sBancoDados, Persistencia persistencia) throws Exception {
        List<PessoaLogin> listp = new ArrayList<>();
        String sql = "select BancoDados from pessoalogin where codigo = ? and bancodados = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.setString(sBancoDados);
            consult.select();
            while (consult.Proximo()) {
                PessoaLogin pl = new PessoaLogin();
                pl.setBancoDados(consult.getString("BancoDados"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.getCodigoPessoaLogin - " + e.getMessage() + "\r\n"
                    + "select BancoDados from pessoalogin where codigo = " + CodPessoa + " and bancodados = " + sBancoDados);
        }
    }

    public List<Pessoa> getAllPessoaLoginD(Persistencia persistencia) throws Exception {
        List<Pessoa> listp = new ArrayList();
        String sql = "select codigo, nome, cpf from pessoa";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                Pessoa pl = new Pessoa();
                pl.setCodigo(consult.getString("codigo").replace(".0", ""));
                pl.setNome(consult.getString("nome"));
                pl.setCPF(consult.getString("cpf"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.getAllPessoaLoginD - " + e.getMessage() + "\r\n");
        }
    }

    public static List<PessoaLogin> getPessoaLogin(String CodPessoa, Persistencia persistencia) throws Exception {
        List<PessoaLogin> listp = new ArrayList();
        String sql = "select * from pessoalogin where codigo = ? --CASE WHEN BancoDados <> 'SATELLITE' THEN REPLACE(BancoDados,'SAT','') ELSE BancoDados END";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                PessoaLogin pl = new PessoaLogin();
                pl.setCodigo(new BigDecimal(CodPessoa));
                pl.setBancoDados(consult.getString("BancoDados").toUpperCase());
                pl.setNivel(consult.getString("Nivel").toUpperCase());
                
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.getPessoaLogin - " + e.getMessage() + "\r\n"
                    + "select BancoDados from pessoalogin where codigo = " + CodPessoa);
        }
    }

    /**
     * Busca os acessos de uma pessoa
     *
     * @param CodPessoa - Código da Pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PessoaLogin> getPessoaLogin(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<PessoaLogin> listp = new ArrayList();
        String sql = "select * from pessoalogin where codigo = ? order by CASE WHEN BancoDados <> 'SATELLITE' THEN REPLACE(BancoDados,'SAT','') ELSE BancoDados END";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                PessoaLogin pl = new PessoaLogin();
                pl.setCodigo(consult.getBigDecimal("Codigo"));
                pl.setBancoDados(consult.getString("BancoDados").toUpperCase());
                pl.setNivel(consult.getString("Nivel"));
                pl.setCodPessoaBD(consult.getBigDecimal("CodPessoaBD"));
                pl.setOperador(consult.getString("Operador"));
                pl.setDt_alter(consult.getString("Dt_alter"));
                pl.setHr_Alter(consult.getString("Hr_Alter"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.getPessoaLogin - " + e.getMessage() + "\r\n"
                    + "select * from pessoalogin where codigo = " + CodPessoa);
        }
    }

    public GTVeAcesso getPessoaGTVe(String Chave, Persistencia persistencia) throws Exception {
        GTVeAcesso retorno = new GTVeAcesso();
        String sql = "";
        
        try {

            sql = "SELECT \n"
                    + "TOP 1 *, CASE WHEN Dt_Valid IS NULL OR Dt_Valid = '' OR convert(varchar, getdate(), 23) > convert(varchar, Dt_Valid,23) THEN 'S' ELSE 'N' END Expirado \n"
                    + "FROM GTVeAcesso\n"
                    + "WHERE Chave = ?";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(Chave);
            consult.select();
            
            while (consult.Proximo()) {
                retorno.setChave(consult.getString("Chave"));
                retorno.setCodCli(consult.getString("CodCli"));
                retorno.setCodFil(consult.getString("CodFil"));
                retorno.setCodPessoa(consult.getString("CodPessoa"));
                retorno.setDt_Alter(consult.getString("Dt_Alter"));
                retorno.setDt_Inc(consult.getString("Dt_Inc"));
                retorno.setHr_Alter(consult.getString("Hr_Alter"));
                retorno.setHr_Inc(consult.getString("Hr_Inc"));
                retorno.setDt_Valid(consult.getString("Dt_Valid"));
                retorno.setFlag_excl(consult.getString("Flag_excl"));
                retorno.setOper_Excl(consult.getString("Oper_Excl"));
                retorno.setDt_Excl(consult.getString("Dt_Excl"));
                retorno.setHr_Excl(consult.getString("Hr_Excl"));                
                retorno.setOper_Inc(consult.getString("Oper_Inc"));
                retorno.setOperador(consult.getString("Operador"));
                retorno.setParametro(consult.getString("Parametro"));
                retorno.setExpirado(consult.getString("Expirado"));
            }
            
            consult.Close();
            
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.getPessoaGTVe - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public Boolean existePessoaLogin(PessoaLogin pessoa, Persistencia persistencia) throws Exception {
        String sql = "select count(*) quantidade from pessoalogin where codigo = ? and bancodados = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(pessoa.getCodigo());
            consult.setString(pessoa.getBancoDados());
            consult.select();

            int quantidade = 0;
            while (consult.Proximo()) {
                quantidade = consult.getInt("quantidade");
            }

            if (quantidade > 0) {
                return true;
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaLoginDao.existePessoaLogin - " + e.getMessage() + "\r\n"
                    + "select count(*) quantidade from pessoalogin where codigo = " + pessoa.getCodigo() + " and bancodados = " + pessoa.getBancoDados());
        }
        return false;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaCandidato;

/**
 *
 * <AUTHOR>
 */
public class PessoaCandidatoDao {

    public void gravaPessoa(String sCod, PessoaCandidato oPessoaCndt, Persistencia persistencia) throws Exception {
        boolean retorno;
        String sql = "insert into PessoaCandidato ("
                + "CodPessoa"
                + ",SindicatoProp"
                + ", Vicios"
                + ", PassaTempos"
                + ", AcaoCriminal "
                + ", MovAcaoCrim"
                + ", MovAcaoTrab"
                + ", SPC"
                + ", CheqDevolvido"
                + ", VlrDivida"
                + ", ArmaFogo"
                + ", RegistroArma "
                + ", AceitaTrocaHr"
                + ", AceitaTrocaLocal"
                + ", OutrosCursos"
                + ", Recrutamento"
                + ", AmigosEmpresa "
                + ", FamiliaresEmpresa"
                + ", QtdeFilhos"
                + ", IdadeFilhos"
                + ", BensProprios"
                + ", Data"
                + ", Hora) "
                + "Values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sCod);
            consulta.setString(oPessoaCndt.getSindicatoProp());
            consulta.setString(oPessoaCndt.getVicios());
            consulta.setString(oPessoaCndt.getPassaTempos());
            consulta.setString(oPessoaCndt.getAcaoCriminal());
            consulta.setString(oPessoaCndt.getMovAcaoCrim());
            consulta.setString(oPessoaCndt.getMovAcaoTrab());
            consulta.setString(oPessoaCndt.getSPC());
            consulta.setString(oPessoaCndt.getCheqDevolvido());
            consulta.setString(oPessoaCndt.getVlrDivida());
            consulta.setString(oPessoaCndt.getArmaFogo());
            consulta.setString(oPessoaCndt.getRegistroArma());
            consulta.setString(oPessoaCndt.getAceitaTrocaHr());
            consulta.setString(oPessoaCndt.getAceitaTrocaLocal());
            consulta.setString(oPessoaCndt.getOutrosCursos());
            consulta.setString(oPessoaCndt.getRecrutamento());
            consulta.setString(oPessoaCndt.getAmigosEmpresa());
            consulta.setString(oPessoaCndt.getFamiliaresEmpresa());
            consulta.setString(oPessoaCndt.getQtdeFilhos());
            consulta.setInt(Integer.parseInt(oPessoaCndt.getIdadeFilhos()));
            consulta.setString(oPessoaCndt.getBensProprios());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("Falha ao inserir dados do candidato - " + e.getMessage());
        }
    }
}

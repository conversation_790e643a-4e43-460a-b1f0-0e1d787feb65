/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.IntegraEBS;

/**
 *
 * <AUTHOR>
 */
public class IntegraEBSDao {
    
    public IntegraEBS ultimaMovimentacao(Persistencia persistencia) throws Exception {
        try {
            String sql = "Select isnull(Convert(Date,Max(occurrenceDate)),'2022-04-25') Date, isnull(convert(Varchar,Max(occurrenceDate),108),'00:00:00') Hour \n"
                    + "from IntegraEBS (nolock)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            IntegraEBS integraEBS = new IntegraEBS();
            while (consulta.Proximo()) {
                integraEBS.setDtUltOcor(consulta.getString("Date"));
                integraEBS.setHrUltOcor(consulta.getString("Hour"));
            }
            consulta.Close();
            return integraEBS;
        } catch (Exception e) {
            throw new Exception("IntegraEBSDao.ultimaMovimentacao - " + e.getMessage());
        }
    }    

    public void inserirMovimentacao(IntegraEBS movimentacao, Persistencia persistencia) throws Exception {
        try {
            String sql = "Declare @existe int;\n"
                    + "Declare @id Varchar(100);\n"
                    + "set @id = ?;\n"
                    + "\n"
                    + "Select @existe = isnull(Count(*),0) from IntegraEBS (nolock) where id = @id;\n"
                    + "\n"
                    + "if (@existe = 0) begin\n"
                    + "	Insert into IntegraEBS (Sequencia, id, alarmGroupId, occurrenceDate, receiveDate, detectDate, alarmGroupDate,\n"
                    + "		 eventType, deviceId, deviceName, deviceSerialNo, type, displayedType, rawOsmEventType, eventData, locationOnly,\n"
                    + "		 codfil, operador, dt_Alter, hr_Alter)\n"
                    
                    + "	Select Isnull(Max(Sequencia),0)+1, "
                    + " @id,"
                    + " ?,"
//                    + " convert(datetime,(Convert(BigInt,?)/1000/60/60/24 + 25567)),"
//                    + " convert(datetime,(Convert(BigInt,?)/1000/60/60/24 + 25567)),"
//                    + " convert(datetime,(Convert(BigInt,?)/1000/60/60/24 + 25567)),"
//                    + " convert(datetime,(Convert(BigInt,?)/1000/60/60/24 + 25567)),"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?,"
                    + " ?\n"
                    + "	From IntegraEBS;\n"
                    + "end";
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setString(movimentacao.getId());
            consulta.setString(movimentacao.getAlarmGroupId());
            consulta.setBigDecimal(movimentacao.getOccurrenceDate());
            consulta.setBigDecimal(movimentacao.getReceiveDate());
            consulta.setBigDecimal(movimentacao.getDetectDate());
            consulta.setBigDecimal(movimentacao.getAlarmGroupDate());
            consulta.setString(movimentacao.getEventType());
            consulta.setString(movimentacao.getDeviceId());
            consulta.setString(movimentacao.getDeviceName());
            consulta.setString(movimentacao.getDeviceSerialNo());
            consulta.setString(movimentacao.getType());
            consulta.setString(movimentacao.getDisplayedType());
            consulta.setString(movimentacao.getRawOsmEventType());
            consulta.setString(movimentacao.getEventData());
            consulta.setString(movimentacao.getLocationOnly());

            consulta.setString(movimentacao.getCodfil());
            consulta.setString(movimentacao.getOperador());
            consulta.setString(movimentacao.getDtAlter());
            consulta.setString(movimentacao.getHrAlter());
            
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("IntegraEBSDao.inserirMovimentacao - " + e.getMessage());
        }
    }

}

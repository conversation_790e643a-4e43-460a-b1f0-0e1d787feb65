/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Ocorlog;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OcorlogDao {
    public List<Ocorlog> recuperarInformacoesImprodutivos(Persistencia persistencia) throws Exception {
        List<Ocorlog> listaResultado = new ArrayList<>();
        try {
            String sql = "SELECT Ocorlog.Tipo, Ocorlog.CodOcorr, Ocorlog.Descricao, " 
                    + "Ocorlog.Faturar " 
                    + "FROM Ocorlog " 
                    + "WHERE Ocorlog.Tipo = '60' AND Ocorlog.Faturar = 'NAO' ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            Ocorlog ocorrenciaNova;
            while (consulta.Proximo()) {
                ocorrenciaNova = new Ocorlog();
                ocorrenciaNova.setCodOcorr(consulta.getString("CodOcorr"));
                ocorrenciaNova.setDescricao(consulta.getString("Descricao"));
                ocorrenciaNova.setFaturar(consulta.getString("Faturar"));
                ocorrenciaNova.setTipo(consulta.getString("Tipo"));
                listaResultado.add(ocorrenciaNova);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("OcorlogDao.recuperarInformacoes - " + e.getMessage() + "\r\n"
                    + "SELECT Ocorlog.Tipo, Ocorlog.CodOcorr, Ocorlog.Descricao\n" 
                    + "FROM Ocorlog\n" 
                    + "WHERE Ocorlog.Tipo = '60' AND Ocorlog.Faturar = 'NAO' ");
        }
        return listaResultado;
    }
}

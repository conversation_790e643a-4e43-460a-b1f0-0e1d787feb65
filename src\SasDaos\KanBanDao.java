/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.KanBan;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class KanBanDao {

    public List<KanBan> listarTickets(String codPessoa, Persistencia persistencia) throws Exception {
        List<KanBan> tickets = new ArrayList<>();
        try {
            String sql = "SELECT    KanBan.*, pessoa.Nome nomePessoa "
                    + "          FROM      KanBan "
                    + " left join pessoa on pessoa.codigo = kanban.codpessoa "
                    + ((null == codPessoa) ? "" : " WHERE kanban.codpessoa = " + codPessoa);
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            KanBan ticket;
            while (consult.Proximo()) {
                ticket = new KanBan();
                ticket.setSequencia(consult.getBigDecimal("sequencia"));
                ticket.setDescricao(consult.getString("descricao"));
                ticket.setHistorico(consult.getString("historico"));
                ticket.setCodPessoa(consult.getBigDecimal("codpessoa").toBigInteger().toString());
                ticket.setFase(consult.getString("fase"));
                ticket.setNomePessoa(consult.getString("nomePessoa"));
                tickets.add(ticket);
            }
            consult.Close();
            return tickets;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de tickets eletrônicos - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de tickets
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<KanBan> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<KanBan> tickets = new ArrayList<>();
        try {
            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY sequencia desc ) AS RowNum, "
                    + "             KanBan.*, pessoa.Nome nomePessoa "
                    + "          FROM      KanBan "
                    + " left join pessoa on pessoa.codigo = kanban.codpessoa "
                    + " WHERE ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "sequencia IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            KanBan ticket;
            while (consult.Proximo()) {
                ticket = new KanBan();
                ticket.setSequencia(consult.getBigDecimal("sequencia"));
                ticket.setDescricao(consult.getString("descricao"));
                ticket.setHistorico(consult.getString("historico"));
                ticket.setCodPessoa(consult.getBigDecimal("codpessoa").toBigInteger().toString());
                ticket.setFase(consult.getString("fase"));
                ticket.setNomePessoa(consult.getString("nomePessoa"));
                tickets.add(ticket);
            }
            consult.Close();
            return tickets;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de tickets eletrônicos - \r\n" + e.getMessage());
        }
    }

    /**
     * Conta o número de tickets
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalTicketsMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from kanban"
                    + " WHERE ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "sequencia IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar tickets - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca o próximo número de sequência
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getMaxSequencia(Persistencia persistencia) throws Exception {
        try {
            String retorno = new String(), sql = "Select isnull(MAX(sequencia),0)+1 sequencia "
                    + " FROM kanban ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("sequencia");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar próxima sequência de tickets - \r\n" + e.getMessage());
        }
    }

    /**
     * Insere um novo ticket
     *
     * @param kanban
     * @param persistencia
     * @throws Exception
     */
    public void inserirTicket(KanBan kanban, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO kanban (sequencia, descricao, historico, codpessoa, fase) "
                    + "VALUES(?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(kanban.getSequencia());
            consulta.setString(kanban.getDescricao());
            consulta.setString(kanban.getHistorico());
            consulta.setString(kanban.getCodPessoa());
            consulta.setString(kanban.getFase());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir ticket - \r\n" + e.getMessage());
        }
    }

    /**
     * Edita um ticket
     *
     * @param kanban
     * @param persistencia
     * @throws Exception
     */
    public void atualizarTicket(KanBan kanban, Persistencia persistencia) throws Exception {
        try {
            String sql = "update kanban set descricao = ?, historico = ?, codpessoa = ?, fase = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(kanban.getDescricao());
            consulta.setString(kanban.getHistorico());
            consulta.setString(kanban.getCodPessoa());
            consulta.setString(kanban.getFase());
            consulta.setBigDecimal(kanban.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar um ticket - \r\n" + e.getMessage());
        }
    }

    public KanBan buscarTicket(String sequencia, Persistencia persistencia) throws Exception {
        KanBan ticket = new KanBan();
        try {
            String sql = " SELECT KanBan.*, pessoa.nome nomePessoa FROM KanBan "
                    + " left join pessoa on pessoa.codigo = kanban.codpessoa"
                    + " WHERE sequencia = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sequencia);
            consult.select();
            while (consult.Proximo()) {
                ticket.setSequencia(consult.getBigDecimal("sequencia"));
                ticket.setDescricao(consult.getString("descricao"));
                ticket.setHistorico(consult.getString("historico"));
                ticket.setCodPessoa(consult.getBigDecimal("codpessoa").toBigInteger().toString());
                ticket.setFase(consult.getString("fase"));
                ticket.setNomePessoa(consult.getString("nomePessoa"));
            }
            consult.Close();
            return ticket;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar tickets eletrônicos - \r\n" + e.getMessage());
        }
    }

    public Integer contarTicketsPendentes(String codPessoa, Persistencia persistencia) throws Exception {
        int total = -1;
        try {
            String sql = "select count(*) total from kanban where codpessoa = ? and fase <> 'FEITO'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                total = consulta.getInt("total");
            }
            consulta.Close();
            persistencia.FechaConexao();
            return total;
        } catch (Exception e) {
            throw new Exception(" Falha ao contar tickets pendentes - " + e.getMessage());
        }
    }

    public String dataUltimoAcesso(String codPessoa, Persistencia persistencia) throws Exception {
        String dt_ultAcPortal = "";
        try {
            String sql = "select dt_ultacportal from pessoa where codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                dt_ultAcPortal = consulta.getString("dt_ultAcPortal");
            }
            consulta.Close();
            return dt_ultAcPortal;
        } catch (Exception e) {
            throw new Exception(" Falha ao buscar data de último acesso - " + e.getMessage());
        }
    }
}

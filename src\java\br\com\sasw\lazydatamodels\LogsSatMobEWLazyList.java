/*
 */
package br.com.sasw.lazydatamodels;

import Controller.SatMobEW.SatMobEWSatWeb;
import Dados.Persistencia;
import SasBeansCompostas.LogsSatMobEW;
import static br.com.sasw.pacotesuteis.utilidades.Numeros.S2I;
import br.com.sasw.utils.Messages;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

/**
 *
 * <AUTHOR>
 */
public class LogsSatMobEWLazyList extends LazyDataModel<LogsSatMobEW> {

    private static final long serialVersionUID = 1L;
    private List<LogsSatMobEW> logsSatMobEWs;
    private final SatMobEWSatWeb satMobEWSatWeb;
    private Persistencia persistencia;
    private BigDecimal codPessoa;
    private String matriculas;
    private String data1;
    private String data2;
    private String nomeColaborador;
    private String ordem;
    private Map filters;
    private Set<String> resumoCategoria = new HashSet();
    private boolean paginacaoDesabilitada;

    public LogsSatMobEWLazyList(Persistencia pst, BigDecimal codPessoa, Map filters) {
        this.satMobEWSatWeb = new SatMobEWSatWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
        this.filters = filters;
        this.matriculas = "";
        this.data1 = "";
        this.data2 = "";
    }

    public LogsSatMobEWLazyList(Persistencia pst, BigDecimal codPessoa, Map filters, String Data1, String Data2) {
        this.satMobEWSatWeb = new SatMobEWSatWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
        this.filters = filters;
        this.matriculas = "";
        this.data1 = Data1;
        this.data2 = Data2;
    }

    public LogsSatMobEWLazyList(Persistencia pst, BigDecimal codPessoa, Map filters, String Matriculas, String NomeColaborador, String Data1, String Data2, String Ordem, Set<String> resumoCategoria, boolean paginacaoDesabilitada) {
        this.satMobEWSatWeb = new SatMobEWSatWeb();
        this.persistencia = pst;
        this.codPessoa = codPessoa;
        this.filters = filters;
        this.matriculas = Matriculas;
        this.data1 = Data1;
        this.data2 = Data2;
        this.nomeColaborador = NomeColaborador;
        this.ordem = Ordem;
        this.resumoCategoria = resumoCategoria;
        this.paginacaoDesabilitada = paginacaoDesabilitada;
    }

    @Override
    public List<LogsSatMobEW> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map f) {
        try {
            /*if (this.matriculas.equals("")) {
                this.logsSatMobEWs = this.satMobEWSatWeb.listagemPaginada(first, pageSize, this.filters, this.codPessoa, this.data1, this.data2, this.persistencia);
            } else {*/
            this.logsSatMobEWs = this.satMobEWSatWeb.listagemPaginada(first,
                    pageSize, this.filters, this.codPessoa, this.matriculas,
                    this.nomeColaborador, this.data1, this.data2, this.ordem,
                    resumoCategoria, paginacaoDesabilitada, this.persistencia);
            //}

            // Filtro adicional por matrícula após retorno do DAO
            String matriculaFiltro = (String) this.filters.get("matricula");
            if (matriculaFiltro != null && !matriculaFiltro.trim().isEmpty()) {
                this.logsSatMobEWs = this.logsSatMobEWs.stream()
                    .filter(log -> log.getMatricula() != null && log.getMatricula().equals(matriculaFiltro.trim()))
                    .collect(java.util.stream.Collectors.toList());
            }

            for (LogsSatMobEW satLog : this.logsSatMobEWs) {
                switch (satLog.getTipo()) {
                    /**
                     * 1 - Batida de Ponto 2 - Relatório 3 - Relatório
                     * Supervisor 4 - Resumo Ronda 5 - Check Ins Supervisores 6
                     * - Rondas 7 - Inspeção
                     *
                     */
                    case "1":
                        satLog.setTitulo((S2I(satLog.getFotos()) % 2 == 1
                                ? Messages.getMessageS("ClockIn") : Messages.getMessageS("ClockOut"))
                                + ": " + satLog.getFuncionario());
                        break;
                    case "2":
                    case "3":
                        satLog.setTitulo(Messages.getMessageS("Relatorio") + ": " + satLog.getTitulo());
                        break;
                    case "4":
                        String chaveRonda[] = satLog.getDetalhes().split(";");
                        String hrInicio = chaveRonda[0];
                        satLog.setTitulo(Messages.getMessageS("InicioRonda") + ": " + Messages.getHoraS(hrInicio, "HH:mm"));
                        break;
                    case "5":
                        satLog.setTitulo((S2I(satLog.getChave().split(";")[2]) % 2 == 1
                                ? Messages.getMessageS("CheckIn") : Messages.getMessageS("CheckOut"))
                                + ": " + satLog.getFuncionario());
                        break;
                    case "9":
                        satLog.setTitulo(Messages.getMessageS("Rota") + ": " + satLog.getFuncionario());
                        break;
                }
            }

            // set the total of players
            if (!data1.equals("") && !data2.equals("")) {
                setRowCount(this.satMobEWSatWeb.contagem(this.filters, 
                        this.codPessoa, this.data1, this.data2, resumoCategoria,
                        this.persistencia));
            } else {
                setRowCount(this.satMobEWSatWeb.contagem(this.filters, this.codPessoa, this.persistencia));
            }

            // set the page dize
            setPageSize(pageSize);
        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.logsSatMobEWs;
    }

    public List<LogsSatMobEW> carregarResumo( Set<String> resumoCategoria) {
        try {
            if (!data1.equals("") && !data2.equals("")) {
                this.logsSatMobEWs = this.satMobEWSatWeb.listagemResumo(0, 9999999, this.filters, this.codPessoa, this.data1, this.data2, resumoCategoria, this.persistencia);
            } else {
                this.logsSatMobEWs = this.satMobEWSatWeb.listagemResumo(0, 9999999, this.filters, this.codPessoa, this.persistencia);
            }

        } catch (Exception e) {
            FacesMessage message = new FacesMessage(FacesMessage.SEVERITY_ERROR, Messages.getMessageS(e.getMessage()), null);
            FacesContext.getCurrentInstance().addMessage(null, message);
        }
        return this.logsSatMobEWs;
    }

    @Override
    public Object getRowKey(LogsSatMobEW logsSatMobEW) {
        return logsSatMobEW.getChave() + ";" + logsSatMobEW.getSecao() + ";" + logsSatMobEW.getCodfil() + ";" + logsSatMobEW.getData() + ";" + logsSatMobEW.getHora() + ";";
    }

    @Override
    public LogsSatMobEW getRowData(String chave) {
        for (LogsSatMobEW logsSatMobEW : this.logsSatMobEWs) {
            if (chave.equals(logsSatMobEW.getChave())) {
                return logsSatMobEW;
            }
        }
        return null;
    }

    public Persistencia getPersistencia() {
        return persistencia;
    }

    public void setPersistencia(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public void setFilters(Map filters) {
        this.filters = filters;
    }
    
    
}

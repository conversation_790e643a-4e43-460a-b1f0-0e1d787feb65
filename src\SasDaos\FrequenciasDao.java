/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Frequencias;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FrequenciasDao {

    /**
     * Lista todas as frequencias para um serviço
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Frequencias> listarFrequencias(Persistencia persistencia) throws Exception {
        List<Frequencias> retorno = new ArrayList<>();
        try {
            String sql = "select * from Frequencias ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Frequencias f;
            while (consulta.Proximo()) {
                f = new Frequencias();
                f.setCodigo(consulta.getBigDecimal("Codigo"));
                f.setCodFil(consulta.getBigDecimal("CodFil"));
                f.setDescricao(consulta.getString("Descricao"));
                f.setOperador(consulta.getString("Operador"));
                f.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                f.setHr_alter(consulta.getString("Hr_alter"));
                retorno.add(f);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list frequencias - " + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.NFiscalCobElet;

/**
 *
 * <AUTHOR>
 */
public class NFiscalCobEletDao {

    public void atualizarLeitura(NFiscalCobElet nfiscal, Persistencia persistencia) throws Exception {
        try {
            String sql = "update NFiscalCobElet set MsgLida = 'S', vBoleto = ?, vNFiscal = ?, vOutros = ?"
                    + " where ordem = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nfiscal.getvBoleto());
            consulta.setString(nfiscal.getvNFiscal());
            consulta.setString(nfiscal.getvOutros());
            consulta.setBigDecimal(nfiscal.getOrdem());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("NFiscalCobEletDao.atualizarLeitura - " + e.getMessage() + "\r\n"
                    + "update NFiscalCobElet set MsgLida = 'S', vBoleto = " + nfiscal.getvBoleto() + ", vNFiscal = " + nfiscal.getvNFiscal() + ", "
                    + "vOutros = " + nfiscal.getvOutros()
                    + " where ordem = " + nfiscal.getOrdem());
        }
    }
}

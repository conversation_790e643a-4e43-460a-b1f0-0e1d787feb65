/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ManifestItem;

/**
 *
 * <AUTHOR>
 */
public class ManifestItemDao {

    public void inserirManifestItem(ManifestItem manifestItem, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ManifestItem (CodFil, SeqRota, Remessa, Guia, Serie, Lacre)\n"
                    + "SELECT ?, ?, ?, ?, ?, ?\n"
                    + "FROM (SELECT COUNT(*) AS qtde_cadastrado FROM ManifestItem \n"
                    + " WHERE CodFil = ? AND SeqRota = ? AND Remessa = ? AND Guia = ? AND Serie = ? AND Lacre = ? ) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(manifestItem.getCodFil());
            consulta.setString(manifestItem.getSeqRota());
            consulta.setString(manifestItem.getRemessa());
            consulta.setString(manifestItem.getGuia());
            consulta.setString(manifestItem.getSerie());
            consulta.setString(manifestItem.getLacre());

            consulta.setString(manifestItem.getCodFil());
            consulta.setString(manifestItem.getSeqRota());
            consulta.setString(manifestItem.getRemessa());
            consulta.setString(manifestItem.getGuia());
            consulta.setString(manifestItem.getSerie());
            consulta.setString(manifestItem.getLacre());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ManifestItemDao.inserirManifestItem - " + e.getMessage());
        }
    }
}

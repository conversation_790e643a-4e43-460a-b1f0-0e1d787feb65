/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.KanBanComent;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class KanBanComentDao {

    /**
     * Insere um novo comentário em um ticket
     *
     * @param comentario
     * @param persistencia
     * @throws Exception
     */
    public void inserirComentario(KanBanComent comentario, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into kanbancoment (Sequencia, Ordem, CodPessoa, Descricao, Detalhes, Operador, Dt_Alter, Hr_Alter)"
                    + " values (?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(comentario.getSequencia());
            consulta.setBigDecimal(comentario.getOrdem());
            consulta.setBigDecimal(comentario.getCodPessoa());
            consulta.setString(comentario.getDescricao());
            consulta.setString(comentario.getDetalhes());
            consulta.setString(comentario.getOperador());
            consulta.setDate(DataAtual.LC2Date(comentario.getDt_Alter()));
            consulta.setString(comentario.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir comentários em um ticket - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca o próximo número da ordem de um ticket
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public BigDecimal getOrdem(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select isnull(MAX(ordem),0)+1 ordem "
                    + " FROM kanbancoment "
                    + " WHERE sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            BigDecimal ordem = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                ordem = consulta.getBigDecimal("ordem");;
            }
            consulta.Close();
            return ordem;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar ordem de um comentário - \r\n" + e.getMessage());
        }
    }

    /**
     * Lista todos os comentários de um ticket
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<KanBanComent> listaComentarios(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "select KanBanComent.*, pessoa.nome nomePessoa "
                    + " from kanbancoment "
                    + " left join pessoa on pessoa.codigo = kanbancoment.codpessoa "
                    + " where KanBanComent.sequencia = ? "
                    + " order by kanbancoment.ordem desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            List<KanBanComent> retorno = new ArrayList<>();
            KanBanComent comentario;
            while (consulta.Proximo()) {
                comentario = new KanBanComent();
                comentario.setCodPessoa(consulta.getBigDecimal("codpessoa"));
                comentario.setDescricao(consulta.getString("descricao"));
                comentario.setDetalhes(consulta.getString("detalhes"));
                comentario.setDt_Alter(consulta.getLocalDate("dt_alter"));
                comentario.setHr_Alter(consulta.getString("hr_alter"));
                comentario.setNomePessoa(consulta.getString("nomePessoa"));
                comentario.setOperador(consulta.getString("Operador"));
                comentario.setOrdem(consulta.getBigDecimal("ordem"));
                comentario.setSequencia(consulta.getBigDecimal("sequencia"));
                retorno.add(comentario);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar ordem de um comentário - \r\n" + e.getMessage());
        }
    }
}

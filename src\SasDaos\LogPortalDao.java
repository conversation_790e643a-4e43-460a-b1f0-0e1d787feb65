package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.sql.SQLException;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LogPortalDao {

    /**
     * Insere na tabela SASLOGPORTAL as operaçoes realizadas no sistema pelo
     * usuário
     *
     * @param mtr
     * @param codfil
     * @param desc
     * @param persist
     * @throws SQLException
     */

    public void insereLog(String mtr, String codfil, String desc, Persistencia persistencia) throws Exception {

        try {

            String sql = "INSERT INTO SASLOGPORTAL(SEQUENCIA, MATR,"
                    + " CODFIL, DATA, HORA, DESCRICAO) VALUES((Select isnull(Max(Sequencia),0) + 1 Sequencia from SASLOGPORTAL),?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);
            consulta.setString(codfil);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(desc);
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Erro ao inserir logportal - " + e.getMessage() + " - ");
        }

    }
    
    public void insereLog(String mtr, String codfil, String desc, String caminhoAssinatura, Persistencia persistencia) throws Exception {

        try {

            String sql = "INSERT INTO SASLOGPORTAL(SEQUENCIA, MATR,"
                    + " CODFIL, DATA, HORA, DESCRICAO, CAMINHOASSINATURA) VALUES((Select isnull(Max(Sequencia),0) + 1 Sequencia from SASLOGPORTAL),?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);
            consulta.setString(codfil);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(desc);
            consulta.setString(caminhoAssinatura);
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Erro ao inserir logportal - " + e.getMessage() + " - ");
        }

    }

    /*  Busca pelos ultimos seis registros de folha de ponto gerados */
 /*  Busca feita pelo usuario */
    public List<String> buscaFolhaPonto(String mtr, Persistencia persistencia) throws Exception {

        List<String> folhas = new ArrayList<>();
        String[] campos, sep;
        String descr, data, ini;

        String sql = "Select top 6 DATA, DESCRICAO from  SASLOGPORTAL where Matr = ? "
                + "and DESCRICAO like 'Folha de Ponto%' "
                + "order by DATA desc, HORA desc";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);

            consulta.select();
            while (consulta.Proximo()) {
                data = consulta.getString("DATA");
                descr = consulta.getString("descricao");
                campos = data.split(" ");
                sep = campos[0].split("-");
                ini = sep[2] + "/" + sep[1] + "/" + sep[0];
                folhas.add(ini + " - " + descr);
            }

            consulta.close();
            return folhas;
        } catch (Exception e) {
            throw new Exception("buscaFolhaPonto logportal- " + e.getMessage());
        }
    }

    /*  Busca pelos ultimos seis registros de contracheque gerados*/
 /*  Busca feita pelo usuario */
    public List<String> buscaCCheque(String mtr, Persistencia persistencia) throws Exception {

        List<String> contrac = new ArrayList<>();
        String[] campos, sep;
        String descr, data;
        String ini;
        String sql = "Select top 6 DATA, DESCRICAO from SASLOGPORTAL "
                + "where Matr = ? and DESCRICAO like 'Contracheque%' "
                + "order by DATA desc, HORA desc";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);
            consulta.select();
            while (consulta.Proximo()) {

                data = consulta.getString("DATA");
                descr = consulta.getString("descricao");
                campos = data.split(" ");
                sep = campos[0].split("-");
                ini = sep[2] + "/" + sep[1] + "/" + sep[0];
                contrac.add(ini + " - " + descr);
            }
            consulta.close();
            return contrac;
        } catch (Exception e) {
            throw new Exception("Erro buscaCCheque logportal- " + e.getMessage());
        }
    }

    /*  Busca pelos registros de contracheque gerados da Matricula */
 /*  Busca feita pelo administrador */
    public ArrayList<String> buscaChequeUsuario(String mtr, String ini, String fim, Persistencia persistencia) throws Exception {

        ArrayList<String> contrac = new ArrayList<>();
        String descr, data;
        String[] campos, sep;
        String sql = "Select DATA,DESCRICAO from SASLOGPORTAL where Matr = ? "
                + "and DATA between ? and ? and DESCRICAO like 'Contracheque%' "
                + "order by DATA desc, HORA desc";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);
            consulta.setString(ini);
            consulta.setString(fim);
            consulta.select();
            while (consulta.Proximo()) {

                data = consulta.getString("DATA");
                descr = consulta.getString("descricao");
                campos = data.split(" ");
                sep = campos[0].split("-");
                ini = sep[2] + "/" + sep[1] + "/" + sep[0];
                contrac.add(ini + " - " + descr);
            }
            consulta.close();
            return contrac;
        } catch (Exception e) {
            throw new Exception("Erro buscaChequeUsuario saslogportal- " + e.getMessage());
        }
    }

    /*  Busca pelos registros de folha de ponto gerados da Matricula */
 /*  Busca feita pelo administrador */
    public ArrayList<String> buscaPontoUsuario(String mtr, String ini, String fim, Persistencia persistencia) throws Exception {

        ArrayList<String> folhas = new ArrayList<>();
        String descr, data;
        String[] campos, sep;
        String sql = "Select DATA,DESCRICAO from SASLOGPORTAL where Matr = ? "
                + "and DATA between ? and ? and DESCRICAO like 'Folha de Ponto%' "
                + "order by DATA desc, HORA desc";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);
            consulta.setString(ini);
            consulta.setString(fim);
            consulta.select();
            while (consulta.Proximo()) {

                data = consulta.getString("DATA");
                descr = consulta.getString("descricao");
                campos = data.split(" ");
                sep = campos[0].split("-");
                ini = sep[2] + "/" + sep[1] + "/" + sep[0];
                folhas.add(ini + " - " + descr);
            }
            consulta.close();
            return folhas;
        } catch (Exception e) {
            throw new Exception("Erro buscaPontoUsuario logportal- " + e.getMessage());
        }
    }

    /*  Busca a ultima troca de senha da MATRICULA mtr*/
    public boolean buscaTrocaSenha(String mtr, Persistencia persistencia) throws Exception {

        boolean retorno = false, troca = false;

        String sql = "Select top 1 DATA from SASLOGPORTAL where MATR = ? "
                + "and DESCRICAO like 'Alteração de senha%' "
                + "order by DATA desc";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mtr);

            consulta.select();

            while (consulta.Proximo()) {
                troca = true; // efetuou uma troca de senha em algum momento
                String data = consulta.getString("data");

                String[] campos = data.split("-");
                String[] dia = campos[2].split(" "); //dia vem dd 00:00:00.0 , retirando a parte dos zeros
                int vl1 = Integer.parseInt(campos[1]);// parte mes
                int vl2 = Integer.parseInt(dia[0]);// parte dia 

                Date d = new Date();
                Format diaHj = new SimpleDateFormat("yyyy-MM-dd");
                String s = diaHj.format(d);

                String[] atual = s.split("-");
                int vlm = Integer.parseInt(atual[1]); //mes atual
                int vld = Integer.parseInt(atual[2]);  //dia atual

                /*Verificaçao se a troca de senha foi feita a seis meses atras*/
                if ((vlm > vl1) && (vlm - vl1) >= 6) { //mes atual maior e diferença maior ou igual a seis meses

                    if (vld >= vl2) //dia atual igual ou maior
                    {
                        retorno = true;
                    }
                } else if ((vlm < vl1) && (vlm - vl1 + 12) >= 6) { //mes atual menor e diferença maior ou igual a seis meses

                    if (vld >= vl2) //dia atual igual ou maior
                    {
                        retorno = true;
                    }
                }

            }
            consulta.close();
            if (troca) { // foi feita troca de senha
                if (retorno) { // em mais de seis meses
                    return true;  //Troca de senha obrigatoria
                } else {
                    return false;  //em menos de seis meses, nao faz troca de senha
                }
            } else {
                return true; //nunca foi feita uma troca de senha. Troca de senha obrigatoria
            }
        } catch (Exception e) {
            throw new Exception("Erro buscaTrocaSenha logpotal- " + e.getMessage());
        }
    }
}

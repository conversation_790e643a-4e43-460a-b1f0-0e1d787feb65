/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Municipios;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MunicipiosDao {

    public List<Municipios> ListaCodMunUf(Persistencia persistencia) throws Exception {
        try {
            List<Municipios> retorno = new ArrayList();
            Municipios municipios;

            String sql = "select codigo ,nome ,UF from municipios";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                municipios = new Municipios();
                municipios.setCodigo(consulta.getBigDecimal("codigo"));
                municipios.setNome(consulta.getString("nome"));
                municipios.setUF(consulta.getString("UF"));
                retorno.add(municipios);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar municipios para mobile \r\n" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> BuscarMunicipio(String query, Persistencia persistencia) throws Exception {
        try {
            List<Municipios> retorno = new ArrayList<>();
            Municipios municipio;
            String sql = "select top 10 nome, uf "
                    + " from municipios"
                    + " where nome COLLATE Latin1_General_CI_AI like ? and codibge is not null "
                    + " order by nome";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();

            while (consult.Proximo()) {
                municipio = new Municipios();
                municipio.setNome(consult.getString("nome"));
                municipio.setUF(consult.getString("uf"));
                retorno.add(municipio);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar municípios \r\n" + e.getMessage());
        }
    }

    /**
     * Valida município pelo estado
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> ValidarMunicipio(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            List<Municipios> retorno = new ArrayList<>();
            Municipios municipio;
            String sql = "select top 30 nome, uf"
                    + " from municipios"
                    + " where nome COLLATE Latin1_General_CI_AI like ? and codibge is not null"
                    + " and uf like ?"
                    + " order by nome";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + cidade.toUpperCase() + "%");
            consult.setString("%" + estado.toUpperCase() + "%");
            consult.select();

            while (consult.Proximo()) {
                municipio = new Municipios();
                municipio.setNome(consult.getString("nome"));
                municipio.setUF(consult.getString("uf"));
                retorno.add(municipio);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar municípios \r\n" + e.getMessage());
        }
    }

    /**
     * Recupera os registros do municipio
     *
     * @param buscar item da busca
     * @param persistencia conexão com a base de dados
     * @return lista de objets
     * @throws Exception
     */
    public List<Municipios> obterMunicipios(String buscar, Persistencia persistencia) throws Exception {
        List<Municipios> municipios = new ArrayList();
        try {
            String sql = "SELECT TOP 40 Codigo, Nome, UF, Pais FROM Municipios WHERE"
                    + " UF LIKE ? OR Nome LIKE ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + buscar + "%");
            consulta.setString("%" + buscar + "%");
            consulta.select();

            Municipios municipio = null;
            while (consulta.Proximo()) {
                municipio = new Municipios();
                municipio.setCodigo(consulta.getBigDecimal("Codigo"));
                municipio.setNome(consulta.getString("Nome"));
                municipio.setUF(consulta.getString("UF"));
                municipio.setPais(consulta.getString("Pais"));
                municipios.add(municipio);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return municipios;
    }

    /**
     * Recupera os registros do municipio
     *
     * @param persistencia conexão com a base de dados
     * @return lista de objets
     * @throws Exception
     */
    public List<Municipios> obterMunicipios(Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT Codigo, Nome, UF, Pais FROM Municipios";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            List<Municipios> municipios = new ArrayList();
            Municipios municipio;
            while (consulta.Proximo()) {
                municipio = new Municipios();
                municipio.setCodigo(consulta.getBigDecimal("Codigo"));
                municipio.setNome(consulta.getString("Nome"));
                municipio.setUF(consulta.getString("UF"));
                municipio.setPais(consulta.getString("Pais"));
                municipios.add(municipio);
            }
            consulta.Close();
            return municipios;
        } catch (Exception e) {
            throw new Exception("MunicipiosDao.obterMunicipios - " + e.getMessage() + "\r\n"
                    + "SELECT Codigo, Nome, UF, Pais FROM Municipios");
        }
    }

    /**
     * Busca o municipio pelo código
     *
     * @param codigo Código do muncipio
     * @param persistencia Conexão com o banco de dados
     * @return Retorna o município
     * @throws Exception
     */
    public Municipios obtemMuncipio(BigDecimal codigo, Persistencia persistencia) throws Exception {
        Municipios municipios = new Municipios();

        try {
            String sql = "SELECT codigo, nome, UF "
                    + "FROM municipios "
                    + "WHERE codigo = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codigo);
            consulta.select();

            while (consulta.Proximo()) {
                municipios.setCodigo(consulta.getBigDecimal("codigo"));
                municipios.setNome(consulta.getString("nome"));
                municipios.setUF(consulta.getString("UF"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Erro ao buscar muncipio: " + e.getMessage());
        }

        return municipios;
    }

}

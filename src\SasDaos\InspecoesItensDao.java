/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.InspecoesItens;
import br.com.sasw.pacotesuteis.sasbeans.InspecoesItensLista;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class InspecoesItensDao {

    /**
     * Seleciona todos os itens de uma inspeção específica
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<InspecoesItens> getItensInspecao(String codigo, Persistencia persistencia) throws Exception {
        String HTML = "";
        try {
            List<InspecoesItens> retorno = new ArrayList<>();
            String sql = "SELECT\n"
                    + "    ISNULL(InspecoesItensLista.Codigo, 0) ListaCodigo, \n"
                    + "    InspecoesItensLista.Ordem,\n"
                    + "    InspecoesItensLista.Descricao,\n"
                    + "    InspecoesItensLista.Item,\n"
                    + "    InspecoesItens.*\n"
                    + "FROM\n"
                    + "    InspecoesItens\n"
                    + "LEFT JOIN\n"
                    + "    InspecoesItensLista ON InspecoesItensLista.Codigo = InspecoesItens.Codigo\n"
                    + "                        AND InspecoesItensLista.Sequencia = InspecoesItens.Sequencia\n"
                    + "WHERE\n"
                    + "    InspecoesItens.Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            InspecoesItens inspecoesItens;
            InspecoesItensLista inspecoesItensLista;
            int indice;
            String sequencia = "";
            String Opcoes = "";

            while (consulta.Proximo()) {
                inspecoesItens = new InspecoesItens();
                inspecoesItens.setCodigo(consulta.getString("codigo"));
                inspecoesItens.setSequencia(consulta.getString("sequencia").replace(".0", ""));
                inspecoesItens.setPergunta(consulta.getString("pergunta"));
                inspecoesItens.setTipoResp(consulta.getString("TipoResp"));
                inspecoesItens.setObrigatorio(consulta.getString("Obrigatorio"));
                inspecoesItens.setFoto(consulta.getString("Foto"));
                inspecoesItens.setVideo(consulta.getString("Video"));
                inspecoesItens.setOperador(consulta.getString("Operador"));
                inspecoesItens.setDt_Alter(consulta.getString("Dt_Alter"));
                inspecoesItens.setHr_Alter(consulta.getString("Hr_Alter"));

                indice = retorno.indexOf(inspecoesItens);

                if (!consulta.getString("ListaCodigo").replace(".0", "").equals("0")) {
                    if (sequencia.equals(consulta.getString("sequencia").replace(".0", ""))) {
                        HTML += "<div ref=\"itemResposta\" style=\"position: relative; width: 100%; margin-top: 5px !important\">\n";
                        HTML += " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 5px 4px 0px 0px !important; float: left\">\n";
                    } else {
                        HTML += "<div ref=\"itemResposta\" style=\"position: relative; width: 100%; padding-top: 5px !important\">\n";
                        HTML += " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 0px 4px 0px 0px !important\">\n";
                    }
                    HTML += "    <input type=\"text\" class=\"form-control\" ref=\"txtNovoOpcaoItem\" sequencia=\"" + consulta.getString("sequencia").replace(".0", "") + "\" value=\"" + consulta.getString("Item") + "\" /></div>\n";
                    if (sequencia.equals(consulta.getString("sequencia").replace(".0", ""))) {
                        HTML += " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 5px 0px 0px 4px !important; float: left; width: calc(50% - 28px) !important\">\n"
                                + "    <input type=\"text\" class=\"form-control\" ref=\"txtNovoOpcaoDescricao\" sequencia=\"" + consulta.getString("sequencia").replace(".0", "") + "\" value=\"" + consulta.getString("Descricao") + "\" />\n"
                                + " </div>\n"
                                + " <i class=\"fa fa-minus-square\" ref=\"btExcluirPossivelRespostaEdicao_" + consulta.getString("sequencia").replace(".0", "") + "\"  style=\"font-size: 12pt; color: red; cursor:pointer; float: right; padding-top: 14px !important; padding-right: 8px !important\" title=\"localemsgs.Excluir\"></i>\n";
                    } else {

                        HTML += " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 0px 0px 0px 4px !important\">\n"
                                + "    <input type=\"text\" class=\"form-control\" ref=\"txtNovoOpcaoDescricao\" sequencia=\"" + consulta.getString("sequencia").replace(".0", "") + "\"  value=\"" + consulta.getString("Descricao") + "\" />\n"
                                + " </div>\n";
                    }
                    HTML += "</div>";

                    inspecoesItensLista = new InspecoesItensLista();
                    inspecoesItensLista.setCodigo(consulta.getString("ListaCodigo"));
                    inspecoesItensLista.setSequencia(consulta.getString("sequencia").replace(".0", ""));
                    inspecoesItensLista.setOrdem(consulta.getString("Ordem"));
                    inspecoesItensLista.setDescricao(consulta.getString("Descricao"));

                    Opcoes = consulta.getString("Item") + "sasw_separator1" + consulta.getString("Descricao");
                    inspecoesItensLista.setItem(consulta.getString("Item"));  
                } else {
                    inspecoesItensLista = null;
                }

                if (indice >= 0) {
                    if (inspecoesItensLista != null) {
                        retorno.get(indice).getItensLista().add(inspecoesItensLista);
                        retorno.get(indice).setOpcoesRespostaHTML(retorno.get(indice).getOpcoesRespostaHTML() + HTML);

                        if (null != retorno.get(indice).getOpcoesResposta()
                                && !retorno.get(indice).getOpcoesResposta().equals("")) {
                            retorno.get(indice).setOpcoesResposta(retorno.get(indice).getOpcoesResposta() + "sasw_separator2" + Opcoes);
                        } else {
                            retorno.get(indice).setOpcoesResposta(Opcoes);
                        }
                    }
                } else {
                    if (inspecoesItensLista != null) {
                        inspecoesItens.getItensLista().add(inspecoesItensLista);
                    }

                    if (HTML.equals("")) {
                        HTML += "<div ref=\"itemResposta\" style=\"position: relative; width: 100%; padding-top: 5px !important\">\n"
                                + " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 0px 4px 0px 0px !important\">\n"
                                + "    <input type=\"text\" class=\"form-control\" ref=\"txtNovoOpcaoItem\" sequencia=\"" + consulta.getString("sequencia").replace(".0", "") + "\" value=\"\" /></div>\n"
                                + " <div class=\"col-md-6 col-sm-6 col-xs-6\" style=\"padding: 0px 0px 0px 4px !important\">\n"
                                + "    <input type=\"text\" class=\"form-control\" ref=\"txtNovoOpcaoDescricao\" sequencia=\"" + consulta.getString("sequencia").replace(".0", "") + "\"  value\"\" />\n"
                                + " </div>\n"
                                + "</div>";
                    }

                    inspecoesItens.setOpcoesResposta(Opcoes);
                    inspecoesItens.setOpcoesRespostaHTML(HTML);
                    retorno.add(inspecoesItens);
                }

                sequencia = consulta.getString("sequencia").replace(".0", "");
                Opcoes = "";
                HTML = "";
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("InspecoesItensDao.getItensInspecao - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    + "    ISNULL(InspecoesItensLista.Codigo, 0) ListaCodigo, \n"
                    + "    InspecoesItensLista.Ordem,\n"
                    + "    InspecoesItensLista.Descricao,\n"
                    + "    InspecoesItensLista.Item,\n"
                    + "    InspecoesItens.*\n"
                    + "FROM\n"
                    + "    InspecoesItens\n"
                    + "LEFT JOIN\n"
                    + "    InspecoesItensLista ON InspecoesItensLista.Codigo = InspecoesItens.Codigo\n"
                    + "                        AND InspecoesItensLista.Sequencia = InspecoesItens.Sequencia\n"
                    + "WHERE\n"
                    + "    InspecoesItens.Codigo = " + codigo);
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecaoItem
     * @param persistencia
     * @throws Exception
     */
    public void putInspecaoItem(InspecoesItens inspecaoItem, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO InspecoesItens (Sequencia, Codigo, Pergunta, TipoResp, Obrigatorio, Foto, Video, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES ((SELECT ISNULL(MAX(Sequencia),0) + 1 Sequencia FROM InspecoesItens where codigo = ?), ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getPergunta());
            consulta.setString(inspecaoItem.getTipoResp());
            consulta.setString(inspecaoItem.getObrigatorio());
            consulta.setString(inspecaoItem.getFoto());
            consulta.setString(inspecaoItem.getVideo());
            consulta.setString(inspecaoItem.getOperador());
            consulta.setString(inspecaoItem.getDt_Alter());
            consulta.setString(inspecaoItem.getHr_Alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.putInspecao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO InspecoesItens (Sequencia, Codigo, Pergunta, TipoResp, Foto, Video, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES ((SELECT ISNULL(MAX(Codigo),0) + 1 Sequencia FROM InspecoesItens), ?, ?, ?, ?, ?, ?, ?, ?) ");
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecaoItem
     * @param inspecoesItensLista
     * @param persistencia
     * @throws Exception
     */
    public void putInspecaoItem(InspecoesItens inspecaoItem, List<InspecoesItensLista> inspecoesItensLista, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DECLARE @inSequencia Float;\n"
                    + " SET @inSequencia = (SELECT ISNULL(MAX(Sequencia),0) + 1 Sequencia FROM InspecoesItens where codigo = ?);";

            sql += " INSERT INTO InspecoesItens (Sequencia, Codigo, Pergunta, TipoResp, Obrigatorio, Foto, Video, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES (@inSequencia, ?, ?, ?, ?, ?, ?, ?, ?, ?); ";

            if (inspecoesItensLista.size() > 0) {
                for (InspecoesItensLista inspecoesItensLista1 : inspecoesItensLista) {
                    sql += " INSERT INTO InspecoesItensLista (Codigo, Sequencia, Ordem, Item, Descricao, Operador, Dt_Alter, Hr_Alter) "
                            + " VALUES (?, @inSequencia, ?, ?, ?, ?, ?, ?); ";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getPergunta());
            consulta.setString(inspecaoItem.getTipoResp());
            consulta.setString(inspecaoItem.getObrigatorio());
            consulta.setString(inspecaoItem.getFoto());
            consulta.setString(inspecaoItem.getVideo());
            consulta.setString(inspecaoItem.getOperador());
            consulta.setString(inspecaoItem.getDt_Alter());
            consulta.setString(inspecaoItem.getHr_Alter());

            if (inspecoesItensLista.size() > 0) {
                for (InspecoesItensLista inspecoesItensLista1 : inspecoesItensLista) {
                    consulta.setString(inspecaoItem.getCodigo());
                    consulta.setString(inspecoesItensLista1.getOrdem());
                    consulta.setString(inspecoesItensLista1.getItem());
                    consulta.setString(inspecoesItensLista1.getDescricao());
                    consulta.setString(inspecaoItem.getOperador());
                    consulta.setString(inspecaoItem.getDt_Alter());
                    consulta.setString(inspecaoItem.getHr_Alter());
                }
            }

            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.putInspecao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Apaga uma inspeção no banco
     *
     * @param inspecaoItem
     * @param persistencia
     * @throws Exception
     */
    public void deleteInspecaoItem(InspecoesItens inspecaoItem, Persistencia persistencia) throws Exception {
        String sql="";
        try {
             sql = " DELETE FROM InspecoesItens "
                    + " WHERE Codigo = ? AND Sequencia = ?; ";
            
            sql += " DELETE FROM InspecoesItensLista"
                    + " WHERE Codigo    = ?"
                    + " AND   Sequencia = ?;";
            
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getSequencia());
            
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getSequencia());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.putInspecao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Edita uma inspeção no banco
     *
     * @param inspecaoItem
     * @param inspecoesItensLista
     * @param persistencia
     * @throws Exception
     */
    public void updateInspecaoItem(InspecoesItens inspecaoItem, List<InspecoesItensLista> inspecoesItensLista, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = " UPDATE InspecoesItens SET Pergunta = ?, TipoResp = ?, Obrigatorio = ?, Foto = ?, Video = ?, Operador = ?,"
                    + " Dt_Alter = ?, Hr_Alter = ? "
                    + " WHERE Codigo = ? AND Sequencia = ?; ";

            sql += " DELETE FROM InspecoesItensLista"
                    + " WHERE Codigo    = ?"
                    + " AND   Sequencia = ?;";

            if (inspecoesItensLista.size() > 0) {
                for (InspecoesItensLista inspecoesItensLista1 : inspecoesItensLista) {
                    sql += " INSERT INTO InspecoesItensLista (Codigo, Sequencia, Ordem, Item, Descricao, Operador, Dt_Alter, Hr_Alter) "
                            + " VALUES (?, ?, ?, ?, ?, ?, ?, ?); ";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecaoItem.getPergunta());
            consulta.setString(inspecaoItem.getTipoResp());
            consulta.setString(inspecaoItem.getObrigatorio());
            consulta.setString(inspecaoItem.getFoto());
            consulta.setString(inspecaoItem.getVideo());
            consulta.setString(inspecaoItem.getOperador());
            consulta.setString(inspecaoItem.getDt_Alter());
            consulta.setString(inspecaoItem.getHr_Alter());
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getSequencia());
            consulta.setString(inspecaoItem.getCodigo());
            consulta.setString(inspecaoItem.getSequencia());

            if (inspecoesItensLista.size() > 0) {
                for (InspecoesItensLista inspecoesItensLista1 : inspecoesItensLista) {
                    consulta.setString(inspecaoItem.getCodigo());
                    consulta.setString(inspecaoItem.getSequencia());
                    consulta.setString(inspecoesItensLista1.getOrdem());
                    consulta.setString(inspecoesItensLista1.getItem());
                    consulta.setString(inspecoesItensLista1.getDescricao());
                    consulta.setString(inspecaoItem.getOperador());
                    consulta.setString(inspecaoItem.getDt_Alter());
                    consulta.setString(inspecaoItem.getHr_Alter());
                }
            }

            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.updateInspecaoItem - " + e.getMessage() + "\r\n" + sql);
        }
    }
}

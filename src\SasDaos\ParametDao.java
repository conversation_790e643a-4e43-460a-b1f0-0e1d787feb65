package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Paramet;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ParametDao {

    /**
     * Lista todas as moedas do parâmetro
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Paramet> getParamet(Persistencia persistencia) throws Exception {
        try {
            List<Paramet> retorno = new ArrayList<>();
            String sql = "SELECT \n"
                    + "     *\n"
                    + "FROM \n"
                    + "     Paramet\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Paramet paramet;
            while (consulta.Proximo()) {
                paramet = new Paramet();
                paramet.setSequencia(consulta.getString("Sequencia"));
                paramet.setCodEmpresa(consulta.getString("CodEmpresa"));
                paramet.setFilial_PDR(consulta.getString("Filial_PDR"));
                paramet.setNome_empr(consulta.getString("Nome_empr"));
                paramet.setCidade_PDR(consulta.getString("Cidade_PDR"));
                paramet.setUF_PDR(consulta.getString("UF_PDR"));
                paramet.setPath(consulta.getString("Path"));
                paramet.setTipo(consulta.getString("Tipo"));
                paramet.setUsuario(consulta.getString("Usuario"));
                paramet.setSenha(consulta.getString("Senha"));
                paramet.setHostName(consulta.getString("HostName"));
                paramet.setHostNameWEB(consulta.getString("HostNameWEB"));
                paramet.setBancoDados(consulta.getString("BancoDados"));
                paramet.setPathSatelite(consulta.getString("PathSatelite"));
                paramet.setPathEagle(consulta.getString("PathEagle"));
                paramet.setPathFotos(consulta.getString("PathFotos"));
                paramet.setPathDoctos(consulta.getString("PathDoctos"));
                paramet.setPathLogoNFE(consulta.getString("PathLogoNFE"));
                paramet.setNetDir(consulta.getString("NetDir"));
                paramet.setVersao(consulta.getString("Versao"));
                paramet.setCodCliCxf(consulta.getString("CodCliCxf"));
                paramet.setDiaSem1(consulta.getString("DiaSem1"));
                paramet.setAbonoPdr(consulta.getString("AbonoPdr"));
                paramet.setDescIntrajHE(consulta.getString("DescIntrajHE"));
                paramet.setCodFornItau(consulta.getString("CodFornItau"));
                paramet.setEscTolerMot(consulta.getInt("EscTolerMot"));
                paramet.setEscTolerChe(consulta.getInt("EscTolerChe"));
                paramet.setEscTolerVig(consulta.getInt("EscTolerVig"));
                paramet.setEscTolerOutros(consulta.getString("EscTolerOutros"));
                paramet.setPtoTolerMot(consulta.getString("PtoTolerMot"));
                paramet.setPtoTolerChe(consulta.getString("PtoTolerChe"));
                paramet.setPtoTolerVig(consulta.getString("PtoTolerVig"));
                paramet.setPtoTolerOutros(consulta.getString("PtoTolerOutros"));
                paramet.setAceTolerMot(consulta.getString("AceTolerMot"));
                paramet.setAceTolerChe(consulta.getString("AceTolerChe"));
                paramet.setAceTolerVig(consulta.getString("AceTolerVig"));
                paramet.setAceTolerOutros(consulta.getString("AceTolerOutros"));
                paramet.setFusoHorario(consulta.getString("FusoHorario"));
                paramet.setFusoHorarioSEFAZ(consulta.getString("FusoHorarioSEFAZ"));
                paramet.setLimiteSeg(consulta.getString("LimiteSeg"));
                paramet.setLimiteCxf(consulta.getString("LimiteCxf"));
                paramet.setLimiteTes(consulta.getString("LimiteTes"));
                paramet.setAutoFechMobile(consulta.getString("AutoFechMobile"));
                paramet.setConexaoPadraoBD(consulta.getString("ConexaoPadraoBD"));
                paramet.setTrocaSenhaMobile(consulta.getString("TrocaSenhaMobile"));
                paramet.setSeqParamPdr(consulta.getString("SeqParamPdr"));
                paramet.setMoedaPdrMobile(consulta.getString("MoedaPdrMobile"));
                paramet.setOperador(consulta.getString("Operador"));
                paramet.setDt_Alter(consulta.getString("Dt_Alter"));
                paramet.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(paramet);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getParamet - " + e.getMessage() + ""
                    + "SELECT \n"
                    + "     *\n"
                    + "FROM \n"
                    + "     Paramet");
        }
    }

    public Paramet getParametGoogleApi(String Path, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            Paramet paramet = new Paramet();

            sql = "SELECT TOP 1\n"
                    + " COALESCE(GoogleApiMob, 'AIzaSyBQMoOZXerunkJBmQPxAb0ZdphbF9KSaFw') GoogleApiMob,\n"
                    + " COALESCE(GoogleApiOper, 'AIzaSyBLXQgn09RZmoQ6NfHxGQaR7Y0M5eFTMts') GoogleApiOper,\n"
                    + " UtilizaGTVe,\n"
                    + " TranspCacamba,\n"
                    + " FirmaGtv,\n"
                    + " CapturaValor\n"
                    + " FROM Paramet\n"
                    + " WHERE Path = ?\n";
            /*+ " AND   (GoogleApiMob  IS NOT NULL OR GoogleApiMob  <> '')\n"
                    + " AND   (GoogleApiOper IS NOT NULL OR GoogleApiOper <> '')";*/

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Path);
            consulta.select();

            while (consulta.Proximo()) {
                if (null != consulta.getString("GoogleApiMob") && !consulta.getString("GoogleApiMob").equals("")) {
                    paramet.setGoogleApiMob(consulta.getString("GoogleApiMob"));
                }

                if (null != consulta.getString("GoogleApiOper") && !consulta.getString("GoogleApiOper").equals("")) {
                    paramet.setGoogleApiOper(consulta.getString("GoogleApiOper"));
                }

                if (null != consulta.getString("UtilizaGTVe") && !consulta.getString("UtilizaGTVe").equals("")) {
                    paramet.setUtilizaGTVe(consulta.getString("UtilizaGTVe"));
                }
                
                if (null != consulta.getString("TranspCacamba") && !consulta.getString("TranspCacamba").equals("")) {
                    paramet.setTranspCacamba(consulta.getString("TranspCacamba"));
                }
                
                if (null != consulta.getString("FirmaGtv") && !consulta.getString("FirmaGtv").equals("")) {
                    paramet.setFirmaGtv(consulta.getString("FirmaGtv"));
                }
                
                 if (null != consulta.getString("CapturaValor") && !consulta.getString("CapturaValor").equals("")) {
                    paramet.setCapturaValor(consulta.getString("CapturaValor"));
                }
            }

            if (null == paramet.getGoogleApiMob()
                    || paramet.getGoogleApiMob().equals("")) {
                paramet.setGoogleApiMob("AIzaSyBQMoOZXerunkJBmQPxAb0ZdphbF9KSaFw");
            }

            if (null == paramet.getGoogleApiOper()
                    || paramet.getGoogleApiOper().equals("")) {
                paramet.setGoogleApiOper("AIzaSyBLXQgn09RZmoQ6NfHxGQaR7Y0M5eFTMts");
            }

            if (null == paramet.getTranspCacamba()
                    || paramet.getTranspCacamba().equals("")) {
                paramet.setTranspCacamba("0");
            }

            if (null == paramet.getUtilizaGTVe()
                    || paramet.getUtilizaGTVe().equals("")) {
                paramet.setUtilizaGTVe("0");
            }
            
            if (null == paramet.getFirmaGtv()
                    || paramet.getFirmaGtv().equals("")) {
                paramet.setFirmaGtv("0");
            }

            if (null == paramet.getCapturaValor()
                    || paramet.getCapturaValor().equals("")) {
                paramet.setCapturaValor("0");
            }
            
            consulta.Close();
            return paramet;
        } catch (Exception e) {
            throw new Exception("ParametDao.getParametGoogleApi - " + e.getMessage() + ""
                    + sql);
        }
    }

    public String getMoedaPdrMobile(String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT MoedaPdrMobile\n"
                    + "FROM Paramet\n"
                    + "WHERE Filial_PDR = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String retorno;
            if (consulta.Proximo()) {
                retorno = consulta.getString("MoedaPdrMobile");
            } else {
                retorno = "BRL";
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getMoedaPdrMobile - " + e.getMessage() + ""
                    + "SELECT MoedaPdrMobile\n"
                    + "FROM Paramet\n"
                    + "WHERE Filial_PDR = " + codFil);
        }
    }

    public String getLogo(String bancoDados, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "SELECT PathSatelite \n"
                    + " FROM paramet\n"
                    + " WHERE (BancoDados = ? OR Path = ?)\n"
                    + " AND   PathSatelite IS NOT NULL \n"
                    + " AND   PathSatelite <> ''";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(bancoDados);
            consulta.setString(bancoDados);
            consulta.select();
            String retorno = "";

            if (consulta.Proximo()) {
                retorno = consulta.getString("PathSatelite");
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getLogo - " + e.getMessage() + sql);
        }
    }

    public String getLogo(String bancoDados, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "SELECT * FROM Paramet WHERE Filial_PDR = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String retorno = "";

            if (consulta.Proximo()) {
                retorno = consulta.getString("PathSatelite");
            }
            consulta.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getLogo - " + e.getMessage() + sql);
        }
    }

    public static String getMoedaPdrMobileS(String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT MoedaPdrMobile\n"
                    + "FROM Paramet\n"
                    + "WHERE Filial_PDR = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            String retorno;
            if (consulta.Proximo()) {
                retorno = consulta.getString("MoedaPdrMobile");
            } else {
                retorno = "BRL";
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getMoedaPdrMobile - " + e.getMessage() + ""
                    + "SELECT MoedaPdrMobile\n"
                    + "FROM Paramet\n"
                    + "WHERE Filial_PDR = " + codFil);
        }
    }

    public List<Paramet> getPath(Persistencia persistencia) throws Exception {
        List<Paramet> lparamet = new ArrayList();
        String sql;
        sql = "select nome_empr, path, usuario, senha, hostnameweb, bancodados from paramet";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            Paramet paramet;
            while (consult.Proximo()) {
                paramet = new Paramet();
                paramet.setNome_empr(consult.getString("nome_empr"));
                paramet.setPath(consult.getString("path"));
                paramet.setUsuario(consult.getString("usuario"));
                paramet.setSenha(consult.getString("senha"));
                paramet.setHostNameWEB(consult.getString("hostnameweb"));
                paramet.setBancoDados(consult.getString("bancodados"));
                lparamet.add(paramet);
            }
            consult.Close();
            return lparamet;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar Paramet - " + e.getMessage());
        }
    }

    public List<Paramet> getAllParamets(Persistencia persistencia) throws Exception {
        List<Paramet> listp = new ArrayList();
        String sql = "select filial_pdr, nome_empr, cidade_pdr, uf_pdr, path from paramet order by nome_empr";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                Paramet pl = new Paramet();
                pl.setFilial_PDR(consult.getString("filial_pdr").replace(".0", ""));
                pl.setNome_empr(consult.getString("nome_empr"));
                pl.setCidade_PDR(consult.getString("cidade_pdr"));
                pl.setUF_PDR(consult.getString("uf_pdr"));
                pl.setPath(consult.getString("path"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar empresas da base central - " + e.getMessage());
        }
    }

    /**
     * Busca a lista de Parametros de conexão com o param passado Deve sempre
     * receber uma conexão ao banco de dados central
     *
     * @param Param - Parametro de conexão
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @return - lista de parametros de conexão para o parametro
     * @throws Exception
     */
    public List<Paramet> getParamPadrao(String Param, Persistencia persistencia) throws Exception {
        try {
            List<Paramet> retorno = new ArrayList();
            String sql = "select paramet.Sequencia, pdr_paramet.Path, paramet.SeqParamPdr "
                    + " from paramet "
                    + " left join paramet as pdr_paramet on pdr_paramet.sequencia = paramet.SeqParamPdr"
                    + " where paramet.path like ?";
            Consulta consult = new Consulta(sql, persistencia);
            //consult.setString("%" + Param + "%");
            if (Param.equals("AGILSERV")) {
                consult.setString("SATAGIL");
            } else {
                consult.setString(Param);
            }
            consult.select();
            Paramet paramet;
            while (consult.Proximo()) {
                paramet = new Paramet();
                paramet.setSequencia(consult.getString("Sequencia"));
                paramet.setPath(consult.getString("Path").toUpperCase());
                //Regra alterada - Raphael - 13/01/2017
                /*if(consult.getString("Path").toUpperCase().equals("SATAGIL")){
                    paramet.setPath("AGILSERV");
                }
                else{
                    paramet.setPath(consult.getString("Path").toUpperCase());
                }*/

                paramet.setSeqParamPdr(consult.getString("SeqParamPdr"));
                retorno.add(paramet);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar parametro de validação de usuário -\r\n" + e.getMessage());
        }
    }

    /**
     * Busca o parametro de conexão
     *
     * @param Sequencia - sequencia dentro da paramet
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public Paramet getParam(String Sequencia, Persistencia persistencia) throws Exception {
        try {
            Paramet retorno = new Paramet();
            String sql = "select Sequencia, Path, SeqParamPdr from paramet where sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Sequencia);
            consult.select();
            while (consult.Proximo()) {
                retorno = new Paramet();
                retorno.setSequencia(consult.getString("Sequencia"));
                retorno.setPath(consult.getString("Path"));
                retorno.setSeqParamPdr(consult.getString("SeqParamPdr"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getParam - " + e.getMessage() + "\r\n"
                    + "select Sequencia, Path, SeqParamPdr from paramet where sequencia = " + Sequencia);
        }
    }

    /**
     * Busca o parametro de conexão
     *
     * @param codFil - filial da empresa
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public Paramet getParametFilial(String codFil, Persistencia persistencia) throws Exception {
        try {
            Paramet retorno = null;
            String sql = "select * from paramet where Filial_PDR = ? AND Path = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.setString(persistencia.getEmpresa());
            consult.select();
            if (consult.Proximo()) {
                retorno = new Paramet();
                retorno.setSequencia(consult.getString("Sequencia"));
                retorno.setPath(consult.getString("Path"));
                retorno.setSeqParamPdr(consult.getString("SeqParamPdr"));
                retorno.setEscTolerMot(consult.getInt("EscTolerMot"));
                retorno.setEscTolerChe(consult.getInt("EscTolerChe"));
                retorno.setEscTolerVig(consult.getInt("EscTolerVig"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ParametDao.getParametFilial - " + e.getMessage() + "\r\n"
                    + "select * from paramet where Filial_PDR = " + codFil);
        }
    }

    /**
     * Busca descrição da filial
     *
     * @param CodFil - código da filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String getDescricaoEmpresa(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            String retorno = "";
            String sql = "Select Nome_empr from paramet where Filial_PDR = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("Nome_empr");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar descricao de filial\r\n" + e.getMessage());
        }
    }

    public long getFusoHorario(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            long retorno = 0;
            String sql = "Select FusoHorario from paramet where Filial_PDR = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getInt("FusoHorario");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar descricao de filial\r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param bancoDados - Nome BD
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @return - lista de parametros de conexão para o parametro
     * @throws Exception
     */
    public String validarParametro(String bancoDados, Persistencia persistencia) throws Exception {
        try {
            List<Paramet> retorno = new ArrayList();
            String sql = "select COUNT(*) qtde\n"
                    + " from Paramet\n"
                    + " WHERE BancoDados = ?";

            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(bancoDados);
            consult.select();

            String Qtde = "";
            while (consult.Proximo()) {
                Qtde = consult.getString("qtde");
            }
            consult.Close();
            return Qtde;
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.validarParametro - \r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param paramet - Dados Paramet
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @return - insere novo parâmetro em tabela paramet no banco Central
     * @throws Exception
     */
    public void criarParametroCentral(Paramet paramet, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Paramet> retorno = new ArrayList();
            sql = "DECLARE @Sequencia AS INT;\n"
                    + " SET @Sequencia = (SELECT (ISNULL(MAX(Sequencia),0) + 1) FROM paramet);";

            sql += " INSERT INTO paramet (Sequencia,\n"
                    + " CodEmpresa,\n"
                    + " Filial_PDR,\n"
                    + " Nome_empr,\n"
                    + " Cidade_PDR,\n"
                    + " UF_PDR,\n"
                    + " Path,\n"
                    + " Tipo,\n"
                    + " Usuario,\n"
                    + " Senha,\n"
                    + " HostName,\n"
                    + " HostNameWEB,\n"
                    + " BancoDados,\n"
                    + " PathSatelite,\n"
                    + " PathFotos,\n"
                    + " PathDoctos,\n"
                    + " PathLogoNFE,\n"
                    + " PathEagle,\n"
                    + " EscTolerMot,\n"
                    + " EscTolerChe,\n"
                    + " EscTolerOutros,\n"
                    + " EscTolerVig,\n"
                    + " PtoTolerChe,\n"
                    + " PtoTolerMot,\n"
                    + " PtoTolerOutros,\n"
                    + " PtoTolerVig,\n"
                    + " AceTolerChe,\n"
                    + " AceTolerMot,\n"
                    + " AceTolerOutros,\n"
                    + " AceTolerVig,\n"
                    + " FusoHorario,\n"
                    + " FusoHorarioSEFAZ,\n"
                    + " LimiteSeg, \n"
                    + " LimiteCxf,\n"
                    + " LimiteTes,\n"
                    + " TrocaSenhaMobile,\n"
                    + " Operador,\n"
                    + " Dt_Alter,\n"
                    + " Hr_Alter, UtilizaGTVe, TranspCacamba, FirmaGtv, CapturaValor) VALUES(@Sequencia, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(paramet.getCodEmpresa());
            consult.setString(paramet.getFilial_PDR());
            consult.setString(paramet.getNome_empr());
            consult.setString(paramet.getCidade_PDR());
            consult.setString(paramet.getUF_PDR().toUpperCase());
            consult.setString(paramet.getPath());
            consult.setString("2");
            consult.setString(paramet.getUsuario());
            consult.setString(paramet.getSenha());
            consult.setString(paramet.getHostName());
            consult.setString(paramet.getHostNameWEB());
            consult.setString(paramet.getBancoDados());
            consult.setString(paramet.getPathSatelite());
            consult.setString(paramet.getPathFotos());
            consult.setString(paramet.getPathDoctos());
            consult.setString(paramet.getPathLogoNFE());
            consult.setString(paramet.getPathEagle());
            consult.setInt(paramet.getEscTolerMot());
            consult.setInt(paramet.getEscTolerChe());
            consult.setString(paramet.getEscTolerOutros());
            consult.setInt(paramet.getEscTolerVig());
            consult.setString(paramet.getPtoTolerChe());
            consult.setString(paramet.getPtoTolerMot());
            consult.setString(paramet.getPtoTolerOutros());
            consult.setString(paramet.getPtoTolerVig());
            consult.setString(paramet.getAceTolerChe());
            consult.setString(paramet.getAceTolerMot());
            consult.setString(paramet.getAceTolerOutros());
            consult.setString(paramet.getAceTolerVig());
            consult.setString(paramet.getFusoHorario());
            consult.setString(paramet.getFusoHorarioSEFAZ());
            consult.setString(paramet.getLimiteSeg());
            consult.setString(paramet.getLimiteCxf());
            consult.setString(paramet.getLimiteTes());
            consult.setString(paramet.getTrocaSenhaMobile());
            consult.setString(paramet.getOperador());
            consult.setString(paramet.getDt_Alter());
            consult.setString(paramet.getHr_Alter());
            
            consult.setString(paramet.getUtilizaGTVe());
            consult.setString(paramet.getTranspCacamba());
            consult.setString(paramet.getFirmaGtv());
            consult.setString(paramet.getCapturaValor());

            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.criarParametroCentral - \r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param paramet - Dados Paramet
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @return - insere novo parâmetro em tabela paramet no banco Local
     * @throws Exception
     */
    public void criarParametroLocal(Paramet paramet, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Paramet> retorno = new ArrayList();
            sql = "DECLARE @Sequencia AS INT;\n"
                    + " SET @Sequencia = (SELECT (ISNULL(MAX(Sequencia),0) + 1) FROM " + paramet.getBancoDados() + ".dbo.paramet);";

            sql += " INSERT INTO " + paramet.getBancoDados() + ".dbo.paramet (Sequencia,\n"
                    + " CodEmpresa,\n"
                    + " Filial_PDR,\n"
                    + " Nome_empr,\n"
                    + " Cidade_PDR,\n"
                    + " UF_PDR,\n"
                    + " Path,\n"
                    + " Tipo,\n"
                    + " Usuario,\n"
                    + " Senha,\n"
                    + " HostName,\n"
                    + " HostNameWEB,\n"
                    + " BancoDados,\n"
                    + " PathSatelite,\n"
                    + " PathFotos,\n"
                    + " PathDoctos,\n"
                    + " PathLogoNFE,\n"
                    + " EscTolerMot,\n"
                    + " EscTolerChe,\n"
                    + " EscTolerOutros,\n"
                    + " EscTolerVig,\n"
                    + " PtoTolerChe,\n"
                    + " PtoTolerMot,\n"
                    + " PtoTolerOutros,\n"
                    + " PtoTolerVig,\n"
                    + " AceTolerChe,\n"
                    + " AceTolerMot,\n"
                    + " AceTolerOutros,\n"
                    + " AceTolerVig,\n"
                    + " FusoHorario,\n"
                    + " FusoHorarioSEFAZ,\n"
                    + " LimiteSeg, \n"
                    + " LimiteCxf,\n"
                    + " LimiteTes,\n"
                    + " TrocaSenhaMobile,\n"
                    + " Operador,\n"
                    + " Dt_Alter,\n"
                    + " Hr_Alter) VALUES(@Sequencia, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(paramet.getCodEmpresa());
            consult.setString(paramet.getFilial_PDR());
            consult.setString(paramet.getNome_empr());
            consult.setString(paramet.getCidade_PDR());
            consult.setString(paramet.getUF_PDR().toUpperCase());
            consult.setString(paramet.getPath());
            consult.setString("2");
            consult.setString(paramet.getUsuario());
            consult.setString(paramet.getSenha());
            consult.setString(paramet.getHostName());
            consult.setString(paramet.getHostNameWEB());
            consult.setString(paramet.getBancoDados());
            consult.setString(paramet.getPathSatelite());
            consult.setString(paramet.getPathFotos());
            consult.setString(paramet.getPathDoctos());
            consult.setString(paramet.getPathLogoNFE());
            consult.setInt(paramet.getEscTolerMot());
            consult.setInt(paramet.getEscTolerChe());
            consult.setString(paramet.getEscTolerOutros());
            consult.setInt(paramet.getEscTolerVig());
            consult.setString(paramet.getPtoTolerChe());
            consult.setString(paramet.getPtoTolerMot());
            consult.setString(paramet.getPtoTolerOutros());
            consult.setString(paramet.getPtoTolerVig());
            consult.setString(paramet.getAceTolerChe());
            consult.setString(paramet.getAceTolerMot());
            consult.setString(paramet.getAceTolerOutros());
            consult.setString(paramet.getAceTolerVig());
            consult.setString(paramet.getFusoHorario());
            consult.setString(paramet.getFusoHorarioSEFAZ());
            consult.setString(paramet.getLimiteSeg());
            consult.setString(paramet.getLimiteCxf());
            consult.setString(paramet.getLimiteTes());
            consult.setString(paramet.getTrocaSenhaMobile());
            consult.setString(paramet.getOperador());
            consult.setString(paramet.getDt_Alter());
            consult.setString(paramet.getHr_Alter());

            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.criarParametroLocal - \r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param paramet - Dados Paramet
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @return - insere functions em novo banco de dados
     * @throws Exception
     */
    public void criarFunctionsNovoParametro(Paramet paramet, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "CREATE FUNCTION dbo.fun_CalcDistancia\n"
                    + "(\n"
                    + "     @latIni float,\n"
                    + "     @lonIni float,\n"
                    + "     @latFim float,\n"
                    + "     @lonFim float\n"
                    + ") RETURNS float\n"
                    + "AS\n"
                    + " BEGIN\n"
                    + "      DECLARE @Result AS FLOAT\n"
                    + "      DECLARE @arcoA AS FLOAT\n"
                    + "      DECLARE @arcoB AS FLOAT\n"
                    + "      DECLARE @arcoC AS FLOAT\n"
                    + "      DECLARE @auxPI AS FLOAT\n"
                    + "  \n"
                    + "      SET @auxPi = Pi() / 180\n"
                    + "      SET @arcoA = (@lonFim - @lonIni) * @auxPi\n"
                    + "      SET @arcoB = (90 - @latFim) * @auxPi\n"
                    + "      SET @arcoC = (90 - @latIni) * @auxPi\n"
                    + "      SET @Result = Cos(@arcoB) * Cos(@arcoC) + Sin(@arcoB) * Sin(@arcoC) * Cos(@arcoA)\n"
                    + "      SET @Result = (40030 * ((180 / Pi()) * Acos(@Result))) /360\n"
                    + "  \n"
                    + "      RETURN Round(@Result,3)\n"
                    + " END\n";

            Consulta consult = new Consulta(sql, persistencia);
            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.criarFunctionsNovoParametro - \r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param sql - StringBuilder com script de criação do banco de dados
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @throws Exception
     */
    public void criarBancoDadosScript(StringBuilder sql, Paramet param, Persistencia persistencia) throws Exception {
        try {
            String sqlCreate = " CREATE DATABASE [" + param.getBancoDados() + "]\n"
                    + "  CONTAINMENT = NONE\n"
                    + "  ON  PRIMARY \n"
                    + " ( NAME = N'" + param.getBancoDados() + "', FILENAME = N'D:\\BD\\Dados\\" + param.getBancoDados() + ".mdf' , SIZE = 1534976KB , MAXSIZE = UNLIMITED, FILEGROWTH = 1024KB )\n"
                    + "  LOG ON \n"
                    + " ( NAME = N'" + param.getBancoDados() + "_log', FILENAME = N'D:\\BD\\Log\\" + param.getBancoDados() + "_log.ldf' , SIZE = 112384KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)\n"
                    + " ;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET COMPATIBILITY_LEVEL = 120;\n"
                    + " \n"
                    + " IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))\n"
                    + " begin\n"
                    + " EXEC [" + param.getBancoDados() + "].[dbo].[sp_fulltext_database] @action = 'enable'\n"
                    + " end;\n"
                    + " \n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ANSI_NULL_DEFAULT OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ANSI_NULLS OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ANSI_PADDING OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ANSI_WARNINGS OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ARITHABORT OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET AUTO_CLOSE OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET AUTO_SHRINK OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET AUTO_UPDATE_STATISTICS ON;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET CURSOR_CLOSE_ON_COMMIT OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET CURSOR_DEFAULT  GLOBAL;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET CONCAT_NULL_YIELDS_NULL OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET NUMERIC_ROUNDABORT OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET QUOTED_IDENTIFIER OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET RECURSIVE_TRIGGERS OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET  DISABLE_BROKER;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET AUTO_UPDATE_STATISTICS_ASYNC OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET DATE_CORRELATION_OPTIMIZATION OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET TRUSTWORTHY OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET ALLOW_SNAPSHOT_ISOLATION OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET PARAMETERIZATION SIMPLE;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET READ_COMMITTED_SNAPSHOT OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET HONOR_BROKER_PRIORITY OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET RECOVERY FULL;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET  MULTI_USER;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET PAGE_VERIFY CHECKSUM;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET DB_CHAINING OFF;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF );\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET TARGET_RECOVERY_TIME = 0 SECONDS;\n"
                    + " ALTER DATABASE [" + param.getBancoDados() + "] SET DELAYED_DURABILITY = DISABLED;";

            Consulta consult = new Consulta(sqlCreate, persistencia);
            consult.insert();

            consult = new Consulta(sql.toString(), persistencia);
            consult.insert();
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.criarBancoDadosScript - \r\n" + e.getMessage());
        }
    }

    /**
     *
     * @param param - Dados paramet
     * @param persistencia - conexão ao banco - conexão deve sempre ser com o
     * banco central
     * @throws Exception
     */
    public void criarUsuarioBancoDados(Paramet param, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "CREATE LOGIN " + param.getUsuario() + "\n"
                    + " WITH PASSWORD = '" + param.getSenha() + "',\n"
                    + " DEFAULT_DATABASE = " + param.getBancoDados() + ",\n"
                    + " CHECK_POLICY = OFF,\n"
                    + " CHECK_EXPIRATION = OFF ;";

            Consulta consult = new Consulta(sql, persistencia);
            consult.insert();
        } catch (Exception e) {
            throw new Exception("Erro ParametDao.criarUsuarioBancoDados - \r\n" + e.getMessage());
        }
    }
}

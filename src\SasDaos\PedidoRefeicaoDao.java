/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.PedidoRefeicao;
import SasBeansCompostas.PedidoRefeicaoItens;
import Xml.Xmls;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PedidoRefeicaoDao {

    public void inserirPedido(PedidoRefeicao pedido, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        int TotalRefeicoes = 0;
        try {

            sql.append("DECLARE @CodCli INTEGER;\n");
            sql.append(" DECLARE @Nred VARCHAR(255);\n");
            sql.append(" DECLARE @Regiao VARCHAR(255);\n");
            sql.append(" DECLARE @CodigoSequencia INT;\n");

            sql.append(" SET @CodCli = (SELECT \n");
            sql.append("                CxForte.CodCli\n");
            sql.append("                FROM CxForte\n");
            sql.append("                JOIN Clientes\n");
            sql.append("                  ON CxForte.CodCli = Clientes.Codigo\n");
            sql.append("                 AND CxForte.CodFil = Clientes.CodFil);\n");

            sql.append(" SET @Nred = (SELECT \n");
            sql.append("              Clientes.Nred\n");
            sql.append("              FROM CxForte\n");
            sql.append("              JOIN Clientes\n");
            sql.append("                ON CxForte.CodCli = Clientes.Codigo\n");
            sql.append("               AND CxForte.CodFil = Clientes.CodFil);\n");

            sql.append(" SET @Regiao = (SELECT \n");
            sql.append("                Clientes.Regiao\n");
            sql.append("                FROM CxForte\n");
            sql.append("                JOIN Clientes\n");
            sql.append("                  ON CxForte.CodCli = Clientes.Codigo\n");
            sql.append("                 AND CxForte.CodFil = Clientes.CodFil);");

            if (pedido.getSequencia() != BigDecimal.ZERO) {
                sql.append(" UPDATE PedidoRefeicao SET\n");
                sql.append(" Codcli      = ?,\n");
                sql.append(" CodFil      = ?,\n");
                sql.append(" Data        = ?,\n");
                sql.append(" Solicitante = ?,\n");
                sql.append(" Obs         = ?,\n");
                sql.append(" Operador    = ?,\n");
                sql.append(" Dt_Alter    = ?,\n");
                sql.append(" Hr_Alter    = ?\n");
                sql.append(" WHERE Sequencia = ?;\n");

                sql.append(" SET @CodigoSequencia = ?;\n");
            } else {
                sql.append(" INSERT INTO PedidoRefeicao(Sequencia, Codcli, CodFil, Data, Solicitante, Obs, Operador, Dt_Alter, Hr_Alter) VALUES (\n");
                sql.append(" (SELECT (ISNULL(MAX(Sequencia), 0) + 1) FROM PedidoRefeicao),\n");
                sql.append(" ?,?,?,?,?,?,?,?);\n");

                sql.append(" SET @CodigoSequencia = (SELECT ISNULL(MAX(Sequencia),1) FROM PedidoRefeicao);\n");
            }

            sql.append(" DELETE FROM PedidoRefeicaoItens WHERE Sequencia = @CodigoSequencia;\n");

            for (PedidoRefeicaoItens pedidoRefeicaoIten : pedido.getPedidoRefeicaoItens()) {
                sql.append(" INSERT INTO PedidoRefeicaoItens(Sequencia, Ordem, Secao, QtdeCafe, QtdeAlmoco, QtdeJantar, QtdeCeia, Operador, Dt_Alter, Hr_Alter) VALUES (\n");
                sql.append(" @CodigoSequencia,?,?,?,?,?,?,?,?,?);\n");
            }

            // Excluir Pedido se Existir
            sql.append(" DELETE FROM Pedido");
            sql.append(" WHERE CodFil  = ?");
            sql.append(" AND   Data    = ?");
            sql.append(" AND   CodCli2 = ?;");

            // Inserir Pedido
            sql.append(" INSERT INTO Pedido (");
            sql.append(" Numero,\n");
            sql.append(" CodFil,\n");
            sql.append(" Data,\n");
            sql.append(" Tipo,\n");
            sql.append(" CodCli1,\n");
            sql.append(" Nred1,\n");
            sql.append(" Regiao1,\n");
            sql.append(" Hora1O,\n");
            sql.append(" Hora2O,\n");
            sql.append(" CodCli2,\n");
            sql.append(" NRed2,\n");
            sql.append(" Regiao2,\n");
            sql.append(" Hora1D,\n");
            sql.append(" Hora2D,\n");
            sql.append(" Solicitante,\n");
            sql.append(" Valor,\n");
            sql.append(" Obs,\n");
            sql.append(" Classifsrv,\n");
            sql.append(" Operador,\n");
            sql.append(" Dt_Alter,\n");
            sql.append(" Dt_Incl,\n");
            sql.append(" Hr_Alter,\n");
            sql.append(" Hr_Incl,\n");
            sql.append(" OS,\n");
            sql.append(" TicketsQtde,\n");
            sql.append(" Situacao,\n");
            sql.append(" OperIncl,\n");
            sql.append(" TipoMoeda, Flag_Excl) VALUES(");
            sql.append(" (SELECT ISNULL((MAX(numero) + 1),1) FROM Pedido WHERE CodFil = ?),\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" @CodCli,\n");
            sql.append(" @Nred,\n");
            sql.append(" @Regiao,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" (SELECT Nred FROM Clientes WHERE Codigo = ? And CodFil = ?),\n");
            sql.append(" (SELECT Regiao FROM Clientes WHERE Codigo = ? And CodFil = ?),\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" (SELECT TOP 1 OS FROM OS_Vig Where OS_Vig.Cliente = ? AND OS_Vig.CliDst = @CodCli),\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?,\n");
            sql.append(" ?, '');");

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setString(pedido.getCodcli());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getData());
            consulta.setString(pedido.getSolicitante());
            consulta.setString(pedido.getObs());
            consulta.setString(pedido.getOperador());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getHr_Alter());

            if (pedido.getSequencia() != BigDecimal.ZERO) {
                consulta.setBigDecimal(pedido.getSequencia());
                consulta.setBigDecimal(pedido.getSequencia());
            }

            for (PedidoRefeicaoItens pedidoRefeicaoIten : pedido.getPedidoRefeicaoItens()) {
                consulta.setString(pedidoRefeicaoIten.getOrdem());
                consulta.setString(pedidoRefeicaoIten.getSecao());
                consulta.setBigDecimal(pedidoRefeicaoIten.getQtdeCafe());
                consulta.setBigDecimal(pedidoRefeicaoIten.getQtdeAlmoco());
                consulta.setBigDecimal(pedidoRefeicaoIten.getQtdeJantar());
                consulta.setBigDecimal(pedidoRefeicaoIten.getQtdeCeia());
                consulta.setString(pedidoRefeicaoIten.getOperador());
                consulta.setString(pedidoRefeicaoIten.getDt_Alter());
                consulta.setString(pedidoRefeicaoIten.getHr_Alter());

                if (!pedidoRefeicaoIten.getQtdeCafe().isEmpty()) {
                    TotalRefeicoes += Integer.parseInt(pedidoRefeicaoIten.getQtdeCafe());
                }

                if (!pedidoRefeicaoIten.getQtdeAlmoco().isEmpty()) {
                    TotalRefeicoes += Integer.parseInt(pedidoRefeicaoIten.getQtdeAlmoco());
                }

                if (!pedidoRefeicaoIten.getQtdeJantar().isEmpty()) {
                    TotalRefeicoes += Integer.parseInt(pedidoRefeicaoIten.getQtdeJantar());
                }

                if (!pedidoRefeicaoIten.getQtdeCeia().isEmpty()) {
                    TotalRefeicoes += Integer.parseInt(pedidoRefeicaoIten.getQtdeCeia());
                }
            }

            // Excluir Pedido se Existir
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getData());
            consulta.setString(pedido.getCodcli());

            // Inserir Pedido
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setBigDecimal(pedido.getCodFil());
            consulta.setString(pedido.getData());
            consulta.setString("T");
            consulta.setString("07:00");
            consulta.setString("20:00");
            consulta.setString(pedido.getCodcli());
            consulta.setString(pedido.getCodcli());
            consulta.setString(pedido.getCodFil());
            consulta.setString(pedido.getCodcli());
            consulta.setString(pedido.getCodFil());
            consulta.setString("07:00");
            consulta.setString("20:00");
            consulta.setString(pedido.getSolicitante());
            consulta.setFloat(new Float("0"));
            consulta.setString(pedido.getObs());
            consulta.setString("R");
            consulta.setString(pedido.getOperador());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getDt_Alter());
            consulta.setString(pedido.getHr_Alter());
            consulta.setString(pedido.getHr_Alter());
            consulta.setString(pedido.getCodcli());
            consulta.setInt(TotalRefeicoes);
            consulta.setString("PD");
            consulta.setString(pedido.getOperador());
            consulta.setString("BRL");

            consulta.insert();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("PedidoRefeicaoDao.inserirPedido - " + e.getMessage() + "\r\n" + sql.toString());
        }

    }

    public void excluirPedido(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {

            sql.append(" DELETE Pedido\n");
            sql.append(" FROM PedidoRefeicao\n");
            sql.append(" JOIN Pedido \n");
            sql.append("   ON PedidoRefeicao.CodFil = Pedido.CodFil\n");
            sql.append("  AND PedidoRefeicao.Data   = Pedido.Data\n");
            sql.append("  AND PedidoRefeicao.CodCli = Pedido.CodCli2\n");
            sql.append(" WHERE PedidoRefeicao.Sequencia  = ?;");
            sql.append(" DELETE FROM PedidoRefeicaoItens WHERE Sequencia = ?;\n");
            sql.append(" DELETE FROM PedidoRefeicao      WHERE Sequencia = ?;");

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setBigDecimal(sequencia);
            consulta.setBigDecimal(sequencia);
            consulta.setBigDecimal(sequencia);
            consulta.delete();

        } catch (Exception ex) {
            throw new Exception("PedidoRefeicaoDao.excluirPedido - " + ex.getMessage() + "\r\n" + sql.toString());
        }
    }

    public List<PedidoRefeicaoItens> listagemPedidosItens(BigDecimal Sequencia, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        List<PedidoRefeicaoItens> Retorno = new ArrayList<>();

        try {
            sql.append("SELECT PedidoRefeicaoItens.*, PstServ.Local\n");
            sql.append(" FROM PedidoRefeicaoItens\n");
            sql.append(" JOIN PstServ\n");
            sql.append("   ON PedidoRefeicaoItens.Secao = PstServ.Secao\n");
            sql.append(" WHERE Sequencia = ?\n");
            sql.append(" ORDER BY PedidoRefeicaoItens.Ordem");

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setBigDecimal(Sequencia);
            consulta.select();

            PedidoRefeicaoItens pedidoRefeicaoItem;

            while (consulta.Proximo()) {
                pedidoRefeicaoItem = new PedidoRefeicaoItens();

                pedidoRefeicaoItem.setQtdeAlmoco(consulta.getString("QtdeAlmoco"));
                pedidoRefeicaoItem.setQtdeCafe(consulta.getString("QtdeCafe"));
                pedidoRefeicaoItem.setQtdeCeia(consulta.getString("QtdeCeia"));
                pedidoRefeicaoItem.setQtdeJantar(consulta.getString("QtdeJantar"));
                pedidoRefeicaoItem.setSecao(consulta.getString("Secao"));
                pedidoRefeicaoItem.setLocal(consulta.getString("Local"));

                Retorno.add(pedidoRefeicaoItem);
            }

            consulta.close();

            return Retorno;

        } catch (Exception e) {
            throw new Exception("PedidoRefeicaoDao.listagemPedidosItens - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public List<PedidoRefeicao> listagemPedidos(String CodFil, String DataInicio, String DataFim, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            List<PedidoRefeicao> Retorno = new ArrayList<>();

            sql.append(" SELECT\n");
            sql.append(" PedidoRefeicao.Sequencia,\n");
            sql.append(" PedidoRefeicao.Data,\n");
            sql.append(" PedidoRefeicao.Solicitante,\n");
            sql.append(" PedidoRefeicao.Codcli,\n");
            sql.append(" PedidoRefeicao.CodFil,\n");
            sql.append(" Clientes.Nred,\n");
            sql.append(" PedidoRefeicao.Obs,\n");
            sql.append(" SUM(QtdeCafe) QtdeCafe,\n");
            sql.append(" SUM(QtdeAlmoco) QtdeAlmoco,\n");
            sql.append(" SUM(QtdeJantar) QtdeJantar,\n");
            sql.append(" SUM(QtdeCeia) QtdeCeia,\n");
            sql.append(" (SUM(QtdeCafe) + SUM(QtdeAlmoco) + SUM(QtdeJantar) + SUM(QtdeCeia)) AS TotalGeral,\n");
            sql.append(" PedidoRefeicao.Operador,\n");
            sql.append(" PedidoRefeicao.Dt_alter,\n");
            sql.append(" PedidoRefeicao.Hr_Alter,\n");
            sql.append(" Pedido.Situacao\n");
            sql.append(" FROM PedidoRefeicao\n");
            sql.append(" JOIN PedidoRefeicaoItens\n");
            sql.append("   ON PedidoRefeicao.Sequencia = PedidoRefeicaoItens.Sequencia\n");
            sql.append(" JOIN Clientes\n");
            sql.append("   ON PedidoRefeicao.Codcli = Clientes.Codigo\n");
            sql.append(" LEFT JOIN Pedido\n");
            sql.append("   ON PedidoRefeicao.CodFil = Pedido.CodFil\n");
            sql.append("  AND PedidoRefeicao.Data   = Pedido.Data\n");
            sql.append("  AND PedidoRefeicao.CodCli = Pedido.CodCli2\n");
            sql.append(" WHERE PedidoRefeicao.Data BETWEEN ? AND ?\n");
            sql.append(" AND   PedidoRefeicao.Codfil = ?\n");
            sql.append(" GROUP BY PedidoRefeicao.Sequencia,\n");
            sql.append("          PedidoRefeicao.Data,\n");
            sql.append("          PedidoRefeicao.Solicitante,\n");
            sql.append("          PedidoRefeicao.Codcli,\n");
            sql.append("          PedidoRefeicao.CodFil,\n");
            sql.append("          Clientes.Nred,\n");
            sql.append("          PedidoRefeicao.Obs,\n");
            sql.append("          PedidoRefeicao.Operador,\n");
            sql.append("          PedidoRefeicao.Dt_alter,\n");
            sql.append("          PedidoRefeicao.Hr_Alter,\n");
            sql.append("          Pedido.Situacao\n");
            sql.append(" ORDER BY PedidoRefeicao.Data, Clientes.Nred");

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setString(DataInicio);
            consulta.setString(DataFim);
            consulta.setBigDecimal(CodFil);

            consulta.select();

            PedidoRefeicao pedidoRefeicao;

            while (consulta.Proximo()) {
                pedidoRefeicao = new PedidoRefeicao();

                pedidoRefeicao.setSequencia(consulta.getBigDecimal("Sequencia"));
                pedidoRefeicao.setData(consulta.getString("Data"));
                pedidoRefeicao.setSolicitante(consulta.getString("Solicitante"));
                pedidoRefeicao.setCodcli(consulta.getString("Codcli"));
                pedidoRefeicao.setCodFil(consulta.getString("CodFil"));
                pedidoRefeicao.setNred(consulta.getString("Nred"));
                pedidoRefeicao.setObs(consulta.getString("Obs"));
                pedidoRefeicao.setQtdeCafe(consulta.getString("QtdeCafe"));
                pedidoRefeicao.setQtdeAlmoco(consulta.getString("QtdeAlmoco"));
                pedidoRefeicao.setQtdeJantar(consulta.getString("QtdeJantar"));
                pedidoRefeicao.setQtdeCeia(consulta.getString("QtdeCeia"));
                pedidoRefeicao.setTotalGeral(consulta.getString("TotalGeral"));
                pedidoRefeicao.setOperador(consulta.getString("Operador"));
                pedidoRefeicao.setDt_Alter(consulta.getString("Dt_alter"));
                pedidoRefeicao.setHr_Alter(consulta.getString("Hr_Alter"));
                pedidoRefeicao.setSituacao(consulta.getString("Situacao"));

                Retorno.add(pedidoRefeicao);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("PedidoRefeicaoDao.listagemPedidos - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public String listarPedidosRota(String codFil, String matr, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT\n"
                    + "Max(Rt_Perc.Sequencia) SeqRota,\n"
                    + "Max(Clientes.NRed) NRed,\n"
                    + "Rt_Perc.Parada,\n"
                    + "PedidoRefeicao.Sequencia,\n"
                    + "PedidoRefeicao.Data,\n"
                    + "PedidoRefeicao.Solicitante,\n"
                    + "PedidoRefeicao.Codcli,\n"
                    + "PedidoRefeicao.CodFil,\n"
                    + "PedidoRefeicao.Obs,\n"
                    + "SUM(QtdeCafe) QtdeCafe,\n"
                    + "SUM(QtdeAlmoco) QtdeAlmoco,\n"
                    + "SUM(QtdeJantar) QtdeJantar,\n"
                    + "SUM(QtdeCeia) QtdeCeia,\n"
                    + "(SUM(QtdeCafe) + SUM(QtdeAlmoco) + SUM(QtdeJantar) + SUM(QtdeCeia)) AS TotalGeral,\n"
                    + "PedidoRefeicao.Operador,\n"
                    + "PedidoRefeicao.Dt_alter,\n"
                    + "PedidoRefeicao.Hr_Alter,\n"
                    + "Pedido.Situacao, \n"
                    + "Rotas.Rota, \n"
                    + "Substring(Rt_perc.Hora1,1,2)+':'+Substring(Rt_perc.Hora1,3,2) Hora1,\n"
                    + "Rt_Perc.ER, \n"
                    + "Rt_perc.TipoSrv, \n"
                    + "OS_vig.OS\n"
                    + "FROM PedidoRefeicao\n"
                    + "JOIN PedidoRefeicaoItens\n"
                    + "  ON PedidoRefeicao.Sequencia = PedidoRefeicaoItens.Sequencia\n"
                    + "JOIN Clientes\n"
                    + "  ON PedidoRefeicao.Codcli = Clientes.Codigo\n"
                    + "LEFT JOIN Pedido\n"
                    + "  ON PedidoRefeicao.CodFil = Pedido.CodFil\n"
                    + " AND PedidoRefeicao.Data   = Pedido.Data\n"
                    + " AND PedidoRefeicao.CodCli = Pedido.CodCli2\n"
                    + "LEFT JOIN Rotas \n"
                    + "  ON Rotas.Sequencia = Pedido.SeqRota\n"
                    + " AND Rotas.CodFil    = Pedido.CodFil\n"
                    + "LEFT JOIN Rt_perc \n"
                    + "  ON Rt_perc.Sequencia = Rotas.Sequencia\n"
                    + " AND Rt_perc.Parada    = Pedido.Parada\n"
                    + " AND Rt_perc.Flag_Excl <> '*' \n"
                    + "LEFT JOIN Escala \n"
                    + "  ON Escala.SeqRota = Rotas.Sequencia\n"
                    + " AND Escala.Codfil  = Rotas.CodFil\n"
                    + " LEFT JOIN OS_Vig \n"
                    + "  ON OS_Vig.OS     = Pedido.OS\n"
                    + " AND OS_Vig.CodFil = Pedido.CodFil\n"
                    + " WHERE PedidoRefeicao.Data = ? AND Escala.MatrMot = ? AND PedidoRefeicao.CodFil = ?\n"
                    + "GROUP BY Rt_Perc.Parada,\n"
                    + "         PedidoRefeicao.Sequencia,\n"
                    + "         PedidoRefeicao.Data,\n"
                    + "         PedidoRefeicao.Solicitante,\n"
                    + "         PedidoRefeicao.Codcli,\n"
                    + "         PedidoRefeicao.CodFil,\n"
                    + "         PedidoRefeicao.Obs,\n"
                    + "         PedidoRefeicao.Operador,\n"
                    + "         PedidoRefeicao.Dt_alter,\n"
                    + "         PedidoRefeicao.Hr_Alter,\n"
                    + "         Pedido.Situacao,\n"
                    + "         Rotas.Rota, \n"
                    + "         Substring(Rt_perc.Hora1,1,2)+':'+Substring(Rt_perc.Hora1,3,2),\n"
                    + "         Rt_Perc.ER, \n"
                    + "         Rt_perc.TipoSrv, \n"
                    + "         OS_vig.OS        \n"
                    + "ORDER BY Rt_Perc.Parada";

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            consulta.setString(data);
            consulta.setString(matr);
            consulta.setBigDecimal(codFil);

            consulta.select();

            StringBuilder retorno = new StringBuilder();
            while (consulta.Proximo()) {
                retorno.append("<refeicao>");
                retorno.append(Xmls.tag("SeqRota", consulta.getString("SeqRota").replace(".0", "")));
                retorno.append(Xmls.tag("Parada", consulta.getString("Parada")));
                retorno.append(Xmls.tag("NRed", consulta.getString("NRed")));
                retorno.append(Xmls.tag("Sequencia", consulta.getString("Sequencia")));
                retorno.append(Xmls.tag("Data", consulta.getString("Data")));
                retorno.append(Xmls.tag("Solicitante", consulta.getString("Solicitante")));
                retorno.append(Xmls.tag("Codcli", consulta.getString("Codcli")));
                retorno.append(Xmls.tag("CodFil", consulta.getString("CodFil")));
                retorno.append(Xmls.tag("Obs", consulta.getString("Obs")));
                retorno.append(Xmls.tag("QtdeCafe", consulta.getString("QtdeCafe")));
                retorno.append(Xmls.tag("QtdeAlmoco", consulta.getString("QtdeAlmoco")));
                retorno.append(Xmls.tag("QtdeJantar", consulta.getString("QtdeJantar")));
                retorno.append(Xmls.tag("QtdeCeia", consulta.getString("QtdeCeia")));
                retorno.append(Xmls.tag("TotalGeral", consulta.getString("TotalGeral")));
                retorno.append(Xmls.tag("Operador", consulta.getString("Operador")));
                retorno.append(Xmls.tag("Dt_alter", consulta.getString("Dt_alter")));
                retorno.append(Xmls.tag("Hr_Alter", consulta.getString("Hr_Alter")));
                retorno.append(Xmls.tag("Situacao", consulta.getString("Situacao")));
                retorno.append(Xmls.tag("Rota", consulta.getString("Rota")));
                retorno.append(Xmls.tag("Hora1", consulta.getString("Hora1")));
                retorno.append(Xmls.tag("ER", consulta.getString("ER")));
                retorno.append(Xmls.tag("TipoSrv", consulta.getString("TipoSrv")));
                retorno.append(Xmls.tag("OS", consulta.getString("OS")));
                retorno.append("</refeicao>");
            }

            consulta.close();

            return retorno.toString();
        } catch (Exception e) {
            throw new Exception("PedidoRefeicaoDao.listarPedidosRota - " + e.getMessage() + "\r\n"
                    + "SELECT\n"
                    + "Rt_Perc.Parada,\n"
                    + "PedidoRefeicao.Sequencia,\n"
                    + "PedidoRefeicao.Data,\n"
                    + "PedidoRefeicao.Solicitante,\n"
                    + "PedidoRefeicao.Codcli,\n"
                    + "PedidoRefeicao.CodFil,\n"
                    + "PedidoRefeicao.Obs,\n"
                    + "SUM(QtdeCafe) QtdeCafe,\n"
                    + "SUM(QtdeAlmoco) QtdeAlmoco,\n"
                    + "SUM(QtdeJantar) QtdeJantar,\n"
                    + "SUM(QtdeCeia) QtdeCeia,\n"
                    + "(SUM(QtdeCafe) + SUM(QtdeAlmoco) + SUM(QtdeJantar) + SUM(QtdeCeia)) AS TotalGeral,\n"
                    + "PedidoRefeicao.Operador,\n"
                    + "PedidoRefeicao.Dt_alter,\n"
                    + "PedidoRefeicao.Hr_Alter,\n"
                    + "Pedido.Situacao, \n"
                    + "Rotas.Rota, \n"
                    + "Substring(Rt_perc.Hora1,1,2)+':'+Substring(Rt_perc.Hora1,3,2) Hora1,\n"
                    + "Rt_Perc.ER, \n"
                    + "Rt_perc.TipoSrv, \n"
                    + "OS_vig.OS\n"
                    + "FROM PedidoRefeicao\n"
                    + "JOIN PedidoRefeicaoItens\n"
                    + "  ON PedidoRefeicao.Sequencia = PedidoRefeicaoItens.Sequencia\n"
                    + "LEFT JOIN Pedido\n"
                    + "  ON PedidoRefeicao.CodFil = Pedido.CodFil\n"
                    + " AND PedidoRefeicao.Data   = Pedido.Data\n"
                    + " AND PedidoRefeicao.CodCli = Pedido.CodCli2\n"
                    + "LEFT JOIN Rotas \n"
                    + "  ON Rotas.Sequencia = Pedido.SeqRota\n"
                    + " AND Rotas.CodFil    = Pedido.CodFil\n"
                    + "LEFT JOIN Rt_perc \n"
                    + "  ON Rt_perc.Sequencia = Rotas.Sequencia\n"
                    + " AND Rt_perc.Parada    = Pedido.Parada\n"
                    + " AND Rt_perc.Flag_Excl <> '*' \n"
                    + "LEFT JOIN Escala \n"
                    + "  ON Escala.SeqRota = Rotas.Sequencia\n"
                    + " AND Escala.Codfil  = Rotas.CodFil               \n"
                    + " LEFT JOIN OS_Vig \n"
                    + "  ON OS_Vig.OS     = Pedido.OS\n"
                    + " AND OS_Vig.CodFil = Pedido.CodFil\n"
                    + " WHERE PedidoRefeicao.Data = " + data + " AND Escala.MatrMot = " + matr + " AND PedidoRefeicao.CodFil = " + codFil + "\n"
                    + "GROUP BY Rt_Perc.Parada,\n"
                    + "         PedidoRefeicao.Sequencia,\n"
                    + "         PedidoRefeicao.Data,\n"
                    + "         PedidoRefeicao.Solicitante,\n"
                    + "         PedidoRefeicao.Codcli,\n"
                    + "         PedidoRefeicao.CodFil,\n"
                    + "         PedidoRefeicao.Obs,\n"
                    + "         PedidoRefeicao.Operador,\n"
                    + "         PedidoRefeicao.Dt_alter,\n"
                    + "         PedidoRefeicao.Hr_Alter,\n"
                    + "         Pedido.Situacao,\n"
                    + "         Rotas.Rota, \n"
                    + "         Substring(Rt_perc.Hora1,1,2)+':'+Substring(Rt_perc.Hora1,3,2),\n"
                    + "         Rt_Perc.ER, \n"
                    + "         Rt_perc.TipoSrv, \n"
                    + "         OS_vig.OS        \n"
                    + "ORDER BY Rt_Perc.Parada");
        }
    }
}

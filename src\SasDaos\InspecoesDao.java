/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Inspecoes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class InspecoesDao {

    /**
     * Busca inspeção por código
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Inspecoes getInspecoes(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Inspecoes WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            Inspecoes inspecoes = new Inspecoes();
            while (consulta.Proximo()) {
                inspecoes.setCodigo(consulta.getString("codigo"));
                inspecoes.setDescricao(consulta.getString("descricao"));
                inspecoes.setDt_Alter(consulta.getString("dt_alter"));
                inspecoes.setHr_Alter(consulta.getString("hr_alter"));
                inspecoes.setOperador(consulta.getString("operador"));
            }
            consulta.Close();
            return inspecoes;
        } catch (Exception e) {
            throw new Exception("InspecoesDao.getAllInspecoes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Inspecoes WHERE Codigo = " + codigo);
        }
    }

    /**
     * Lista todas as inspeções cadastradas
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Inspecoes> getAllInspecoes(Persistencia persistencia) throws Exception {
        try {
            List<Inspecoes> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Inspecoes ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Inspecoes inspecoes;
            while (consulta.Proximo()) {
                inspecoes = new Inspecoes();
                inspecoes.setCodigo(consulta.getString("codigo"));
                inspecoes.setDescricao(consulta.getString("descricao"));
                inspecoes.setDt_Alter(consulta.getString("dt_alter"));
                inspecoes.setHr_Alter(consulta.getString("hr_alter"));
                inspecoes.setOperador(consulta.getString("operador"));
                retorno.add(inspecoes);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("InspecoesDao.getAllInspecoes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Inspecoes ");
        }
    }

    /**
     * Insere uma nova inspeção no banco
     *
     * @param inspecao
     * @param persistencia
     * @throws Exception
     */
    public void putInspecao(Inspecoes inspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Inspecoes (Codigo, Descricao, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES ((SELECT ISNULL(MAX(Codigo),0) + 1 Codigo FROM Inspecoes), ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecao.getDescricao());
            consulta.setString(inspecao.getOperador());
            consulta.setString(inspecao.getDt_Alter());
            consulta.setString(inspecao.getHr_Alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.putInspecao - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Inspecoes (Codigo, Descricao, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES (SELECT ISNULL(MAX(Codigo),0) + 1 Codigo FROM Inspecoes, " + inspecao.getDescricao() + ", "
                    + inspecao.getOperador() + ", " + inspecao.getDt_Alter() + ", " + inspecao.getHr_Alter() + ")");
        }
    }

    /**
     * Apaga uma inspeção
     *
     * @param inspecao
     * @param persistencia
     * @throws Exception
     */
    public void deleteInspecao(Inspecoes inspecao, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM Inspecoes "
                    + " WHERE Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(inspecao.getCodigo());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("InspecoesDao.putInspecao - " + e.getMessage() + "\r\n"
                    + " DELETE FROM Inspecoes "
                    + " WHERE Codigo = " + inspecao.getCodigo());
        }
    }
    
    /**
     * Lista todas as inspeções cadastradas
     *
     * @param Secao
     * @param CodFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Inspecoes> getAllInspecoesPst(String Secao, String CodFil,  Persistencia persistencia) throws Exception {
        try {
            List<Inspecoes> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Inspecoes "+
                         " where Secao = '"+Secao+"'"+
                         "   and codFil = "+CodFil;                    
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Inspecoes inspecoes;
            while (consulta.Proximo()) {
                inspecoes = new Inspecoes();
                inspecoes.setCodigo(consulta.getString("codigo"));
                inspecoes.setDescricao(consulta.getString("descricao"));
                inspecoes.setDt_Alter(consulta.getString("dt_alter"));
                inspecoes.setHr_Alter(consulta.getString("hr_alter"));
                inspecoes.setOperador(consulta.getString("operador"));
                retorno.add(inspecoes);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("InspecoesDao.getAllInspecoes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Inspecoes ");
        }
    }
}

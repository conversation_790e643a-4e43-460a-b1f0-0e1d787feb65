/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.InterfaceCI;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class InterfaceCIDao {

    /**
     * Insere uma nova entrada na tabela InterfaceCI
     *
     * @param dados - objeto interface ci
     * @param pst - persistencia
     * @throws Exception
     */
    public void Gravar(InterfaceCI dados, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into interfaceci "
                    + " (Sequencia,Data,Hora,Mensagem,Processado)"
                    + " values (?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(dados.getSequencia());
            consulta.setString(dados.getData());
            consulta.setString(dados.getHora());
            consulta.setString(dados.getMensagem());
            consulta.setString(dados.getProcessado());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir dados InterfaceCI\r\n" + e.getMessage());
        }
    }

    /**
     * Busca o último número de sequencia gravado
     *
     * @param persistencia - conexão ao banco dedados
     * @return
     * @throws Exception
     */
    public BigDecimal maxSequencia(Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("0");
            String sql = "Select MAX(Sequencia) sequencia from interfaceci";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = new BigDecimal(consult.getString("sequencia"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar máximo da sequencia InterfaceCI\r\n" + e.getMessage());
        }
    }
}

/*
 *
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.OS_VITens;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OS_VItensDao {

    /**
     * Insere registros em OS_Vitens
     *
     * @param os_vitens objeto contendo item da OS
     * @param persistencia conexao com o banco de dados
     * @throws Exception
     */
    public void Inserir(OS_VITens os_vitens, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into OS_Vitens (OS, CodFil, TipoPosto, DtInicio, DtFim, Qtde, Obs, "
                    + "<PERSON>g<PERSON>x<PERSON><PERSON>, Valor, Operador, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON>_<PERSON>, CHSeman, CHMensal, Salario) Values("
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?); ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os_vitens.getOS());
            consulta.setString(os_vitens.getCodFil());
            consulta.setString(os_vitens.getTipoPosto());
            consulta.setString(os_vitens.getDtInicio());
            consulta.setString(os_vitens.getDtFim());
            consulta.setString(os_vitens.getQtde());
            consulta.setString(os_vitens.getObs());
            consulta.setString(os_vitens.getMsgExtrato());
            consulta.setString(os_vitens.getValor());
            consulta.setString(os_vitens.getOperador());
            consulta.setString(os_vitens.getDt_Alter());
            consulta.setString(os_vitens.getHr_Alter());
            consulta.setString(os_vitens.getCHSeman());
            consulta.setString(os_vitens.getCHMensal());
            consulta.setString(os_vitens.getSalario());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VItensDao.Inserir  - " + e.getMessage() + "\r\n");
        }
    }

    public void Excluir(OS_VITens os_vitens, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DELETE FROM OS_Vitens WHERE OS = ? AND CodFil = ? AND TipoPosto = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os_vitens.getOS());
            consulta.setString(os_vitens.getCodFil());
            consulta.setString(os_vitens.getTipoPosto());

            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OS_VItensDao.Excluir  - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void Atualizar(OS_VITens os_vitens, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update OS_Vitens set "
                    + " CodFil = ?, "
                    + " TipoPosto = ?, "
                    + " DtInicio = ?, "
                    + " DtFim = ?, "
                    + " Qtde = ?, "
                    + " Obs = ?, "
                    + " MsgExtrato = ?, "
                    + " Valor = ?, "
                    + " Operador = ?, "
                    + " Dt_Alter = ?, "
                    + " Hr_Alter = ?, "
                    + " CHSeman = ?, "
                    + " CHMensal = ?, "
                    + " Salario = ? "
                    + " Where OS = ? "
                    + "   and CodFil = ? "
                    + "   and TipoPosto = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(os_vitens.getCodFil());
            consulta.setString(os_vitens.getTipoPosto());
            consulta.setString(os_vitens.getDtInicio());
            consulta.setString(os_vitens.getDtFim());
            consulta.setString(os_vitens.getQtde());
            consulta.setString(os_vitens.getObs());
            consulta.setString(os_vitens.getMsgExtrato());
            consulta.setString(os_vitens.getValor());
            consulta.setString(os_vitens.getOperador());
            consulta.setString(os_vitens.getDt_Alter());
            consulta.setString(os_vitens.getHr_Alter());
            consulta.setString(os_vitens.getCHSeman());
            consulta.setString(os_vitens.getCHMensal());
            consulta.setString(os_vitens.getSalario());

            consulta.setString(os_vitens.getOS());
            consulta.setString(os_vitens.getCodFil());
            consulta.setString(os_vitens.getTipoPosto());

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("OS_VItensDao.Atualizar  - " + e.getMessage() + "\r\n");
        }

    }

    public List<OS_VITens> listarOS_VItens(String CodFil, String OS, Persistencia persistencia) throws Exception {
        try {
            List<OS_VITens> retorno = new ArrayList<>();
            String sql = "select OS_VItens.*, CtrItens.Descricao TipoPostoDesc, CtrItens.TipoCalc\n"
                    + "from OS_VItens \n"
                    + " left Join OS_Vig on  OS_Vig.OS     = OS_VItens.OS    \n"
                    + "                  and OS_Vig.CodFil = OS_VItens.CodFil \n"
                    + " left Join CtrItens on  CtrItens.Contrato  = OS_Vig.Contrato   \n"
                    + "                   and CtrItens.CodFil    = OS_VItens.CodFil   \n"
                    + "                and CtrItens.TipoPosto = OS_VItens.TipoPosto\n"
                    + "where OS_VItens.codfil = ?  and OS_VItens.os = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString(OS);
            consult.select();
            OS_VITens os_vitens;
            while (consult.Proximo()) {
                os_vitens = new OS_VITens();
                os_vitens.setOS(consult.getString("OS"));
                os_vitens.setCodFil(consult.getString("CodFil"));
                os_vitens.setTipoPosto(consult.getString("TipoPosto"));
                os_vitens.setDtInicio(consult.getString("DtInicio"));
                os_vitens.setDtFim(consult.getString("DtFim"));
                os_vitens.setQtde(consult.getString("Qtde"));
                os_vitens.setObs(consult.getString("Obs"));
                os_vitens.setMsgExtrato(consult.getString("MsgExtrato"));
                os_vitens.setValor(consult.getString("Valor"));
                os_vitens.setOperador(consult.getString("Operador"));
                os_vitens.setDt_Alter(consult.getString("Dt_Alter"));
                os_vitens.setHr_Alter(consult.getString("Hr_Alter"));
                os_vitens.setCHSeman(consult.getString("CHSeman"));
                os_vitens.setCHMensal(consult.getString("CHMensal"));
                os_vitens.setSalario(consult.getString("Salario"));

                os_vitens.setTipoPostoDesc(consult.getString("TipoPostoDesc"));
                os_vitens.setTipoCalc(consult.getString("TipoCalc"));
                retorno.add(os_vitens);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("OS_VItensDao.Listar  - " + e.getMessage() + "\r\n"
                    + "select * from OS_VItens "
                    + " where codfil = " + CodFil
                    + " and os = " + OS);
        }
    }
}

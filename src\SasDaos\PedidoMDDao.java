/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PedidoMD;

/**
 *
 * <AUTHOR>
 */
public class PedidoMDDao {

    public void deletarPedidoMD(PedidoMD pedidoMD, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM PedidoMD WHERE  Numero = ? AND CodFil = ? AND Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoMD.getNumero());
            consulta.setString(pedidoMD.getCodFil());
            consulta.setString(pedidoMD.getCodigo());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PedidoMDDao.deletarPedidoMD - " + e.getMessage() + "\r\n"
                    + " DELETE FROM PedidoMD WHERE  Numero = " + pedidoMD.getNumero() + " AND CodFil = " + pedidoMD.getCodFil()
                    + " AND Codigo = " + pedidoMD.getCodigo());
        }
    }

    /**
     * Insere uma nova entrada na tabela PedidoMD
     *
     * @param pedidoMD
     * @param persistencia
     * @throws Exception
     */
    public void inserirPedidoMD(PedidoMD pedidoMD, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO PedidoMD values (?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoMD.getNumero());
            consulta.setString(pedidoMD.getCodFil());
            consulta.setString(pedidoMD.getCodigo());
            consulta.setString(pedidoMD.getDocto());
            consulta.setString(pedidoMD.getQtde());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PedidoMDDao.inserirPedidoMD - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PedidoMD values (" + pedidoMD.getNumero() + ", " + pedidoMD.getCodFil() + ", " + pedidoMD.getCodigo() + ","
                    + pedidoMD.getDocto() + ", " + pedidoMD.getQtde() + ") ");
        }
    }

}

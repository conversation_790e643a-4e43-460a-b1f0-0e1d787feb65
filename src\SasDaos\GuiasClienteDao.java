package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.GuiasCliente;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class GuiasClienteDao {

    /**
     * Recupera registros do banco de dados: se codcli = -1, então recupera os
     * registros de todos os clientes
     *
     * @param codPessoa codigo da pessoa
     * @param codFil codigo da filial
     * @param codCli codigo do cliente
     * @param data1 periodo inicial
     * @param data2 periodo final
     * @param persistencia conexão com o banco de dados
     * @return lista de guias do banco de dados
     * @throws Exception
     */
    public List<GuiasCliente> listarGuias(BigDecimal codPessoa, String codFil, String codCli, String data1, String data2,
            Persistencia persistencia) throws Exception {
        List<GuiasCliente> guias = new ArrayList<>();
        try {
            String sql = "";
            DecimalFormat df = new DecimalFormat("#");
            df.setMaximumFractionDigits(0);
            if (data1.equals("") || data2.equals("")) {
                sql = sql + "Select top 20 ";
            } else {
                sql = sql + "Select ";
            }
            sql = sql + "Rotas.Rota, case when pessoaaut.situacao = 'F' then cast(pessoaaut.matr as varchar)+': '+pessoaaut.nome"
                    + " else pessoaaut.nome end 'codpessoaaut', OS_VIG.OS, OS_VIG.CliFat, Escala.Veiculo, "
                    + "Rt_Perc.Observ, Rotas.CodFil, Rotas.Data, Rt_Perc.Hora1, "
                    + "Case when DatePart(w, Rotas.Data) = 1 then 'Domingo' "
                    + "     when DatePart(w, Rotas.Data) = 2 then 'Segunda' "
                    + "     when DatePart(w, Rotas.Data) = 3 then 'Terça' "
                    + "     when DatePart(w, Rotas.Data) = 4 then 'Quarta' "
                    + "     when DatePart(w, Rotas.Data) = 5 then 'Quinta' "
                    + "     when DatePart(w, Rotas.Data) = 6 then 'Sexta' "
                    + "     when DatePart(w, Rotas.Data) = 7 then 'Sabado' "
                    + "else '' "
                    + "end Dia, Rt_Perc.HrCheg 'HoraChegada', Rt_Perc.HrSaida 'HoraSaida', Rt_Perc.TempoEspera 'tempoespera',"
                    + " Rt_guias.Guia, Rt_Guias.Serie, "
                    + "  Case "
                    + "  when Rt_Perc.TipoSrv = 'A' then 'AssistenciaTecnica' "
                    + "  when Rt_Perc.ER = 'E' then 'Reforço' "
                    + "  when Rt_Perc.ER =  'R' then 'Alívio' else '' end 'operacao', Rt_Perc.NRed, Rt_Guias.Valor, "
                    + "  (Select Count(*) from CXFGuiasVol where CXFGuiasVol.Guia = Rt_guias.Guia "
                    + " and CXFGuiasVol.Serie = Rt_Guias.Serie) 'Volumes', "
                    + " Case when RPV.CodPessoaAut = PessoaChe.Codigo then 'Chefe equipe: '+PessoaChe.Nome "
                    + "      when Pessoa.Nome is not null then Pessoa.Nome "
                    + "   else (Select 'Chefe equipe: '+min(Pessoa.Nome) from Pessoa "
                    + "left join Escala on Escala.SeqRota = Rotas.Sequencia "
                    + " where Pessoa.Matr = Escala.MatrChe) "
                    + "end 'assinado', cliori.nred origem, cliori.codigo codorigem, clidst.codigo coddst,"
                    + " clidst.nred destino from RPV "
                    + "left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota "
                    + "                  and Rt_Perc.Parada    = RPV.Parada "
                    + "left join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia "
                    + "                   and Rt_Guias.Serie   = RPV.Serie "
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut "
                    + "left join OS_Vig on OS_VIg.OS = Rt_Guias.OS "
                    + "                and OS_Vig.codfil = Rotas.codfil "
                    + "left join clientes cliori on cliori.codigo = os_vig.cliente "
                    + "                         and cliori.codfil = os_vig.codfil "
                    + "left join clientes clidst on clidst.codigo = os_vig.clidst "
                    + "                         and clidst.codfil = os_vig.codfil "
                    + "left join escala on escala.seqrota = rotas.sequencia "
                    + "left join pessoa pessoache on pessoache.matr = escala.matrche "
                    + "left join pessoa pessoaaut on pessoaaut.codigo = rpv.codpessoaaut "
                    + "where Rt_Perc.Codcli1 = ?"
                    + "  and Rotas.CodFil = ? "
                    + "  and rpv.flag_excl != '*'";
            if (!data1.equals("") || !data2.equals("")) {
                sql = sql + " and rotas.data between ? and ? ";
            }
            sql = sql + " order by Data desc, HrCheg desc, Guia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            if (codCli.equals("-1")) {
                consulta.setBigDecimal(codPessoa);
            } else {
                consulta.setString(codCli);
                consulta.setString(codFil);
            }
            if (!data1.equals("") || !data2.equals("")) {
                consulta.setString(data1);
                consulta.setString(data2);
            }
            consulta.select();

            GuiasCliente guia;
            while (consulta.Proximo()) {
                guia = new GuiasCliente();
                guia.setCodPessoaAut(consulta.getString("codpessoaaut"));
                guia.setRota(consulta.getString("rota"));
                guia.setHora1(consulta.getString("hora1"));
                guia.setVeiculo(consulta.getBigDecimal("veiculo"));
                guia.setOS(consulta.getString("OS"));
                guia.setCodfil(consulta.getString("CodFil"));
                guia.setCodfat(consulta.getString("CliFat"));
                guia.setData(consulta.getString("Data"));
                guia.setDia(consulta.getString("Dia"));
                guia.setHoraChegada(consulta.getString("HoraChegada"));
                guia.setHoraSaida(consulta.getString("HoraSaida"));
                guia.setTempo(consulta.getString("tempoespera"));
                try {
                    guia.setGuia(df.format(Float.parseFloat(consulta.getString("Guia"))));
                } catch (Exception e33) {
                    guia.setGuia(consulta.getString("Guia"));
                }
                guia.setSerie(consulta.getString("Serie"));
                guia.setOperacao(consulta.getString("operacao"));
                guia.setNred(consulta.getString("Nred"));
                guia.setValor(consulta.getString("Valor"));
                guia.setVolumes(consulta.getString("Volumes"));
                guia.setAssinado(consulta.getString("Assinado"));
                guia.setOrigem(consulta.getString("origem"));
                guia.setCodorigem(consulta.getString("codorigem"));
                guia.setCoddst(consulta.getString("coddst"));
                guia.setDestino(consulta.getString("destino"));
                guia.setObserv(consulta.getString("observ"));
                guias.add(guia);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return guias;
    }

    public Integer totalGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select count(*) total from RPV "
                    + "left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota "
                    + "                  and Rt_Perc.Parada    = RPV.Parada "
                    + "left join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia "
                    + "                   and Rt_Guias.Serie   = RPV.Serie "
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut "
                    + "left join OS_Vig on OS_VIg.OS = Rt_Guias.OS "
                    + "                and OS_Vig.codfil = Rotas.codfil "
                    + "left join clientes cliori on cliori.codigo = os_vig.cliente "
                    + "                         and cliori.codfil = os_vig.codfil "
                    + "left join clientes clidst on clidst.codigo = os_vig.clidst "
                    + "                         and clidst.codfil = os_vig.codfil "
                    + "left join escala on escala.seqrota = rotas.sequencia "
                    + "left join pessoa pessoache on pessoache.matr = escala.matrche "
                    + "left join pessoa pessoaaut on pessoaaut.codigo = rpv.codpessoaaut "
                    + "where ";

            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "rpv.flag_excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar guias -  \r\n" + e.getMessage());
        }
    }

    public BigDecimal valorGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select sum( Rt_Guias.Valor) valor from RPV "
                    + "left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota "
                    + "                  and Rt_Perc.Parada    = RPV.Parada "
                    + "left join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia "
                    + "                   and Rt_Guias.Serie   = RPV.Serie "
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut "
                    + "left join OS_Vig on OS_VIg.OS = Rt_Guias.OS "
                    + "                and OS_Vig.codfil = Rotas.codfil "
                    + "left join clientes cliori on cliori.codigo = os_vig.cliente "
                    + "                         and cliori.codfil = os_vig.codfil "
                    + "left join clientes clidst on clidst.codigo = os_vig.clidst "
                    + "                         and clidst.codfil = os_vig.codfil "
                    + "left join escala on escala.seqrota = rotas.sequencia "
                    + "left join pessoa pessoache on pessoache.matr = escala.matrche "
                    + "left join pessoa pessoaaut on pessoaaut.codigo = rpv.codpessoaaut "
                    + "where ";

            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "rpv.flag_excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            BigDecimal retorno = BigDecimal.ZERO;
            while (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("valor");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar valor das guias -  \r\n" + e.getMessage());
        }
    }

    public int volumesGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select  (Select Count(*) from CXFGuiasVol where CXFGuiasVol.Guia = Rt_guias.Guia "
                    + "and CXFGuiasVol.Serie = Rt_Guias.Serie) as volume from RPV "
                    + "left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota "
                    + "                  and Rt_Perc.Parada    = RPV.Parada "
                    + "left join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia "
                    + "                   and Rt_Guias.Serie   = RPV.Serie "
                    + "left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut "
                    + "left join OS_Vig on OS_VIg.OS = Rt_Guias.OS "
                    + "                and OS_Vig.codfil = Rotas.codfil "
                    + "left join clientes cliori on cliori.codigo = os_vig.cliente "
                    + "                         and cliori.codfil = os_vig.codfil "
                    + "left join clientes clidst on clidst.codigo = os_vig.clidst "
                    + "                         and clidst.codfil = os_vig.codfil "
                    + "left join escala on escala.seqrota = rotas.sequencia "
                    + "left join pessoa pessoache on pessoache.matr = escala.matrche "
                    + "left join pessoa pessoaaut on pessoaaut.codigo = rpv.codpessoaaut "
                    + "where ";

            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "rpv.flag_excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = retorno + consulta.getInt("volume");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar volumes das guias -  \r\n" + e.getMessage());
        }
    }

    public List<GuiasCliente> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<GuiasCliente> guias = new ArrayList();
        DecimalFormat df = new DecimalFormat("#");
        df.setMaximumFractionDigits(0);
        try {
            String sql = "SELECT  *  FROM"
                    + " ( SELECT    ROW_NUMBER() OVER ( ORDER BY  Rotas.Data desc, Rt_Perc.HrCheg desc, Rt_guias.Guia desc  ) AS RowNum,"
                    + "        Rotas.Rota, Rt_Perc.sequencia, Rt_Perc.parada,"
                    + " case when pessoaaut.situacao = 'F' then cast(pessoaaut.matr as varchar)+': '+pessoaaut.nome"
                    + " else pessoaaut.nome end 'codpessoaaut', OS_VIG.OS, OS_VIG.CliFat, Escala.Veiculo, "
                    + " Rt_Perc.Observ, Rotas.CodFil, Rotas.Data, Rt_Perc.Hora1, "
                    + " Case when DatePart(w, Rotas.Data) = 1 then 'Domingo' "
                    + "     when DatePart(w, Rotas.Data) = 2 then 'Segunda' "
                    + "     when DatePart(w, Rotas.Data) = 3 then 'Terça' "
                    + "     when DatePart(w, Rotas.Data) = 4 then 'Quarta' "
                    + "     when DatePart(w, Rotas.Data) = 5 then 'Quinta' "
                    + "     when DatePart(w, Rotas.Data) = 6 then 'Sexta' "
                    + "     when DatePart(w, Rotas.Data) = 7 then 'Sabado' "
                    + " else '' "
                    + " end Dia, Rt_Perc.HrCheg 'HoraChegada', Rt_Perc.HrSaida 'HoraSaida', Rt_Perc.TempoEspera 'tempoespera',"
                    + " Rt_guias.Guia, Rt_Guias.Serie, "
                    + "  Case "
                    + "  when Rt_Perc.TipoSrv = 'A' then 'AssistenciaTecnica' "
                    + "  when Rt_Perc.ER = 'E' then 'Reforço' "
                    + "  when Rt_Perc.ER =  'R' then 'Alívio' else '' end 'operacao', Rt_Perc.NRed, Rt_Guias.Valor, "
                    + "  (Select Count(*) from CXFGuiasVol where CXFGuiasVol.Guia = Rt_guias.Guia "
                    + " and CXFGuiasVol.Serie = Rt_Guias.Serie) 'Volumes', "
                    + " Case when RPV.CodPessoaAut = PessoaChe.Codigo then 'Chefe equipe: '+PessoaChe.Nome "
                    + "      when Pessoa.Nome is not null then Pessoa.Nome "
                    + "   else (Select 'Chefe equipe: '+min(Pessoa.Nome) from Pessoa "
                    + " left join Escala on Escala.SeqRota = Rotas.Sequencia "
                    + " where Pessoa.Matr = Escala.MatrChe) "
                    + " end 'assinado', Case when Rt_Perc.CodCli1 = CliOri.Codigo then cliori.nred "
                    + " else clidst.NRed end origem, "
                    + " Case when Rt_Perc.CodCli1 = CliOri.Codigo then cliori.codigo "
                    + " else clidst.Codigo end codorigem, "
                    + " Case when Rt_Perc.CodCli1 <> CliOri.Codigo then cliori.nred "
                    + " else clidst.nred end destino, "
                    + " Case when Rt_Perc.CodCli1 <> CliOri.Codigo then cliori.codigo "
                    + " else clidst.codigo end coddst from RPV "
                    + " left join Rt_Perc on  Rt_perc.Sequencia = RPV.SeqRota "
                    + "                  and Rt_Perc.Parada    = RPV.Parada "
                    + " inner join Rt_Guias  on Rt_Guias.Guia    = RPV.Guia "
                    + "                   and Rt_Guias.Serie   = RPV.Serie "
                    + " left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + " left join Pessoa on Pessoa.Codigo = RPV.CodPessoaAut "
                    + " left join OS_Vig on OS_VIg.OS = Rt_Guias.OS "
                    + "                and OS_Vig.codfil = Rotas.codfil "
                    + " left join clientes cliori on cliori.codigo = os_vig.cliente "
                    + "                         and cliori.codfil = os_vig.codfil "
                    + " left join clientes clidst on clidst.codigo = os_vig.clidst "
                    + "                         and clidst.codfil = os_vig.codfil "
                    + " left join escala on escala.seqrota = rotas.sequencia "
                    + " left join pessoa pessoache on pessoache.matr = escala.matrche "
                    + " left join pessoa pessoaaut on pessoaaut.codigo = rpv.codpessoaaut "
                    + " where ";
            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "rpv.flag_excl != '*') AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            GuiasCliente guia;
            while (consulta.Proximo()) {
                guia = new GuiasCliente();
                guia.setSequencia(consulta.getString("sequencia"));
                guia.setParada(consulta.getString("parada"));
                guia.setCodPessoaAut(consulta.getString("codpessoaaut"));
                guia.setRota(consulta.getString("rota"));
                guia.setHora1(consulta.getString("hora1"));
                guia.setVeiculo(consulta.getBigDecimal("veiculo"));
                guia.setOS(consulta.getString("OS"));
                guia.setCodfil(consulta.getString("CodFil"));
                guia.setCodfat(consulta.getString("CliFat"));
                guia.setData(consulta.getString("Data"));
                guia.setDia(consulta.getString("Dia"));
                guia.setHoraChegada(consulta.getString("HoraChegada"));
                guia.setHoraSaida(consulta.getString("HoraSaida"));
                guia.setTempo(consulta.getString("tempoespera"));
                try {
                    guia.setGuia(df.format(Float.parseFloat(consulta.getString("Guia"))));
                } catch (Exception e33) {
                    guia.setGuia(consulta.getString("Guia"));
                }
                guia.setSerie(consulta.getString("Serie"));
                guia.setOperacao(consulta.getString("operacao"));
                guia.setNred(consulta.getString("Nred"));
                guia.setValor(consulta.getString("Valor"));
                guia.setVolumes(consulta.getString("Volumes"));
                guia.setAssinado(consulta.getString("Assinado"));
                guia.setOrigem(consulta.getString("origem"));
                guia.setCodorigem(consulta.getString("codorigem"));
                guia.setCoddst(consulta.getString("coddst"));
                guia.setDestino(consulta.getString("destino"));
                guia.setObserv(consulta.getString("observ"));
                guias.add(guia);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de guias - \r\n" + e.getMessage());
        }
        return guias;
    }
}

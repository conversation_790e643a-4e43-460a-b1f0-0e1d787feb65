package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaQst;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class PessoaQstDao {

    // create
    public boolean gravaPessoaQst(PessoaQst pessoaQst, Persistencia persistencia) throws Exception {
        boolean retorno = false;
        String sql = "insert into PessoaQst (CodPessoa,CodQuestao,OrdQuestao,RespostaDet,Data,Hora) "
                + "values (?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaQst.getCodPessoa());
            consulta.setInt(pessoaQst.getCodQuestao());
            consulta.setInt(pessoaQst.getOrdQuestao());
            // consulta.setInt(pessoaQst.getResposta());
            consulta.setString(pessoaQst.getRespostaDet());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("Falha ao gravar resposta de questionario - " + e.getMessage());
        }
        return retorno;
    }

    // read
    public List<PessoaQst> buscaPessoaQst(Persistencia persistencia) throws Exception {
        List<PessoaQst> listPessoaQst;
        try {
            PessoaQst pessoaQst;
            Consulta consult = new Consulta("select CodPessoa,CodQuestao,OrdQuestao,Resposta,RespostaDet,Data,Hora "
                    + "from pessoaQst", persistencia);
            consult.select();
            listPessoaQst = new ArrayList();
            while (consult.Proximo()) {
                pessoaQst = new PessoaQst();
                pessoaQst.setCodPessoa(consult.getString("CodPessoa"));
                pessoaQst.setCodQuestao(consult.getInt("CodQuestao"));
                pessoaQst.setOrdQuestao(consult.getInt("OrdQuestao"));
                pessoaQst.setResposta(consult.getInt("Resposta"));
                pessoaQst.setRespostaDet(consult.getString("RespostaDet"));
                pessoaQst.setData(consult.getDate("Data").toLocalDate());
                pessoaQst.setHora(consult.getString("Hora"));
                listPessoaQst.add(pessoaQst);
            }
            consult.Close();
        } catch (Exception e) {
            listPessoaQst = null;
            throw new Exception("Falha ao buscar..." + e.getMessage());
        }
        return listPessoaQst;
    }

    // update
    public void atualizaPessoaQst(PessoaQst pessoaQst, Persistencia persistencia) throws Exception {
        String sql = "update PessoaQst set CodPessoa=?, CodQuestao=?, Resposta=?, RespostaDet=?, Data=?, Hora=? "
                + "where CodPessoa=? and CodQuestao=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaQst.getCodPessoa());
            consulta.setInt(pessoaQst.getCodQuestao());
            consulta.setInt(pessoaQst.getResposta());
            consulta.setString(pessoaQst.getRespostaDet());
            consulta.setString(pessoaQst.getData().toString());
            consulta.setString(pessoaQst.getHora());
            consulta.setBigDecimal(pessoaQst.getCodPessoa());
            consulta.setInt(pessoaQst.getCodQuestao());
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Falha ao atualizar... " + e.getMessage());
        }
    }

    // delete
    public void excluirPessoaQst(PessoaQst pessoaQst, Persistencia persistencia) throws Exception {
        String sql = "delete from pessoaQst where CodPessoa=? and CodQuestao=? and OrdQestao=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaQst.getCodPessoa());
            consulta.setInt(pessoaQst.getCodQuestao());
            consulta.setInt(pessoaQst.getOrdQuestao());
            consulta.delete();
            consulta.close();
        } catch (SQLException e) {
            throw new SQLException("Erro ao excluir..." + e.getMessage());
        }
    }

    public BigDecimal MaxCodigo(Persistencia persistencia) throws Exception {
        try {
            String sql = "select Max(CodPessoa) Codigo from PessoaQst";
            BigDecimal retorno = new BigDecimal("1");
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = retorno.add(new BigDecimal(consult.getString("Codigo")));
                } catch (Exception e) {
                    retorno = new BigDecimal("1");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar próximo código - " + e.getMessage());
        }
    }
}

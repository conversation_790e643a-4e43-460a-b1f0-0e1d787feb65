package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.sql.SQLException;

/**
 *
 * <AUTHOR>
 */
public class LacresDao {

    /**
     * Consulta lacre
     *
     * @param guia - Número guia
     * @param serie - Número Série
     * @param lacre - Número lacre
     * @param persistencia - Conexão ao banco
     * @return - Verdadeiro ou falso
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean consultaLacre(String guia, String serie, String lacre, Persistencia persistencia) throws Exception {
        boolean existe = false;
        Consulta rs = new Consulta("Select top 1 guia from CxfGuiasVol where "
                + " Guia=?"
                + " and Serie=?"
                + " and Lacre=?", persistencia);
        try {
            rs.setString(guia);
            rs.setString(serie);
            rs.setString(lacre);
            rs.select();
            while (rs.Proximo()) {
                existe = true;
            }
            rs.Close();
            return existe;
        } catch (Exception e) {
            throw new Exception("Falha ao validar lacre - " + e.getMessage());
        }
    }

    /**
     * Consulta lacre
     *
     * @param guia - Número guia
     * @param serie - Número Série
     * @param lacre - Número lacre
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão ao banco
     * @return - Verdadeiro - já cadastrado ou falso - ainda não cadastrado
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean LacreValido(String guia, String serie, String lacre, String CodFil, Persistencia persistencia) throws Exception {
        boolean existe = false;
        Consulta rs = new Consulta("Select top 1 guia from CxfGuiasVol where "
                + " CodFil = ?"
                + " and Guia = ?"
                + " and Serie = ?"
                + " and Lacre = ?", persistencia);
        try {
            rs.setString(CodFil);
            rs.setString(guia);
            rs.setString(serie);
            rs.setString(lacre);
            rs.select();
            while (rs.Proximo()) {
                existe = true;
            }
            rs.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao validar lacre - " + e.getMessage());
        }
        return existe;
    }

    /**
     * Grava Lacre
     *
     * @param codFil - Código filial
     * @param codCliCxf - Código cliente caixa forte
     * @param guia - Número guia
     * @param serie - Número Série
     * @param ordem - Número
     * @param qtde - Quantidade
     * @param lacreAtual - Lacre atual
     * @param tipo - Número lacre
     * @param valor - Valor
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void insereLacre(String codFil, String codCliCxf, String guia, String serie, int ordem, int qtde, String lacreAtual,
            int tipo, String valor, Persistencia persistencia) throws Exception {
        String sql = "insert into CxfGuiasVol"
                + " (CodFil, Codcli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Valor) "
                + " values(?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codCliCxf);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setInt(ordem);
            consulta.setInt(qtde);
            consulta.setString(lacreAtual);
            consulta.setInt(tipo);
            consulta.setString(valor);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("LacresDao.insereLacre - " + e.getMessage() + "\r\n"
                    + " insert into CxfGuiasVol"
                    + " (CodFil, Codcli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Valor) "
                    + " values(" + codFil + ", " + codCliCxf + ", " + guia + ", " + serie + ", " + ordem + ", " + qtde + ", " + lacreAtual + ", " + tipo + ", " + valor + ")");
        }
    }

    /**
     * Grava Lacre
     *
     * @param codFil - Código filial
     * @param codCliCxf - Código cliente caixa forte
     * @param guia - Número guia
     * @param serie - Número Série
     * @param ordem - Número
     * @param qtde - Quantidade
     * @param lacreAtual - Lacre atual
     * @param tipo - Número lacre
     * @param valor - Valor
     * @param observacao
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void insereLacre(String codFil, String codCliCxf, String guia, String serie, int ordem, int qtde, String lacreAtual,
            String tipo, String valor, String observacao, Persistencia persistencia) throws Exception {
        String sql = "insert into CxfGuiasVol"
                + " (CodFil, Codcli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Valor, Obs) "
                + " values(?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codCliCxf);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setInt(ordem);
            consulta.setInt(qtde);
            consulta.setString(lacreAtual);
            consulta.setString(tipo);
            consulta.setString(valor);
            consulta.setString(observacao);
            consulta.insert();
            consulta.close();
        } catch (SQLException e) {
            throw new Exception("Falha ao gravar lacre - " + e.getMessage());
        }
    }

    /**
     * Consulta lacre
     *
     * @param Sequencia - Número guia
     * @param Parada - Número Série
     * @param persistencia - Conexão ao banco
     * @return - Quantidade de guias
     * @throws java.lang.Exception - pode gerar exception
     */
    public int ContaGuiasParada(String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        String sql = "select count(*) qtguias from rt_guias where "
                + " sequencia=? and parada=?";
        int qtguias = 1;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Sequencia);
            consult.setString(Parada);
            consult.select();
            consult.Close();
            while (consult.Proximo()) {
                qtguias = consult.getInt("qtguias");
            }
            consult.Close();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return qtguias;
    }

}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PedidoDN;

/**
 *
 * <AUTHOR>
 */
public class PedidoDNDao {

    public void deletarPedidoDN(PedidoDN pedidoDN, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE FROM PedidoDN WHERE  Numero = ? AND CodFil = ? AND Codigo = ? AND  Docto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoDN.getNumero());
            consulta.setString(pedidoDN.getCodFil());
            consulta.setString(pedidoDN.getCodigo());
            consulta.setString(pedidoDN.getDocto());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PedidoDNDao.deletarPedidoDN - " + e.getMessage() + "\r\n"
                    + " DELETE FROM PedidoDN WHERE  Numero = " + pedidoDN.getNumero() + " AND CodFil = " + pedidoDN.getCodFil()
                    + " AND Codigo = " + pedidoDN.getCodigo() + " AND  Docto = " + pedidoDN.getDocto());
        }
    }

    /**
     * Insere uma nova entrada na tabela PedidoDN
     *
     * @param pedidoDN
     * @param persistencia
     * @throws Exception
     */
    public void inserirPedidoDN(PedidoDN pedidoDN, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO PedidoDN values (?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pedidoDN.getNumero());
            consulta.setString(pedidoDN.getCodFil());
            consulta.setString(pedidoDN.getCodigo());
            consulta.setString(pedidoDN.getDocto());
            consulta.setString(pedidoDN.getQtde());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PedidoDNDao.inserirPedidoDN - " + e.getMessage() + "\r\n"
                    + " INSERT INTO PedidoDN values (" + pedidoDN.getNumero() + ", " + pedidoDN.getCodFil() + ", " + pedidoDN.getCodigo() + ","
                    + pedidoDN.getDocto() + ", " + pedidoDN.getQtde() + ") ");
        }
    }
}

package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileProcAnt;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class MobileProcAntDao {

    /**
     * Salva registros no banco de dados
     *
     * @param mobileProcAnt Objecto contendo informações
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void salvarRegistros(MobileProcAnt mobileProcAnt, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO MobileProcAnt (sequencia, param, comando, dtProcant, dtrecebido, hrrecebido) VALUES(?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(mobileProcAnt.getSequencia());
            consulta.setString(mobileProcAnt.getParam());
            consulta.setString(mobileProcAnt.getComando());
            consulta.setString(mobileProcAnt.getDtProcAnt());
            consulta.setString(mobileProcAnt.getDtRecebido());
            consulta.setString(mobileProcAnt.getHrRecebido());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("MobileProcAntDAO.salvarRegistros - " + e.getMessage() + "\r\n"
                    + "INSERT INTO MobileProcAnt (sequencia, param, comando, dtProcant, dtrecebido, hrrecebido) "
                    + " VALUES(" + mobileProcAnt.getSequencia() + "," + mobileProcAnt.getParam() + "," + mobileProcAnt.getComando() + ","
                    + mobileProcAnt.getDtProcAnt() + "," + mobileProcAnt.getDtRecebido() + "," + mobileProcAnt.getHrRecebido() + ")");
        }
    }

    /**
     * Gera o numero da sequencia
     *
     * @param persistencia Conexão com o banco de dados
     * @return sequencia
     * @throws Exception
     */
    public BigDecimal gerarSequencia(Persistencia persistencia) throws Exception {
        BigDecimal sequencia = new BigDecimal("0");
        try {
            String sql = "SELECT MAX(Sequencia) + 1 novasequencia FROM MobileProcAnt";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                sequencia = consulta.getBigDecimal("novasequencia");
            }

            if (sequencia == null) {
                sequencia = new BigDecimal("1");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("MobileProcAntDAO.gerarSequencia - " + e.getMessage() + "\r\n"
                    + "SELECT MAX(Sequencia) + 1 novasequencia FROM MobileProcAnt");
        }
        return sequencia;
    }

}

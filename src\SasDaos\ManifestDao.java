/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Manifest;
import SasBeans.Rt_Perc;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ManifestDao {

    public List<Manifest> buscarManifest(String codfil, String seqRota, String parada, Persistencia persistencia) throws Exception {
        try {
            List<Manifest> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Manifest WHERE CodFil = ? AND SeqRota = ? AND Parada = ? ORDER BY Lacre, Remessa";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(seqRota);
            consulta.setString(parada);
            consulta.select();
            Manifest manifest;
            while (consulta.Proximo()) {
                manifest = new Manifest();
                manifest.setCodFil(consulta.getString("CodFil"));
                manifest.setSeqRota(consulta.getString("SeqRota"));
                manifest.setRemessa(consulta.getString("Remessa"));
                manifest.setParada(consulta.getString("Parada"));
                manifest.setCodRemessa(consulta.getString("CodRemessa"));
                manifest.setQtde(consulta.getString("Qtde"));
                manifest.setValor(consulta.getString("Valor"));
                manifest.setLacre(consulta.getString("Lacre"));
                manifest.setOper_Incl(consulta.getString("Oper_Incl"));
                manifest.setDt_Incl(consulta.getString("Dt_Incl"));
                manifest.setHr_Incl(consulta.getString("Hr_Incl"));
                manifest.setOper_Baixa(consulta.getString("Oper_Baixa"));
                manifest.setDt_Baixa(consulta.getString("Dt_Baixa"));
                manifest.setHr_Baixa(consulta.getString("Hr_Baixa"));
                retorno.add(manifest);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ManifestDao.buscarManifest - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Manifest WHERE CodFil = " + codfil + " AND SeqRota = " + seqRota + " AND Parada = " + parada + " ORDER BY Lacre, Remessa");
        }
    }

    public List<Rt_Perc> listarManifestsDisponiveis(String sequencia, Persistencia persistencia) throws Exception {
        List<Rt_Perc> manifests = new ArrayList<>();
        try {
            String sql = "SELECT Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, '0' Impresso, '0' QtdPreOrder from manifest\n"
                    + "LEFT JOIN Rt_Perc ON Rt_Perc.Sequencia = Manifest.SeqRota\n"
                    + "                    AND Rt_Perc.Codfil = Manifest.Codfil\n"
                    + "                    AND Rt_Perc.Parada = Manifest.Parada\n"
                    + "WHERE Manifest.SeqRota = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();

            while (consulta.Proximo()) {
                Rt_Perc cliente = new Rt_Perc();
                cliente.setER(consulta.getString("ER"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setParada(consulta.getInt("Parada"));
                cliente.setImpresso(consulta.getInt("Impresso"));
                cliente.setPedido(consulta.getString("QtdPreOrder"));
                manifests.add(cliente);
            }
            consulta.Close();
            return manifests;
        } catch (Exception e) {
            throw new Exception("Rt_PercDao.listaClientesBaixados - " + e.getMessage() + "\r\n"
                    + "SELECT Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, count(preorder.sequencia) QtdPreOrder "
                    + " FROM Rt_Perc "
                    + " JOIN RPV ON RPV.Parada = Rt_Perc.Parada AND RPV.SeqRota = Rt_Perc.Sequencia "
                    + " left join Rotas ON Rotas.Sequencia = Rt_Perc.Sequencia "
                    + " left join PreOrder ON PreOrder.DtColeta = Rotas.Data "
                    + "                  AND PreOrder.CodFil  = Rt_Perc.CodFil "
                    + "                  AND PreOrder.CodCli1 = Rt_perc.CodCli1 "
                    + "                  AND PreOrder.RPV is not null  "
                    + " WHERE Sequencia = " + sequencia + " AND HrBaixa != '' "
                    + " GROUP BY Rt_Perc.Parada, Rt_Perc.NRed, Rt_Perc.ER, RPV.Impresso, Rt_Perc.HrCheg "
                    + " ORDER BY HrCheg DESC");
        }
    }

    /**
     * Tenta atualizar tabela Manifest antes de tentar inserir, e só insere se
     * não haver registro anterior.
     *
     * @param manifest
     * @param persistencia
     * @return Remessa
     * @throws Exception
     */
    public String inserirManifest(Manifest manifest, Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @nRemessa INT = (SELECT CASE WHEN count(*) > 0 \n"
                    + "    THEN MAX(Manifest.Remessa) \n"
                    + "    ELSE (SELECT ISNULL(MAX(Manifest.Remessa),0) + 1 \n"
                    + "                FROM Manifest \n"
                    + "                WHERE Manifest.SeqRota = ? AND Manifest.Parada = ? AND Manifest.CodFil = ?) \n"
                    + "    END Remessa\n"
                    + "    FROM Manifest\n"
                    + "    WHERE Manifest.SeqRota = ? AND Manifest.Parada = ? AND Manifest.CodFil = ? AND Manifest.lacre = ?);\n"
                    + ""
                    + "UPDATE Manifest SET Qtde = Qtde + ?, Valor = Valor + ? \n"
                    + " WHERE Manifest.CodFil = ? AND Manifest.SeqRota = ? \n"
                    + " AND Manifest.Remessa = @nRemessa \n"
                    + " AND Manifest.Parada = ? \n"
                    + " AND Manifest.Lacre = ?; \n"
                    + " \n"
                    + " INSERT INTO Manifest (CodFil, SeqRota, Remessa, Parada, CodRemessa, Qtde, Valor, Lacre, Oper_Incl, Dt_Incl, Hr_Incl) \n"
                    + " SELECT ?, ?, @nRemessa, ?, ?, ?, ?, ?, ?, ?, ? \n"
                    + " FROM (SELECT COUNT(*) AS qtde_cadastrado FROM  Manifest WHERE CodFil = ? AND SeqRota = ? AND Remessa = @nRemessa \n"
                    + " AND Parada = ? AND Lacre = ?) AS A\n"
                    + " WHERE A.qtde_cadastrado = 0;"
                    + ""
                    + " SELECT @nRemessa Remessa;";
            Consulta consulta = new Consulta(sql, persistencia);

            // DECLARE
            consulta.setString(manifest.getSeqRota());
            consulta.setString(manifest.getParada());
            consulta.setString(manifest.getCodFil());
            consulta.setString(manifest.getSeqRota());
            consulta.setString(manifest.getParada());
            consulta.setString(manifest.getCodFil());
            consulta.setString(manifest.getLacre());

            // UPDATE
            consulta.setString(manifest.getQtde());
            consulta.setString(manifest.getValor());
            consulta.setString(manifest.getCodFil());
            consulta.setString(manifest.getSeqRota());
            consulta.setString(manifest.getParada());
            consulta.setString(manifest.getLacre());

            // INSERT
            consulta.setString(manifest.getCodFil());
            consulta.setString(manifest.getSeqRota());
            consulta.setString(manifest.getParada());
            consulta.setString(manifest.getCodRemessa());
            consulta.setString(manifest.getQtde());
            consulta.setString(manifest.getValor());
            consulta.setString(manifest.getLacre());
            consulta.setString(manifest.getOper_Incl());
            consulta.setString(manifest.getDt_Incl());
            consulta.setString(manifest.getHr_Incl());
            consulta.setString(manifest.getCodFil());
            consulta.setString(manifest.getSeqRota());
            consulta.setString(manifest.getParada());
            consulta.setString(manifest.getLacre());

            consulta.select();

            String retorno = "1";
            if (consulta.Proximo()) {
                retorno = consulta.getString("Remessa");
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ManifestDao.inserirManifest - " + e.getMessage() + "\r\n"
                    + "");
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileHW;

/**
 *
 * <AUTHOR>
 */
public class MobileHWDao {

    /**
     * Busca as configurações do cofre
     *
     * @param imei
     * @param persistencia
     * @return
     * @throws Exception
     */
    public MobileHW obterConfiguracao(String imei, Persistencia persistencia) throws Exception {
        try {
            MobileHW retorno = null;
            String sql = " SELECT * \n"
                    + " FROM MobileHW \n"
                    + " WHERE IMEI = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(imei);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new MobileHW();
                retorno.setIMEI(consulta.getString("IMEI"));
                retorno.setParametro(consulta.getString("Parametro"));
                retorno.setCodEquip(consulta.getString("CodEquip"));
                retorno.setTipoEquip(consulta.getString("TipoEquip"));
                retorno.setMarcaEquip(consulta.getString("MarcaEquip"));
                retorno.setSerialEquip(consulta.getString("SerialEquip"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setSaldo(consulta.getString("Saldo"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_alter(consulta.getString("Hr_alter"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("MobileHWDao.obterConfiguracao - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM MobileHW WHERE IMEI = " + imei);
        }
    }

    /**
     * Insere um novo cofre na base de dados
     *
     * @param mobileHW
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCofre(MobileHW mobileHW, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE MobileHW SET SerialEquip = ?, TipoEquip = ?, CodCli = ?, CodFil = ?, CodEquip = ?, \n"
                    + " Operador = ?, Dt_Alter = ?, Hr_Alter = ?"
                    + " WHERE IMEI = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mobileHW.getSerialEquip());
            consulta.setString(mobileHW.getTipoEquip());
            consulta.setString(mobileHW.getCodCli());
            consulta.setString(mobileHW.getCodFil());
            consulta.setString(mobileHW.getCodEquip());
            consulta.setString(mobileHW.getOperador());
            consulta.setString(mobileHW.getDt_Alter());
            consulta.setString(mobileHW.getHr_alter());
            consulta.setString(mobileHW.getIMEI());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("MobileHWDao.atualizarCofre - " + e.getMessage() + "\r\n"
                    + "  UPDATE MobileHW SET SerialEquip = " + mobileHW.getSerialEquip() + ", TipoEquip = " + mobileHW.getTipoEquip() + ", "
                    + " CodCli = " + mobileHW.getCodCli() + ", CodFil = " + mobileHW.getCodFil() + ", CodEquip = " + mobileHW.getCodEquip() + ", \n"
                    + " Operador = " + mobileHW.getOperador() + ", Dt_Alter = " + mobileHW.getDt_Alter() + ", Hr_Alter = " + mobileHW.getHr_alter() + ""
                    + " WHERE IMEI = " + mobileHW.getIMEI() + "");
        }
    }

    /**
     * Insere um novo cofre na base de dados
     *
     * @param mobileHW
     * @param persistencia
     * @throws Exception
     */
    public void atualizarStatusCofre(MobileHW mobileHW, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE MobileHW SET Bateria = ?, Versao = ?, \n"
                    + " Operador = ?, Dt_Alter = ?, Hr_Alter = ?"
                    + " WHERE IMEI = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mobileHW.getBateria());
            consulta.setString(mobileHW.getVersao());
            consulta.setString(mobileHW.getOperador());
            consulta.setString(mobileHW.getDt_Alter());
            consulta.setString(mobileHW.getHr_alter());
            consulta.setString(mobileHW.getIMEI());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("MobileHWDao.atualizarStatusCofre - " + e.getMessage() + "\r\n"
                    + "  UPDATE MobileHW SET Bateria = " + mobileHW.getBateria() + ", Versao = " + mobileHW.getVersao() + ", "
                    + " Operador = " + mobileHW.getOperador() + ", Dt_Alter = " + mobileHW.getDt_Alter() + ", Hr_Alter = " + mobileHW.getHr_alter()
                    + " WHERE IMEI = " + mobileHW.getIMEI() + "");
        }
    }

    /**
     * Insere um novo cofre na base de dados
     *
     * @param mobileHW
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String cadastrarCofre(MobileHW mobileHW, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO MobileHW (IMEI, Parametro, CodEquip, TipoEquip, MarcaEquip, SerialEquip, \n"
                    + " CodCli, CodFil, Saldo, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, (SELECT ISNULL(MAX(CodEquip), 1000) + 1 CodEquip FROM MobileHW), ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mobileHW.getIMEI());
            consulta.setString(mobileHW.getParametro());
            consulta.setString(mobileHW.getTipoEquip());
            consulta.setString(mobileHW.getMarcaEquip());
            consulta.setString(mobileHW.getSerialEquip());
            consulta.setString(mobileHW.getCodCli());
            consulta.setString(mobileHW.getCodFil());
            consulta.setString(mobileHW.getSaldo());
            consulta.setString(mobileHW.getOperador());
            consulta.setString(mobileHW.getDt_Alter());
            consulta.setString(mobileHW.getHr_alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("MobileHWDao.cadastrarCofre - " + e.getMessage() + "\r\n"
                    + " INSERT INTO MobileHW (IMEI, Parametro, CodEquip, TipoEquip, MarcaEquip, SerialEquip, \n"
                    + " CodCli, CodFil, Saldo, Operador, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (" + mobileHW.getIMEI() + ", " + mobileHW.getParametro() + ", "
                    + " (SELECT ISNULL(MAX(CodEquip), 0) + 1000 CodEquip FROM MobileHW), " + mobileHW.getTipoEquip() + ", " + mobileHW.getMarcaEquip() + ", \n"
                    + "" + mobileHW.getSerialEquip() + ", " + mobileHW.getCodCli() + ", " + mobileHW.getCodFil() + ", " + mobileHW.getSaldo() + ", "
                    + "" + mobileHW.getOperador() + ", " + mobileHW.getDt_Alter() + ", " + mobileHW.getHr_alter() + ") ");
        }
        try {
            String sql = " SELECT CodEquip FROM MobileHW WHERE IMEI = ? ", retorno = null;
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mobileHW.getIMEI());
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getString("CodEquip");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("MobileHWDao.cadastrarCofre - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM MobileHW WHERE IMEI = " + mobileHW.getIMEI());
        }
    }

    public void atualizarSaldo(String codCofre, String valor, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE MobileHW SET Saldo = (Saldo + ?) WHERE CodEquip = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(codCofre);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("MobileHWDao.atualizarSaldo - " + e.getMessage() + "\r\n"
                    + " UPDATE MobileHW SET Saldo = (Saldo + " + valor + ") WHERE CodCofre = " + codCofre);
        }
    }

    public void zerarSaldo(String codCofre, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE MobileHW SET Saldo = 0 WHERE CodEquip = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("MobileHWDao.atualizarSaldo - " + e.getMessage() + "\r\n"
                    + " UPDATE MobileHW SET Saldo = 0 WHERE CodCofre = " + codCofre);
        }
    }

    /**
     * Busca as configurações do cofre
     *
     * @param codEquip
     * @param persistencia
     * @return
     * @throws Exception
     */
    public MobileHW obterInfoCofre(String codEquip, Persistencia persistencia) throws Exception {
        try {
            MobileHW retorno = null;
            String sql = " SELECT * \n"
                    + " FROM MobileHW \n"
                    + " WHERE CodEquip = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codEquip);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new MobileHW();
                retorno.setParametro(consulta.getString("Parametro"));
                retorno.setIMEI(consulta.getString("IMEI"));
                retorno.setCodEquip(consulta.getString("CodEquip"));
                retorno.setTipoEquip(consulta.getString("TipoEquip"));
                retorno.setMarcaEquip(consulta.getString("MarcaEquip"));
                retorno.setSerialEquip(consulta.getString("SerialEquip"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setSaldo(consulta.getString("Saldo"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_alter(consulta.getString("Hr_alter"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("MobileHWDao.obterConfiguracao - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM MobileHW WHERE CodEquip = " + codEquip);
        }
    }
}

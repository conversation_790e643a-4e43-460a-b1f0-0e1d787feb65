/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.PstServ;
import SasBeansCompostas.CarregaFuncs;
import SasBeansCompostas.FuncaoPessoa;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FuncaoDao {

    String sql;

    /*
     * Retorna dados de Funcionário Ausente
     *
     * @param sSecao - Código do posto de trabalho
     * @param sSituacao - Situação do funcionário A=ausente
     * @param sSCodFil - Código Filial
     * @param persistencia - conexão com o banco
     * @return - retorna lista com funcionários ausentes
     * @throws java.lang.Exception - pode gerar exception
     */

    public List<CarregaFuncs> getFucao(String sPagN, String sLinP, String sSecao, String sSituacao, String SCodFil, Persistencia persistencia) throws Exception {
        Integer iPagN = Integer.parseInt(sPagN);
        Integer iLinP = Integer.parseInt(sLinP);

        sql = "DECLARE @PageNumber AS INT, @RowspPage AS INT "
                + "SET @PageNumber = " + iPagN
                + "SET @RowspPage = " + iLinP
                + " select *       FROM ( "
                + "             SELECT ROW_NUMBER() OVER(ORDER BY funcion.Matr) AS Ordem, "
                + "     funcion.Matr, funcion.Nome, funcion.Nome_Guer, cargos.Descricao, pstserv.Local, funcion.CodFil, funcion.Regional, funcion.Secao, funcion.CCusto,    "
                + "     funcion.CodPonto, funcion.Dt_Admis, funcion.Cargo, funcion.CodCargo, funcion.Apresen, funcion.Situacao, funcion.Dt_Situac,    "
                + "                  funcion.Escala, funcion.Horario, funcion.GrpEscala,    "
                + "                  funcion.Funcao,    "
                + "                  funcion.Dt_FormIni,    "
                + "                  funcion.Dt_FormFim,    "
                + "                  funcion.LocalForm,    "
                + "                  funcion.Certific,    "
                + "                  funcion.Reg_PF,    "
                + "                  funcion.Local_PF,    "
                + "                  funcion.Reg_PFUF,    "
                + "                  funcion.Reg_PFDt,    "
                + "                  funcion.CarNacVig,    "
                + "                  funcion.DtValCNV,    "
                + "                  funcion.Reg_MT,    "
                + "                  funcion.Dt_Recicl,    "
                + "                  funcion.Dt_VenCurs,    "
                + "                  funcion.Dt_ExameMe,    "
                + "                  funcion.Dt_Psico,    "
                + "                  funcion.ExtensaoTV,    "
                + "                  funcion.ExtSegPes,    "
                + "                  funcion.ExtEscolta,    "
                + "                  funcion.GrupoSang,    "
                + "                  funcion.Instrucao,    "
                + "                  funcion.Raca,    "
                + "                  funcion.EstCivil,    "
                + "                  funcion.Endereco,    "
                + "                  funcion.Numero,    "
                + "                  funcion.Complemento,    "
                + "                  funcion.Bairro,    "
                + "                  funcion.Cidade,    "
                + "                  funcion.UF,    "
                + "                  funcion.CEP,    "
                + "                  funcion.Fone1,    "
                + "                  funcion.Fone2,    "
                + "                  funcion.Email,    "
                + "                  funcion.Dt_Nasc,    "
                + "                  funcion.Sexo,    "
                + "                  funcion.Naturalid,    "
                + "                  funcion.Pai,    "
                + "                  funcion.Mae,    "
                + "                  funcion.Conjuge,    "
                + "                  funcion.CNH,    "
                + "                  funcion.Dt_VenCNH,    "
                + "                  funcion.Categoria,    "
                + "                  funcion.RG,    "
                + "                  funcion.OrgEmis,    "
                + "                  funcion.RgDtEmis,    "
                + "                  funcion.CPF,    "
                + "                  funcion.PIS,    "
                + "                  funcion.Reservista,    "
                + "                  funcion.ReservCat,    "
                + "                  funcion.CTPS_Nro,    "
                + "                  funcion.CTPS_Serie,    "
                + "                  funcion.CTPS_UF,    "
                + "                  funcion.CTPS_Emis,    "
                + "                  funcion.TitEleit,    "
                + "                  funcion.TitEZona,    "
                + "                  funcion.TitSecao,    "
                + "                  funcion.Ct_Banco,    "
                + "                  funcion.Ct_Agencia,    "
                + "                  funcion.Ct_Conta,    "
                + "                  funcion.Ct_CodOper,    "
                + "                  funcion.Obs,    "
                + "                  funcion.Salario,    "
                + "                  funcion.Sindicato,    "
                + "                  funcion.CHMes,    "
                + "                  funcion.CHSeman,    "
                + "                  funcion.He_Periodo,    "
                + "                  funcion.DepIR,    "
                + "                  funcion.DepSF,    "
                + "                  funcion.FGTSOpcao,    "
                + "                  funcion.FGTSBanco,    "
                + "                  funcion.FGTSAg,    "
                + "                  funcion.PgCtSin,    "
                + "                  funcion.AssMedic,    "
                + "                  funcion.DepAssMed,    "
                + "                  funcion.CestaBas,    "
                + "                  funcion.ValeRef,    "
                + "                  funcion.ConvFarma,    "
                + "                  funcion.SegVida,    "
                + "                  funcion.TipoAdm,    "
                + "                  funcion.DefFis,    "
                + "                  funcion.DefFisTipo,    "
                + "                  funcion.DefFisDesc,    "
                + "                  funcion.Nacionalid,    "
                + "                  funcion.AnoCheg,    "
                + "                  funcion.FolhaLivro,    "
                + "                  funcion.PgINSS,    "
                + "                  funcion.PgIR,    "
                + "                  funcion.SEFIPOcor,    "
                + "                  funcion.Conta_Ctb,    "
                + "                  funcion.Altura,    "
                + "                  funcion.Peso,    "
                + "                  funcion.Dt_Demis,    "
                + "                  funcion.CodCidade,    "
                + "                  funcion.CodNaturalid,    "
                + "                  funcion.ExpGESP,    "
                + "                  funcion.Vinculo,    "
                + "                  funcion.FormaPgto,    "
                + "                  funcion.Jornada,    "
                + "                  funcion.SegDesemp,    "
                + "                  funcion.FPAdiant,    "
                + "                  funcion.Operador,    "
                + "                  funcion.Dt_Alter,    "
                + "                  funcion.Hr_Alter    "
                + "                  from funcion    "
                + "                  left join cargos on funcion.cargo = cargos.cargo   "
                + "                  left join pstserv on pstserv.secao = funcion.secao and pstserv.codfil = funcion.codfil   "
                + "                  where funcion.CodFil=?";
        if (!"null".equals(sSituacao)) {
            sql += " and funcion.Situacao=? ";
        }
        if (!"null".equals(sSecao)) {
            sql += " and funcion.Secao=? ";
        }
        sql += ") AS TBL "
                + " WHERE Ordem BETWEEN ((@PageNumber - 1) * @RowspPage + 1) AND (@PageNumber * @RowspPage) "
                + " ORDER BY Matr ";

        List<CarregaFuncs> lCarregaFuncs = new ArrayList<CarregaFuncs>();
        CarregaFuncs oCarregaFuncs;
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(SCodFil);
            if (!"null".equals(sSituacao)) {
                stm.setString(sSituacao);
            }
            if (!"null".equals(sSecao)) {
                stm.setString(sSecao);
            }

            stm.select();

            while (stm.Proximo()) {
                Funcion oFuncion = new Funcion();
                Cargos oCargos = new Cargos();
                PstServ oPstServ = new PstServ();
                oCarregaFuncs = new CarregaFuncs();

                oFuncion.setMatr(stm.getString("Matr"));
                oFuncion.setNome(stm.getString("Nome"));
                oCargos.setDescricao(stm.getString("Descricao"));
                oPstServ.setLocal(stm.getString("Local"));
                oFuncion.setNome_Guer(stm.getString("Nome_Guer"));
                oFuncion.setRegional(stm.getInt("Regional"));
                oFuncion.setSecao(stm.getString("Secao"));
                oFuncion.setCCusto(stm.getString("CCusto"));
                oFuncion.setCodPonto(stm.getString("CodPonto"));
                oFuncion.setDt_Admis(stm.getString("Dt_Admis"));
                oFuncion.setCargo(stm.getString("Cargo"));
                oFuncion.setCodCargo(stm.getString("CodCargo"));
                oFuncion.setApresen(stm.getString("Apresen"));
                oFuncion.setSituacao(stm.getString("Situacao"));
                oFuncion.setDt_Situac(stm.getString("Dt_Situac"));
                oFuncion.setEscala(stm.getString("Escala"));
                oFuncion.setHorario(stm.getInt("Horario"));
                oFuncion.setGrpEscala(stm.getInt("GrpEscala"));
                oFuncion.setFuncao(stm.getString("Funcao"));
                oFuncion.setDt_FormIni(stm.getString("Dt_FormIni"));
                oFuncion.setDt_FormFim(stm.getString("Dt_FormFim"));
                oFuncion.setLocalForm(stm.getString("LocalForm"));
                oFuncion.setCertific(stm.getString("Certific"));
                oFuncion.setReg_PF(stm.getString("Reg_PF"));
                oFuncion.setLocal_PF(stm.getString("Local_PF"));
                oFuncion.setReg_PF(stm.getString("Reg_PFUF"));
                oFuncion.setReg_PFDt(stm.getString("Reg_PFDt"));
                oFuncion.setCarNacVig(stm.getString("CarNacVig"));
                oFuncion.setDtValCNV(stm.getString("DtValCNV"));
                oFuncion.setReg_MT(stm.getString("Reg_MT"));
                oFuncion.setDt_Recicl(stm.getString("Dt_Recicl"));
                oFuncion.setDt_VenCurs(stm.getString("Dt_VenCurs"));
                oFuncion.setDt_ExameMe(stm.getString("Dt_ExameMe"));
                oFuncion.setDt_Psico(stm.getString("Dt_Psico"));
                oFuncion.setExtensaoTV(stm.getString("ExtensaoTV"));
                oFuncion.setExtSegPes(stm.getString("ExtSegPes"));
                oFuncion.setExtEscolta(stm.getString("ExtEscolta"));
                oFuncion.setGrupoSang(stm.getString("GrupoSang"));
                oFuncion.setInstrucao(stm.getString("Instrucao"));
                oFuncion.setRaca(stm.getString("Raca"));
                oFuncion.setEstCivil(stm.getString("EstCivil"));
                oFuncion.setEndereco(stm.getString("Endereco"));
                oFuncion.setNumero(stm.getString("Numero"));
                oFuncion.setComplemento(stm.getString("Complemento"));
                oFuncion.setBairro(stm.getString("Bairro"));
                oFuncion.setCidade(stm.getString("Cidade"));
                oFuncion.setUF(stm.getString("UF"));
                oFuncion.setCEP(stm.getString("CEP"));
                oFuncion.setFone1(stm.getString("Fone1"));
                oFuncion.setFone2(stm.getString("Fone2"));
                oFuncion.setEmail(stm.getString("Email"));
                oFuncion.setDt_Nasc(stm.getString("Dt_Nasc"));
                oFuncion.setSexo(stm.getString("Sexo"));
                oFuncion.setNaturalid(stm.getString("Naturalid"));
                oFuncion.setPai(stm.getString("Pai"));
                oFuncion.setMae(stm.getString("Mae"));
                oFuncion.setConjuge(stm.getString("Conjuge"));
                oFuncion.setCNH(stm.getString("CNH"));
                oFuncion.setDt_VenCNH(stm.getString("Dt_VenCNH"));
                oFuncion.setCategoria(stm.getString("Categoria"));
                oFuncion.setRG(stm.getString("RG"));
                oFuncion.setOrgEmis(stm.getString("OrgEmis"));
                oFuncion.setRgDtEmis(stm.getString("RgDtEmis"));
                oFuncion.setCPF(stm.getString("CPF"));
                oFuncion.setPIS(stm.getString("PIS"));
                oFuncion.setReservista(stm.getString("Reservista"));
                oFuncion.setReservCat(stm.getString("ReservCat"));
                oFuncion.setCTPS_Nro(stm.getString("CTPS_Nro"));
                oFuncion.setCTPS_Serie(stm.getString("CTPS_Serie"));
                oFuncion.setCTPS_UF(stm.getString("CTPS_UF"));
                oFuncion.setCTPS_Emis(stm.getString("CTPS_Emis"));
                oFuncion.setTitEleit(stm.getString("TitEleit"));
                oFuncion.setTitEZona(stm.getString("TitEZona"));
                oFuncion.setTitSecao(stm.getString("TitSecao"));
                oFuncion.setCt_Banco(stm.getString("Ct_Banco"));
                oFuncion.setCt_Agencia(stm.getString("Ct_Agencia"));
                oFuncion.setCt_Conta(stm.getString("Ct_Conta"));
                oFuncion.setCt_CodOper(stm.getString("Ct_CodOper"));
                oFuncion.setObs(stm.getString("Obs"));
                oFuncion.setSalario(stm.getString("Salario"));
                oFuncion.setSindicato(stm.getString("Sindicato"));
                oFuncion.setCHMes(stm.getString("CHMes"));
                oFuncion.setCHSeman(stm.getString("CHSeman"));
                oFuncion.setHe_Periodo(stm.getString("He_Periodo"));
                oFuncion.setDepIR(stm.getString("DepIR"));
                oFuncion.setDepSF(stm.getString("DepSF"));
                oFuncion.setFGTSOpcao(stm.getString("FGTSOpcao"));
                oFuncion.setFGTSBanco(stm.getString("FGTSBanco"));
                oFuncion.setFGTSAg(stm.getString("FGTSAg"));
                oFuncion.setPgCtSin(stm.getString("PgCtSin"));
                oFuncion.setAssMedic(stm.getString("AssMedic"));
                oFuncion.setDepAssMed(stm.getString("DepAssMed"));
                oFuncion.setCestaBas(stm.getString("CestaBas"));
                oFuncion.setValeRef(stm.getString("ValeRef"));
                oFuncion.setConvFarma(stm.getString("ConvFarma"));
                oFuncion.setSegVida(stm.getString("SegVida"));
                oFuncion.setTipoAdm(stm.getString("TipoAdm"));
                oFuncion.setDefFis(stm.getString("DefFis"));
                oFuncion.setDefFisTipo(stm.getString("DefFisTipo"));
                oFuncion.setDefFisDesc(stm.getString("DefFisDesc"));
                oFuncion.setNacionalid(stm.getString("Nacionalid"));
                oFuncion.setAnoCheg(stm.getString("AnoCheg"));
                oFuncion.setFolhaLivro(stm.getString("FolhaLivro"));
                oFuncion.setPgINSS(stm.getString("PgINSS"));
                oFuncion.setPgIR(stm.getString("PgIR"));
                oFuncion.setSEFIPOcor(stm.getString("SEFIPOcor"));
                oFuncion.setConta_Ctb(stm.getString("Conta_Ctb"));
                oFuncion.setAltura(stm.getString("Altura"));
                oFuncion.setPeso(stm.getString("Peso"));
                oFuncion.setDt_Demis(stm.getString("Dt_Demis"));
                oFuncion.setCodCidade(stm.getString("CodCidade"));
                oFuncion.setCodNaturalid(stm.getString("CodNaturalid"));
                oFuncion.setExpGESP(stm.getString("ExpGESP"));
                oFuncion.setVinculo(stm.getString("Vinculo"));
                oFuncion.setFormaPgto(stm.getString("FormaPgto"));
                oFuncion.setJornada(stm.getString("Jornada"));
                oFuncion.setSegDesemp(stm.getString("SegDesemp"));
                oFuncion.setOperador(stm.getString("Operador"));
                oFuncion.setDt_Alter(stm.getString("Dt_Alter"));
                oFuncion.setHr_Alter(stm.getString("Hr_Alter"));

                oCarregaFuncs.setFuncion(oFuncion);
                oCarregaFuncs.setCargos(oCargos);
                oCarregaFuncs.setPstServ(oPstServ);

                lCarregaFuncs.add(oCarregaFuncs);
            }
            stm.Close();

            return lCarregaFuncs;
        } catch (Exception e) {
            throw new Exception("Falha ao executar gtFuncao/FuncaoDao  - " + e.getMessage());
        }
    }

    public List<Pessoa> buscaEmailnSat(String sEmail, Persistencia persistencia) throws Exception {
        List<Pessoa> lFuncionC = null;
        String sql = " select SubString(pessoa.nome,1,20) Nome_Guer "
                + ",pessoa.cidade "
                + ",pessoa.rg "
                + ",pessoa.cpf "
                + ",pessoa.dt_nasc "
                //+ ",pessoa.pwportal "
                + ",pessoa.pwweb "
                // + ",pessoa.situacao "
                + "from pessoa "
                + "where "
                // + "pessoa.dt_ultacportal is null and "
                + "pessoa.email=? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sEmail);
            consult.select();
            while (consult.Proximo()) {
                lFuncionC = new ArrayList<>();
                Pessoa oPessoa = new Pessoa();

                oPessoa.setEmail(sEmail);
                oPessoa.setNome(consult.getString("Nome_Guer"));
                oPessoa.setCidade(consult.getString("cidade"));
                oPessoa.setRG(consult.getString("rg"));
                oPessoa.setCPF(consult.getString("cpf"));
                oPessoa.setDt_nasc(consult.getString("dt_nasc"));
                oPessoa.setPWWeb(consult.getString("pwweb"));

                lFuncionC.add(oPessoa);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar Matrícula - " + e.getMessage());
        }
        return lFuncionC;
    }

    public List<Pessoa> buscaEmailPa(String sEmail, Persistencia persistencia) throws Exception {
        List<Pessoa> lFuncionC = null;
        String sql = " select SubString(pessoa.nome,1,20) Nome_Guer "
                + ",pessoa.cidade "
                + ",pessoa.rg "
                + ",pessoa.cpf "
                + ",pessoa.dt_nasc "
                + ",pessoa.pwportal "
                // + ",pessoa.situacao "
                + "from pessoa "
                + "where "
                // + "pessoa.dt_ultacportal is null and "
                + "pessoa.email=? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sEmail);
            consult.select();
            while (consult.Proximo()) {
                lFuncionC = new ArrayList<>();
                Pessoa oPessoa = new Pessoa();

                oPessoa.setEmail(sEmail);
                oPessoa.setNome(consult.getString("Nome_Guer"));
                oPessoa.setCidade(consult.getString("cidade"));
                oPessoa.setRG(consult.getString("rg"));
                oPessoa.setCPF(consult.getString("cpf"));
                oPessoa.setDt_nasc(consult.getString("dt_nasc"));
                oPessoa.setPWPortal(consult.getString("pwportal"));

                lFuncionC.add(oPessoa);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar Matrícula - " + e.getMessage());
        }
        return lFuncionC;
    }

    public List<FuncaoPessoa> buscaMtr(String sMtr, Persistencia persistencia) throws Exception {
        List<FuncaoPessoa> lFuncionC = new ArrayList<FuncaoPessoa>();
        FuncaoPessoa fucPess = null;
        String sql = " select funcion.nome_guer, "
                + " funcion.cidade, funcion.rg, "
                + " funcion.cpf, funcion.dt_nasc, "
                + " pessoa.pwportal "
                + " from pessoa as pessoa "
                + " Left Join funcion as funcion on "
                + " funcion.matr=pessoa.matr where "
                + " funcion.situacao<>'D' "
                + " and pessoa.dt_ultacportal is not null "
                + " and funcion.matr=? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sMtr);
            consult.select();
            while (consult.Proximo()) {
                fucPess = new FuncaoPessoa();

                Funcion oFuncion = new Funcion();
                Pessoa oPessoa = new Pessoa();

                oFuncion.setMatr(sMtr);
                oFuncion.setCidade(consult.getString("cidade"));
                oFuncion.setRG(consult.getString("rg"));
                oFuncion.setCPF(consult.getString("cpf"));
                oFuncion.setDt_Nasc(consult.getString("dt_nasc"));
                oPessoa.setPWPortal(consult.getString("pwportal"));

                fucPess.setFuncion(oFuncion);
                fucPess.setPessoa(oPessoa);
            }
            lFuncionC.add(fucPess);
            consult.Close();
            return lFuncionC;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar Matrícula - " + e.getMessage());
        }
    }

    public void salvarSenhaNsat(String sEmail, String sSenha, Persistencia persistencia) throws Exception {

        String sql = "update pessoa set "
                + " pwweb = ?, "
                + " dt_ultacportal = ? "
                + " where email = ?";
        try {
            //Consulta consult = new Consulta(sql, persistencia);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sSenha);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(sEmail);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao salvar senha - " + e.getMessage());
        }
    }

    public void salvarSenha(String sEmail, String sSenha, Persistencia persistencia) throws Exception {

        String sql = "update pessoa set "
                + " pwportal = ?, "
                + " dt_ultacportal = ? "
                + " where email = ?";
        try {
            //Consulta consult = new Consulta(sql, persistencia);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sSenha);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(sEmail);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao salvar senha - " + e.getMessage());
        }
    }
    
    public void gravaImg(String vSequencia, String vParametro, String vSistema,  String vEndereco, String vImagem, Persistencia persistencia) throws Exception {
        
        Persistencia dbsatellite;
        //dbsatellite = this.pool.getConexao("SATELLITE");
        
        String vSQL = "Insert into QueueImg(Sequencia, Parametro, Sistema, Endereco, Gravado,Imagem, Operador, Dt_alter, Hr_alter) Values("+
                     "(Select Max(sequencia)+1 from QueueImg), "+
                     "'"+vParametro+"',"+
                     "'"+vSistema+"',"+
                     "'"+vEndereco+"',"+
                     "'N',"+
                     "'"+vEndereco+"',"+
                     "'"+vImagem+"',"+
                     "'SATMOBEW',"+
                     "'"+br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL")+"',"+                
                     "'"+br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA")+"')";                                
        try {
            //Consulta consult = new Consulta(sql, persistencia);
            Consulta consulta = new Consulta(vSQL, persistencia);                        
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao salvar Imagem - Parametros: " + 
                               vSQL+" - ERRO - "+                    
                               e.getMessage());
        }
    }
}

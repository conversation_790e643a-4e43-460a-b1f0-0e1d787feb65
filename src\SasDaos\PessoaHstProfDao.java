package SasDaos;

import <PERSON>os.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaHstProf;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaHstProfDao {

    /**
     * Inserção de registro na tabela PessoaHstProf
     *
     * @param php - Objeto PessoaHstProf
     * @param persistencia - Conexão com o banco
     * @throws Exception - pode gerar Exception
     */
    public void gravaPessoaProf(String sCod, PessoaHstProf php, Persistencia persistencia) throws Exception { //verificar se parametro php não vai criar conflito de linguagem, kkkkk
        String sql = "insert into PessoaHstProf "
                + " (CodPessoa,Ordem,Empresa,Endereco,Fone,Dt_Admis,<PERSON>t_De<PERSON>,Cargo,Ult_Salario, Supervisor,MotivoSaida)"
                + " values (?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Integer Ordem = MaxOrdem(persistencia);

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sCod);
            consulta.setInt(Ordem);
            consulta.setString(php.getEmpresa());
            consulta.setString(php.getEndereco());
            consulta.setString(FuncoesString.limpa2(php.getFone(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(php.getDt_Admis()));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(php.getDt_Demis()));
            consulta.setString(php.getCargo());
            consulta.setBigDecimal(php.getUlt_Salario());
            consulta.setString(php.getSupervisor());
            consulta.setString(php.getMotivoSaida());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir Histórico Profissional - " + e.getMessage());
        }
    }

    /**
     * Devolve o proximo número da ordem dos históricos da pessoa
     *
     * @param php - Objeto PessoaHstProf, importate apenas o código de pessoa
     * @param persistencia - Conxeão com o banco de dados
     * @return - Retorna o próximo elemento do campo ordem
     * @throws Exception
     */
    public Integer MaxOrdem(Persistencia persistencia) throws Exception {
        try {
            Integer retorno = 1;
            String sql = "select Max(Ordem)+1 ordem from pessoahstprof";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                retorno = retorno + consult.getInt("ordem");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar ordem - " + e.getMessage());
        }
    }

    /**
     * Devolve todo o histórico profissional da Pessoa
     *
     * @param php - Objeto PessoaHstProf contendo o Código de Pessoa
     * @param persistencia - Conexão com banco de dados
     * @return - lista de histórico profissional
     * @throws Exception - Pode lanças Exception
     */
    public List<PessoaHstProf> listaHistoricoProfissional(PessoaHstProf php, Persistencia persistencia) throws Exception {
        try {
            List<PessoaHstProf> retorno = new ArrayList();
            String sql = "select * from pessoahstprof where codpessoa = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(php.getCodPessoa().toString());
            consult.select();
            while (consult.Proximo()) {
                PessoaHstProf phst = new PessoaHstProf();
                phst.setCodPessoa(consult.getString("Codigo"));
                phst.setOrdem(consult.getInt("Ordem"));
                phst.setEmpresa(consult.getString("Empresa"));
                phst.setEndereco(consult.getString("Endereco"));
                phst.setEndereco(consult.getString("Fone"));
                phst.setCargo(consult.getString("Cargo"));
                phst.setDt_Admis(consult.getDate("Dt_Admis").toString());
                phst.setDt_Demis(consult.getDate("Dt_Demis").toString());
                phst.setMotivoSaida(consult.getString("MotivoSaida"));
                phst.setSupervisor(consult.getString("MotivoSaida"));
                phst.setUlt_Salario(new BigDecimal(consult.getString("Ult_Salario")));
                retorno.add(phst);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar histórico profissional de pessoa - " + e.getMessage());
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileHWSt;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MobileHWStDao {

    /**
     * Lista os status de um cofre
     *
     * @param codigo
     * @param IMEI
     * @param data1
     * @param data2
     * @param somenteAlertas
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<MobileHWSt> listarStatusCore(String codigo, String IMEI, String data1, String data2, boolean somenteAlertas,
            Persistencia persistencia) throws Exception {
        try {
            List<MobileHWSt> retorno = new ArrayList<>();
            String sql = " SELECT ";
            if (data1.equals("") || data2.equals("")) {
                sql += " TOP 20 ";
            }
            sql += " * FROM MobileHWSt \n"
                    + " WHERE CodEquip = ? AND IMEI = ? \n";
            if (!data1.equals("") || !data2.equals("")) {
                sql += " AND Data BETWEEN ? AND ? \n";
            }
            if (somenteAlertas) {
                sql += " AND Situacao = '999999' ";
            }
            sql += " ORDER BY Data DESC, Hora DESC ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(IMEI);
            if (!data1.equals("") || !data2.equals("")) {
                consulta.setString(data1);
                consulta.setString(data2);
            }
            consulta.select();
            MobileHWSt mobileHWSt;
            while (consulta.Proximo()) {
                mobileHWSt = new MobileHWSt();
                mobileHWSt.setIMEI(consulta.getString("IMEI"));
                mobileHWSt.setCodEquip(consulta.getString("CodEquip"));
                mobileHWSt.setSequencia(consulta.getString("Sequencia"));
                mobileHWSt.setLatitude(consulta.getString("Latitude"));
                mobileHWSt.setLongitude(consulta.getString("Longitude"));
                mobileHWSt.setSituacao(consulta.getString("Situacao"));
                mobileHWSt.setOperador(consulta.getString("Operador"));
                mobileHWSt.setData(consulta.getString("Data"));
                mobileHWSt.setHora(consulta.getString("Hora"));

                /**
                 * +--------------------+ | o Porta Superior | 1 Aberto , 0
                 * Fechado | o porta Cofre | 1 Aberto , 0 Fechado | o Cassete |
                 * 1 Aberto , 0 Fechado | o Fechadura | 1 Aberto , 0 Fechado | o
                 * Validador | 1 Conectado, 0 Desconectado | o Impressao | 1
                 * Conectado, 0 Desconectado +--------------------+
                 */
                try {
                    mobileHWSt.setPortaSuperior(Integer.valueOf(mobileHWSt.getSituacao().substring(0, 1)));
                } catch (Exception s) {
                    mobileHWSt.setPortaSuperior(9);
                }
                try {
                    mobileHWSt.setPortaCofre(Integer.valueOf(mobileHWSt.getSituacao().substring(1, 2)));
                } catch (Exception s) {
                    mobileHWSt.setPortaCofre(9);
                }
                try {
                    mobileHWSt.setCassete(Integer.valueOf(mobileHWSt.getSituacao().substring(2, 3)));
                } catch (Exception s) {
                    mobileHWSt.setCassete(9);
                }
                try {
                    mobileHWSt.setFechadura(Integer.valueOf(mobileHWSt.getSituacao().substring(3, 4)));
                } catch (Exception s) {
                    mobileHWSt.setFechadura(9);
                }
                try {
                    mobileHWSt.setValidadora(Integer.valueOf(mobileHWSt.getSituacao().substring(4, 5)));
                } catch (Exception s) {
                    mobileHWSt.setValidadora(9);
                }
                try {
                    mobileHWSt.setImpressao(Integer.valueOf(mobileHWSt.getSituacao().substring(5, 6)));
                } catch (Exception s) {
                    mobileHWSt.setImpressao(9);
                }

                retorno.add(mobileHWSt);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("MobileHWStDao.listarStatusCore - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM MobileHWSt \n"
                    + " WHERE CodEquip = " + codigo + " and Data between " + data1 + " and " + data2 + " \n"
                    + " ORDER BY Data DESC, Hora DESC ");
        }
    }

    /**
     * Insere uma nova entrada na tabela de status
     *
     * @param mobileHWSt
     * @param persistencia
     * @return true se inseriu na tabela.
     * @throws Exception
     */
    public boolean inserirStatus(MobileHWSt mobileHWSt, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO MobileHWSt \n"
                    + " VALUES (?, ?, (SELECT ISNULL(MAX(Sequencia), 0) + 1 Sequencia FROM MobileHWSt WHERE IMEI = ? AND CodEquip = ?), ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(mobileHWSt.getIMEI());
            consulta.setString(mobileHWSt.getCodEquip());
            consulta.setString(mobileHWSt.getIMEI());
            consulta.setString(mobileHWSt.getCodEquip());
            consulta.setString(mobileHWSt.getLatitude());
            consulta.setString(mobileHWSt.getLongitude());
            consulta.setString(mobileHWSt.getSituacao());
            consulta.setString(mobileHWSt.getOperador());
            consulta.setString(mobileHWSt.getData());
            consulta.setString(mobileHWSt.getHora());

            String erro = "Tentativas excedidas";
            int retorno = 0;
            for (int i = 0; i < 20; i++) {
                try {
                    retorno = consulta.insert();
                    if (retorno > 0) {
                        break;
                    }
                } catch (Exception ex) {
                    erro = ex.getMessage();
                }
            }

            consulta.close();
            if (retorno > 0) {
                return true;
            }
            throw new Exception(erro);
        } catch (Exception e) {
            throw new Exception("MobileHWStDao.inserirStatus - " + e.getMessage() + "\r\n"
                    + " INSERT INTO MobileHWSt \n"
                    + " VALUES (" + mobileHWSt.getIMEI() + ", " + mobileHWSt.getCodEquip() + ", "
                    + "(SELECT ISNULL(MAX(Sequencia), 0) + 1 Sequencia FROM MobileHWSt WHERE IMEI = " + mobileHWSt.getIMEI()
                    + " AND CodEquip = " + mobileHWSt.getCodEquip() + "), " + mobileHWSt.getLatitude() + ", " + mobileHWSt.getLongitude() + ", "
                    + mobileHWSt.getSituacao() + ", " + mobileHWSt.getOperador() + ", " + mobileHWSt.getData() + ", " + mobileHWSt.getHora() + ") ");
        }
    }
}

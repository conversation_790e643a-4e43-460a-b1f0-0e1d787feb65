/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileHard;

/**
 *
 * <AUTHOR>
 */
public class MobileHardDao {

    /**
     * Busca as configurações do cofre pelo IMEI do tablet
     *
     * @param imei
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public MobileHard obterConfiguracao(String imei, Persistencia persistencia) throws Exception {
        try {
            MobileHard retorno = null;
            String sql = " SELECT * FROM MobileHard WHERE IMEI = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(imei);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new MobileHard();
                retorno.setIMEI(consulta.getString("IMEI"));
                retorno.setParametro(consulta.getString("Parametro"));
                retorno.setCodEquip(consulta.getString("CodEquip"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setCodFil(consulta.getString("CodFil"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getString("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("MobileHardDao.obterConfiguracao - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM MobileHard WHERE IMEI = " + imei);
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobilePTT;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
/**
 * Classe para inserção de valores na tabela MobilePTT onde se grava os valores
 * de comunicação entre os smartphones.
 */
public class MobilePTTDao {

    /**
     * Código de status: Erro: 0 Enviado: 1 Recebido: 2 Escutado: 3.
     */
    /**
     * Busca o último número de sequencia do MobilePTT
     *
     * @param persistencia Conexão com o banco de dados
     * @return Próximo número de sequência
     * @throws Exception
     */
    public BigDecimal MaxSequencia(Persistencia persistencia) throws Exception {

        String sql = "SELECT ISNULL( MAX(sequencia),1) 'Sequencia' FROM MobilePTT";
        BigDecimal retorno = new BigDecimal("1");
        Consulta consulta;

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                try {
                    retorno = new BigDecimal(consulta.getString("Sequencia"));
                    retorno = retorno.add(BigDecimal.ONE);
                    //retorno = retorno.add(new BigDecimal(BigInteger.ONE));
                } catch (Exception e) {
                    e.printStackTrace();
                    retorno = new BigDecimal("1");
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha na sequencia de MobilePTT: " + e.getMessage());
        }
    }

    /**
     * Insere os os dados de envio de áudio
     *
     * @param mobilePTT Objeto PTT mobile a ser inserido
     * @param persistencia Conexão com o banco dados
     * @return Número de sequência de áudio
     * @throws Exception
     */
    public BigDecimal insereMobilePTT(MobilePTT mobilePTT, Persistencia persistencia) throws Exception {
        BigDecimal retorno = null;

        String sql = "INSERT INTO mobileptt(Sequencia, Data, Hora, Paramet, CodPessoa, "
                + "CodpessoaDst, GrupoDst, Recebido, CodPessoaRec, SeqChamada ) "
                + "VALUES  (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(mobilePTT.getSequencia());
            consulta.setString(mobilePTT.getData());
            consulta.setString(mobilePTT.getHora());
            consulta.setString(mobilePTT.getParamet());
            consulta.setBigDecimal(mobilePTT.getCodPessoa());
            consulta.setBigDecimal(mobilePTT.getCodpessoaDest());
            consulta.setBigDecimal(mobilePTT.getGrupoDest());
            consulta.setInt(mobilePTT.getRecebido());
            consulta.setBigDecimal(mobilePTT.getCodPessoaRec());
            consulta.setBigDecimal(mobilePTT.getSeqChamada());

            consulta.insert();
            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("Erro ao inserir na tabela MobilePTT:" + e.getMessage());
        }

    }

    /**
     * Seleciona os valores todos os dados de áudio já enviado pelo remetente
     *
     * @param codpessoa Código do rementente (Campo: CodPessoa)
     * @param persistencia Conexão como banco de dados
     * @return Lista com todos os dados que já foram enviados pelo remetente de
     * um áudio
     * @throws Exception
     */
    public List<MobilePTT> selecionaPorRemetente(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<MobilePTT> retorno = new ArrayList<>();
        MobilePTT mobilePTT;

        String sql = "SELECT * FROM mobileptt "
                + "WHERE codpessoa = ?";

        Consulta consulta;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
            consulta.select();

            while (consulta.Proximo()) {
                mobilePTT = new MobilePTT();

                mobilePTT.setSequencia(consulta.getBigDecimal("Sequencia"));
                mobilePTT.setData(consulta.getString("Data"));
                mobilePTT.setHora(consulta.getString("Hora"));
                mobilePTT.setParamet(consulta.getString("Paramet"));
                mobilePTT.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobilePTT.setCodpessoaDest(consulta.getBigDecimal("CodPessoaDest"));
                mobilePTT.setGrupoDest(consulta.getBigDecimal("GrupoDest"));
                mobilePTT.setRecebido(consulta.getInt("Recebido"));
                mobilePTT.setCodPessoaRec(consulta.getBigDecimal("CodPessoaRec"));
                mobilePTT.setSeqChamada(consulta.getBigDecimal("SeqChamada"));

                retorno.add(mobilePTT);
            }

        } catch (Exception e) {
            throw new Exception("Erro ao selecionar MobilePTT: " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Seleciona todos os dados já recebidos por um usuário
     *
     * @param codpessoadest Código da pessoa destinatária
     * @param persistencia Conexão com o banco de dados
     * @return Lista ddos dados da tabela MobilePTT
     * @throws Exception
     */
    public List<MobilePTT> selecionaPorDestinatario(BigDecimal codpessoadest, Persistencia persistencia) throws Exception {
        List<MobilePTT> retorno = new ArrayList<>();
        MobilePTT mobilePTT;

        String sql = "SELECT * FROM mobileptt "
                + "WHERE codpessoadest = ?";

        Consulta consulta;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoadest);
            consulta.select();

            while (consulta.Proximo()) {
                mobilePTT = new MobilePTT();

                mobilePTT.setSequencia(consulta.getBigDecimal("Sequencia"));
                mobilePTT.setData(consulta.getString("Data"));
                mobilePTT.setHora(consulta.getString("Hora"));
                mobilePTT.setParamet(consulta.getString("Paramet"));
                mobilePTT.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobilePTT.setCodpessoaDest(consulta.getBigDecimal("CodPessoaDest"));
                mobilePTT.setGrupoDest(consulta.getBigDecimal("GrupoDest"));
                mobilePTT.setRecebido(consulta.getInt("Recebido"));
                mobilePTT.setCodPessoaRec(consulta.getBigDecimal("CodPessoaRec"));
                mobilePTT.setSeqChamada(consulta.getBigDecimal("SeqChamada"));

                retorno.add(mobilePTT);
            }

        } catch (Exception e) {
            throw new Exception("Erro ao selecionar MobilePTT: " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Verifica se existe áudio para o usuário
     *
     * @param codpessoadest Código da pessoa destinada
     * @param persistencia Conexão com o banco
     * @return Retorna true se existe áudio
     * @throws Exception
     */
    public boolean verificaAudioDest(BigDecimal codpessoadest, Persistencia persistencia) throws Exception {
        boolean retorno = false;
        MobilePTT mobilePTT;
        List<MobilePTT> list = new ArrayList<>();
        String sql = "SELECT * FROM mobilePTT "
                + "WHERE codpessoadest = ? AND status = 1";

        Consulta consulta;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoadest);
            consulta.select();

            while (consulta.Proximo()) {
                mobilePTT = new MobilePTT();

                mobilePTT.setSequencia(consulta.getBigDecimal("Sequencia"));
                mobilePTT.setData(consulta.getString("Data"));
                mobilePTT.setHora(consulta.getString("Hora"));
                mobilePTT.setParamet(consulta.getString("Paramet"));
                mobilePTT.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobilePTT.setCodpessoaDest(consulta.getBigDecimal("CodPessoaDest"));
                mobilePTT.setGrupoDest(consulta.getBigDecimal("GrupoDest"));
                mobilePTT.setRecebido(consulta.getInt("Recebido"));
                mobilePTT.setCodPessoaRec(consulta.getBigDecimal("CodPessoaRec"));
                mobilePTT.setSeqChamada(consulta.getBigDecimal("SeqChamada"));

                list.add(mobilePTT);
            }

            if (list.isEmpty()) {
                retorno = false;
            } else {
                retorno = true;
            }
        } catch (Exception e) {
            throw new Exception("Falha ao verificar se existe audio: " + e.getMessage());
        }
        return retorno;
    }

    public MobilePTT verificaStatus(BigDecimal sequencia, Persistencia persistencia) throws Exception {

        MobilePTT mobilePTT;
        Consulta consulta;

        String sql = "SELECT sequencia, recebido FROM mobileptt"
                + "WHERE sequencia = ?";
        try {
            mobilePTT = new MobilePTT();

            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);

            mobilePTT.setSequencia(consulta.getBigDecimal("sequencia"));
            mobilePTT.setRecebido(consulta.getInt("recebido"));

        } catch (Exception e) {
            throw new Exception("Falha ao adquirir status: " + e.getMessage());
        }
        return mobilePTT;
    }

    public List<MobilePTT> verificaMsgs(BigDecimal codpessoa, Persistencia persistenciaUsuario, Persistencia persistenciaGeral) throws Exception {
        List<MobilePTT> retorno = new ArrayList<>();
        MobilePTT mobilePTT;
        Consulta consultaLocal;
        Consulta consultaGeral;

        String sqlLocal = "SELECT CodPessoaWeb, Nome FROM Pessoa WHERE Codigo = ?";
        try {
            consultaLocal = new Consulta(sqlLocal, persistenciaUsuario);
            consultaLocal.setBigDecimal(codpessoa);
            consultaLocal.select();

            mobilePTT = new MobilePTT();

            while (consultaLocal.Proximo()) {
                mobilePTT.setNome(consultaLocal.getString("Nome"));
                mobilePTT.setCodpessoaDest(new BigDecimal(consultaLocal.getString("CodPessoaWeb")));
            }
            consultaLocal.Close();

            String sqlGeral = "SELECT * FROM mobileptt "
                    + "WHERE codpessoadst = ? AND recebido <> 0";

            consultaGeral = new Consulta(sqlGeral, persistenciaGeral);
            consultaGeral.setBigDecimal(mobilePTT.getCodpessoaDest());
            consultaGeral.select();

            while (consultaGeral.Proximo()) {
                mobilePTT.setSequencia(consultaGeral.getBigDecimal("Sequencia"));
                mobilePTT.setData(consultaGeral.getString("Data"));
                mobilePTT.setHora(consultaGeral.getString("Hora"));
                mobilePTT.setParamet(consultaGeral.getString("Paramet"));
                mobilePTT.setCodPessoa(consultaGeral.getBigDecimal("CodPessoa"));
                //mobilePTT.setCodpessoaDest(consultaGeral.getBigDecimal("CodPessoaDest"));
                mobilePTT.setGrupoDest(consultaGeral.getBigDecimal("GrupoDest"));
                mobilePTT.setRecebido(consultaGeral.getInt("Recebido"));
                mobilePTT.setCodPessoaRec(consultaGeral.getBigDecimal("CodPessoaRec"));
                mobilePTT.setSeqChamada(consultaGeral.getBigDecimal("SeqChamada"));

                retorno.add(mobilePTT);
            }

        } catch (Exception e) {

        }

        return retorno;
    }

    /**
     * Alta o registro para recebido
     *
     * @param sequencia sequencia do registro
     * @param persistencia conexao com o banco de dados
     * @throws Exception
     */
    public void alterarParaRecebido(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE mobileptt SET recebido = 1 WHERE sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia.toPlainString());
            consulta.update();
            consulta.Close();
        } catch (Exception ex) {
            throw new Exception("ocorreu um erro: " + ex.getMessage());
        }
    }

    /**
     * Recupera lista de mensagens pendentes
     *
     * @param codPessoaDst codigo pessoa destino
     * @param persistenciaGeral conexao com o banco geral
     * @return lista contendo mensagens pendetes
     * @throws Exception
     */
    public List<MobilePTT> listaMensagensPendentes(BigDecimal codPessoaDst, Persistencia persistenciaGeral) throws Exception {
        List<MobilePTT> mensagens = new ArrayList<>();
        try {
            String sql = "SELECT * FROM mobileptt WHERE codpessoadst = ? AND recebido = 0";

            Consulta consulta = new Consulta(sql, persistenciaGeral);
            consulta.setBigDecimal(codPessoaDst);
            consulta.select();

            MobilePTT mobilePTT = null;
            while (consulta.Proximo()) {
                mobilePTT = new MobilePTT();
                mobilePTT.setSequencia(consulta.getBigDecimal("sequencia"));
                mobilePTT.setData(consulta.getString("data"));
                mobilePTT.setHora(consulta.getString("hora"));
                mobilePTT.setParamet(consulta.getString("paramet"));
                mobilePTT.setCodPessoa(consulta.getBigDecimal("codpessoa"));
                mobilePTT.setCodpessoaDest(consulta.getBigDecimal("codpessoadst"));
                mobilePTT.setGrupoDest(consulta.getBigDecimal("grupodst"));
                mobilePTT.setRecebido(consulta.getInt("recebido"));
                mobilePTT.setCodPessoaRec(consulta.getBigDecimal("codpessoarec"));
                mobilePTT.setSeqChamada(consulta.getBigDecimal("seqchamada"));
                mensagens.add(mobilePTT);
            }
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return mensagens;
    }

}

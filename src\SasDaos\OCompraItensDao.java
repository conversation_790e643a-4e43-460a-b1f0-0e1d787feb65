/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CCusto;
import SasBeans.ComprasRec;
import SasBeans.Filiais;
import SasBeans.OCompra;
import SasBeans.OCompraItens;
import SasBeans.Produtos;
import SasBeans.TiposTribut;
import SasBeansCompostas.RegistroSaidaNotaFiscal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class OCompraItensDao {

    public List<RegistroSaidaNotaFiscal> listaFiscalEntrada(String dtInicio, String dtFim, String codFil, Persistencia persistenciaEntrada) throws Exception {
        try {
            List<RegistroSaidaNotaFiscal> retornoEntrada = new ArrayList<>();
            String sql = "Select 'ENTRADA' Tipo, CCusto.CCusto, CCusto.Descricao CCustoDesc, Filiais.CNPJ, Filiais.UF,Filiais.InscEst,"
                    + "  Produtos.CFOPEnt,OCompra.Data, Produtos.CFOP, Filiais.Descricao,  Sum(OCompraItens.Valor+OCompraItens.IPI) Valor,"
                    + " Sum((OCompraItens.Valor+OCompraItens.IPI)*(TiposTribut.Indice/100)) Imposto,\n"
                    + "Case when TiposTribut.ClasseFiscal = 1 then Sum((OCompraItens.Valor+OCompraItens.IPI)*(TiposTribut.Indice/100)) else 0 end As CredICMS, \n"
                    + " Sum(OCompraItens.IPI) IPI, Sum(OCompraItens.PIS) PIS, Sum(OCompraItens.COFINS) COFINS "
                    + "from OCompraItens \n"
                    + "left join OCompra on OCompra.Sequencia = OCOmpraItens.Sequencia \n"
                    + "left join TiposTribut on TiposTribut.Codigo = OCompraItens.TipoTribut \n"
                    + "left join Produtos on Produtos.Codigo = OCompraItens.CodProd \n"
                    + "left join CCusto on CCusto.CCusto = OCompra.CCusto \n"
                    + "left join ComprasRec on ComprasRec.Ocompra = OCompra.Sequencia\n"
                    + "left join Filiais on Filiais.CodFil = OCompra.CodFil	\n"
                    + " where ComprasRec.DtNF >= ? \n"
                    + "   and ComprasRec.DtNF <= ? \n"
                    + " and OCompra.CodFil = ? \n"
                    + "   and OCompra.Situacao = 'OK' "
                    + "   and TiposTribut.ClasseFiscal = 1 \n"
                    + "Group by CCusto.CCusto,Filiais.CNPJ,OCompra.Data, Filiais.Descricao , CCusto.Descricao, Filiais.UF,Filiais.InscEst,  Produtos.CFOP, Produtos.CFOPEnt, TiposTribut.ClasseFiscal";

            Consulta consulta = new Consulta(sql, persistenciaEntrada);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codFil);
            consulta.select();

            RegistroSaidaNotaFiscal regEntrada;
            OCompraItens ocompraItens;
            OCompra ocompra;
            TiposTribut tiposTribut;
            Produtos produtos;
            CCusto ccusto;
            ComprasRec comprasrec;
            Filiais filiais;

            while (consulta.Proximo()) {
                regEntrada = new RegistroSaidaNotaFiscal();
                ocompraItens = new OCompraItens();
                ocompra = new OCompra();
                tiposTribut = new TiposTribut();
                produtos = new Produtos();
                ccusto = new CCusto();
                comprasrec = new ComprasRec();
                filiais = new Filiais();

                ccusto.setCCusto(consulta.getString("CCusto"));
                ccusto.setDescricao(consulta.getString("CCustoDesc"));

                filiais.setCNPJ(consulta.getString("CNPJ"));
                filiais.setUF(consulta.getString("UF"));
                filiais.setInscEst(consulta.getString("InscEst"));
                filiais.setDescricao(consulta.getString("Descricao"));

                produtos.setCFOPEnt(consulta.getString("CFOPEnt"));
                produtos.setCFOP(consulta.getString("CFOP"));

                ocompra.setData(consulta.getString(("Data")));

                ocompraItens.setValor(consulta.getString("Valor"));
                ocompraItens.setImposto(consulta.getString("Imposto"));
                ocompraItens.setIPI(consulta.getString("IPI"));
                ocompraItens.setPIS(consulta.getString("PIS"));
                ocompraItens.setCOFINS(consulta.getString("COFINS"));

                tiposTribut.setClasseFiscal(consulta.getInt("CredICMS"));

                regEntrada.setCcusto(ccusto);
                regEntrada.setFiliais(filiais);
                regEntrada.setProdutos(produtos);
                regEntrada.setOcompraitens(ocompraItens);
                regEntrada.setOcompra(ocompra);

                retornoEntrada.add(regEntrada);

            }

            consulta.Close();
            return retornoEntrada;

        } catch (Exception ex) {
            throw new Exception("NFiscalDao.ListaFiscalSaida - ERRO - " + ex.getMessage() + " Select 'ENTRADA' Tipo, CCusto.CCusto, CCusto.Descricao CCustoDesc, Fornec.UF,  PRodutos.CFOP, PRodutos.CFOPEnt, Sum(OCompraItens.Valor+OCompraItens.IPI) Valor, Sum((OCompraItens.Valor+OCompraItens.IPI)*(TiposTribut.Indice/100)) Imposto,\n"
                    + "Case when TiposTribut.ClasseFiscal = 1 then Sum((OCompraItens.Valor+OCompraItens.IPI)*(TiposTribut.Indice/100)) else 0 end As CredICMS, \n"
                    + " Sum(OCompraItens.IPI) IPI, Sum(OCompraItens.PIS) PIS, Sum(OCompraItens.COFINS) COFINS from OCompraItens \n"
                    + "left join OCompra on OCompra.Sequencia = OCOmpraItens.Sequencia \n"
                    + "left join TiposTribut on TiposTribut.Codigo = OCompraItens.TipoTribut \n"
                    + "left join Produtos on Produtos.Codigo = OCompraItens.CodProd \n"
                    + "left join CCusto on CCusto.CCusto = OCompra.CCusto \n"
                    + "left join ComprasRec on ComprasRec.Ocompra = OCompra.Sequencia\n"
                    + "left join Fornec on Fornec.Codigo = OCompra.CodFornec	\n"
                    + " where ComprasRec.DtNF >= " + dtInicio + " \n"
                    + "   and ComprasRec.DtNF <= " + dtFim + " \n"
                    + "   and OCompra.Situacao = 'OK' \n"
                    + "and OCompra.CodFil = " + codFil + ""
                    + "   and TiposTribut.ClasseFiscal = 1 \n"
                    + "Group by CCusto.CCusto, CCusto.Descricao, Fornec.UF,  PRodutos.CFOP, Produtos.CFOPEnt, TiposTribut.ClasseFiscal");
        }

    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.MobileContatos;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MobileContatosDao {

    /**
     * Verifica a existencia de um contato
     *
     * @param contato
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Boolean existeContato(MobileContatos contato, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT MobileContatos.CodPessoa, MobileContatos.CodContato, MobileContatos.Situacao "
                    + " FROM MobileContatos "
                    + " WHERE codpessoa = ? and codcontato = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(contato.getCodPessoa());
            consulta.setBigDecimal(contato.getCodContato());
            consulta.select();
            MobileContatos mobilecontatos = null;
            while (consulta.Proximo()) {
                mobilecontatos = new MobileContatos();
                mobilecontatos.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                mobilecontatos.setCodContato(consulta.getBigDecimal("CodContato"));
                mobilecontatos.setSituacao(consulta.getInt("Situacao"));
                mobilecontatos.setNome(consulta.getString("Nome"));
            }
            consulta.Close();

            return null != mobilecontatos;

        } catch (Exception e) {
            throw new Exception("Falha ao buscar algum contato: " + e.getMessage());
        }
    }

    /**
     * Insere o contato do usuário
     *
     * @param contatos Objeto contato mobile
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void insereMobileContatos(MobileContatos contatos, Persistencia persistencia) throws Exception {
        String sql = "INSERT INTO mobilecontatos (CodPessoa, CodContato,"
                + " Situacao, Operador, Dt_Alter, Hr_Alter) "
                + " VALUES (?, ?, ?, ?, ?, ?);";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(contatos.getCodPessoa());
            consulta.setBigDecimal(contatos.getCodContato());
            consulta.setInt(contatos.getSituacao());
            consulta.setString(contatos.getOperador());
            consulta.setString(contatos.getData());
            consulta.setString(contatos.getHora());

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha ao inserir contato: " + e.getMessage());
        }

    }

    /**
     * Realiza a atualização do contato
     *
     * @param contatos Objeto contato mobile
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void atualizaContato(MobileContatos contatos, Persistencia persistencia) throws Exception {

        String sql = "UPDATE mobilecontatos SET situacao = ?, "
                + " operador = ?, dt_alter = ?, hr_alter = ? "
                + " where codpessoa = ? and codcontato = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setInt(contatos.getSituacao());
            consulta.setString(contatos.getOperador());
            consulta.setString(contatos.getData());
            consulta.setString(contatos.getHora());
            consulta.setBigDecimal(contatos.getCodPessoa());
            consulta.setBigDecimal(contatos.getCodContato());

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha ao atualizar contato: " + e.getMessage());
        }
    }

    /**
     * Exclui o contato para o usuário
     *
     * @param codpessoa Código da pessoa remetente
     * @param codpessoaDest Código da pessoa destinatário
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void deletaContato(BigDecimal codpessoa, BigDecimal codpessoaDest, Persistencia persistencia) throws Exception {
        String Sql = "DELETE FROM mobilecontatos "
                + " WHERE codpessoa = ? AND codcontato = ? ";

        try {
            Consulta consulta = new Consulta(Sql, persistencia);

            consulta.setBigDecimal(codpessoa);
            consulta.setBigDecimal(codpessoaDest);

            consulta.delete();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha ao deletar contato: " + e.getMessage());
        }
    }

    /**
     * Busca a última visualização dos usuário no mobile
     *
     * @param codpessoa Código da pessoa
     * @param persistenciaUsuario Conexão com o banco de dados do usuário
     * @param persistenciaGeral Conexão com o banco da base central
     * @return Retorna a hora e data da última visualização
     * @throws Exception
     */
    public String vistoPorUltimo(BigDecimal codpessoa, Persistencia persistenciaUsuario, Persistencia persistenciaGeral) throws Exception {
        String hora = null;
        String data = null;
        LocalDate date = null;
        String retorno = null;
        BigDecimal cod = null;

        String sqlGeral = "SELECT codpessoaweb FROM pessoa "
                + "WHERE codigo = ?";

        String sqlLocal = "SELECT hora, data FROM rastrear "
                + "WHERE matr = ?";
        Consulta consultaGeral;
        Consulta consultaLocal;

        try {

            consultaGeral = new Consulta(sqlGeral, persistenciaGeral);
            consultaGeral.setBigDecimal(codpessoa);
            consultaGeral.select();

            while (consultaGeral.Proximo()) {
                cod = new BigDecimal(consultaGeral.getString("codpessoaweb"));
            }

            consultaGeral.Close();

            consultaLocal = new Consulta(sqlLocal, persistenciaUsuario);
            consultaLocal.setBigDecimal(cod);
            consultaLocal.select();

            while (consultaLocal.Proximo()) {
                hora = consultaLocal.getString("hora");
                date = consultaLocal.getLocalDate("data");

            }

            consultaLocal.Close();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            retorno = date.format(formatter) + "," + hora;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar hora: " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Traz a lista de contatos da pessoa que fez a requisição
     *
     * @param codPessoaWeb Código da pessoa
     * @param persistenciaLocal Persistência do usuário
     * @param persistenciaCentral Persistência Central
     * @return Lista de contatos
     * @throws Exception
     */
    public List<MobileContatos> listaDeContatos(BigDecimal codPessoaWeb, Persistencia persistenciaCentral) throws Exception {
        List<MobileContatos> retorno = new ArrayList<>();
        MobileContatos contatos;
        Consulta consultaCentral;
        try {

            String sqlCentral = "SELECT MobileContatos.CodPessoa, MobileContatos.CodContato, MobileContatos.Situacao,"
                    + " Pessoa.Nome FROM MobileContatos "
                    + " Inner Join Pessoa  on Pessoa.Codigo = MobileContatos.CodContato"
                    + " WHERE codpessoa = ? and MobileContatos.situacao = 1";
            consultaCentral = new Consulta(sqlCentral, persistenciaCentral);
            consultaCentral.setBigDecimal(codPessoaWeb);
            consultaCentral.select();
            while (consultaCentral.Proximo()) {
                contatos = new MobileContatos();
                contatos.setCodPessoa(consultaCentral.getBigDecimal("CodPessoa"));
                contatos.setCodContato(consultaCentral.getBigDecimal("CodContato"));
                contatos.setSituacao(consultaCentral.getInt("Situacao"));
                contatos.setNome(consultaCentral.getString("Nome"));

                retorno.add(contatos);
            }
            consultaCentral.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list contacts: " + e.getMessage());
        }

        return retorno;
    }

    /**
     * Lista os contatos de uma pessoa e salva as empresas dos contatos no campo
     * 'operador'
     *
     * @param codPessoaWeb
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<MobileContatos> listaDeContatosSatMobWeb(BigDecimal codPessoaWeb, Persistencia persistencia) throws Exception {
        List<MobileContatos> retorno = new ArrayList<>();
        try {
            String sql = "SELECT MobileContatos.CodPessoa, MobileContatos.CodContato, MobileContatos.Situacao,"
                    + " Pessoa.Nome FROM MobileContatos "
                    + " Inner Join Pessoa  on Pessoa.Codigo = MobileContatos.CodContato"
                    + " WHERE codpessoa = ?  and MobileContatos.situacao = 1 order by pessoa.Nome";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoaWeb);
            consulta.select();

            String sql2 = "DECLARE @listStr VARCHAR(MAX) "
                    + " SELECT @listStr = COALESCE(@listStr+', ' ,'') + pessoalogin.bancodados "
                    + " FROM pessoalogin where codigo = ? "
                    + " SELECT @listStr empresas";
            Consulta consulta2;
            MobileContatos contatos;
            while (consulta.Proximo()) {
                contatos = new MobileContatos();
                contatos.setCodPessoa(consulta.getBigDecimal("CodPessoa"));
                contatos.setCodContato(consulta.getBigDecimal("CodContato"));
                contatos.setSituacao(consulta.getInt("Situacao"));
                contatos.setNome(consulta.getString("Nome"));

                consulta2 = new Consulta(sql2, persistencia);
                consulta2.setBigDecimal(contatos.getCodContato());
                consulta2.select();
                while (consulta2.Proximo()) {
                    contatos.setOperador(consulta2.getString("empresas"));
                }
                retorno.add(contatos);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list contacts: " + e.getMessage());
        }
        return retorno;
    }

    public List<MobileContatos> BuscaContatosMobile(String query, Persistencia persistencia) throws Exception {
        List<MobileContatos> retorno = new ArrayList<>();
        try {
            String sql = "select top 20 * from pessoa where cpf like ? or nome like ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(query + "%");
            consulta.setString("%" + query + "%");
            consulta.select();
            MobileContatos contato;
            while (consulta.Proximo()) {
                contato = new MobileContatos();
                contato.setNome(consulta.getString("nome"));
                contato.setCodContato(consulta.getBigDecimal("codigo"));
                retorno.add(contato);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("falha ao buscar pessoas: " + e.getMessage());
        }
    }
}

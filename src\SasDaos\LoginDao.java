package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.RHPonto;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.TesAutomatiza;
import SasBeansCompostas.Login;
import SasBeansCompostas.LoginRota;
import SasBeansCompostas.UsuarioSatMobWeb;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoginDao {

    /**
     * Login inicial do mobile, trazendo as permissões de acesso
     *
     * @param matricula - Codigo pessoa (usuário)
     * @param persistencia - Classe de conexão ao cliente
     * @return - Retorna lista de permissões
     * @throws Exception - pode gerar exception
     */
    public static List<Login> LoginMobilePonto(String matricula, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer, "
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, "
                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, EscalaMatr.Rota RotaM, "
                    + " EscalaCodPessoa.Rota RotaP "
                    + " from  pessoa "
                    + " left join funcion on funcion.matr=pessoa.matr "
                    + " left join Escala EscalaMatr on EscalaMatr.Data     = Cast(GetDate() as Date) "
                    + "        and EscalaMatr.MatrChe = Funcion.Matr "
                    + " left join Escala EscalaCodPessoa on EscalaCodPessoa.Data = Cast(GetDate() as Date) "
                    + "             and EscalaCodPessoa.CodPessoaSup = Pessoa.Codigo "
                    + " where pessoa.Matr=? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                saspw.setSituacao(consulta.getString("Situacao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setRotaM(consulta.getString("rotam"));
                login.setRotaP(consulta.getString("rotap"));
                list_login.add(login);
            }
            if (list_login.isEmpty()) {
                sql = "select Codigo, Nome, PwWeb from pessoa"
                        + " where pessoa.Matr=? ";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(matricula);
                consulta.select();
                while (consulta.Proximo()) {
                    Pessoa pessoa = new Pessoa();
                    Funcion funcion = new Funcion();
                    Login login = new Login();
                    pessoa.setPWWeb(consulta.getString("PwWeb"));
                    pessoa.setCodigo(consulta.getString("codigo"));
                    pessoa.setNome(consulta.getString("Nome"));
                    funcion.setSituacao("A");
                    login.setPessoa(pessoa);
                    login.setFuncion(funcion);
                    login.setSaspw(null);
                    login.setSaspwac(null);
                    list_login.add(login);
                }
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     *
     * @param matricula - Codigo pessoa (usuário)
     * @param pwweb
     * @param persistencia - Classe de conexão ao cliente
     * @return - Retorna lista de permissões
     * @throws Exception - pode gerar exception
     */
    public static boolean ValidarMatricula(String matricula, String pwweb, Persistencia persistencia) throws Exception {
        try {
            String sql = "select \n"
                    + "     pessoa.matr \n"
                    + " from \n"
                    + "     pessoa \n"
                    + " left join \n"
                    + "     funcion on funcion.matr = pessoa.matr \n"
                    + " where \n"
                    + "     pessoa.Matr = ? AND Pessoa.PWWeb = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.setString(pwweb);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LoginDao.ValidarMatricula - " + e.getMessage() + "\r\n"
                    + "select \n"
                    + "     pessoa.matr \n"
                    + " from \n"
                    + "     pessoa \n"
                    + " left join \n"
                    + "     funcion on funcion.matr = pessoa.matr \n"
                    + " where \n"
                    + "     pessoa.Matr = " + matricula + " AND Pessoa.PWWeb = " + pwweb + " \n");
        }
    }

    /**
     * Verifica se o usuário tem permissão de edição de ronda de vigilância
     *
     * @param codpessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static boolean isSupervisorRondas(String codpessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            boolean retorno = false;
            Consulta consulta;
            /*String sql = " select * from saspwac "
                    + " Left Join SasPW on SASPW.nome = saspwac.nome "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa "
                    + " where pessoa.codigo = ? and saspwac.sistema = 302001 ";*/

            // Insert de Novo Código de validação de Inspetor
            // Quem possuir o código 300009 e não possuir o 302001, o sistema se encarregará de inserir automaticamente
            try {
                sql = "INSERT INTO SASPWAC \n"
                        + " SELECT SASPWAc.Nome, 302001, SASPWAc.Codfil, SASPWAc.codigo, SASPWAc.Inclusao, SASPWAc.Alteracao, SASPWAc.Exclusao, SASPWAc.Operador, SASPWAc.Dt_Alter, SASPWAc.Hr_Alter \n"
                        + " FROM SASPWAc \n"
                        + " LEFT JOIN SASPWAc B\n"
                        + "    ON SASPWAc.Nome = B.Nome\n"
                        + "   AND B.Sistema    = '302001'\n"
                        + " WHERE SASPWAc.Sistema = '300009'\n"
                        + " AND   B.Nome IS NULL;";

                consulta = new Consulta(sql, persistencia);
                consulta.insert();
            } catch (Exception ex3) {
            }

            // Consulta para verificar se Supervisor estava Top 1 29/09/2022
            sql = "SELECT top 1 \n"
                    + " saspwac.sistema\n"
                    + " FROM Pessoa \n"
                    + " LEFT JOIN Pessoa PessoaLista\n"
                    + "   ON Pessoa.CPF = PessoaLista.CPF\n"
                    + "  AND PessoaLista.CPF IS NOT NULL \n"
                    + "  AND PessoaLista.CPF <> ''\n"
                    + " Left Join Funcion on Funcion.matr = Pessoa.matr " //Alteracao Carlos 01/08/2022
                    + " Left Join PstServ on PstServ.SupervDiu = Pessoa.Codigo \n"
                    + "                  and PstServ.CodFil = Funcion.CodFil \n"                    
                    + " JOIN saspw\n"
                    + "   ON COALESCE(PessoaLista.Codigo, Pessoa.Codigo) = saspw.CodPessoa\n"
                    + " JOIN saspwac\n"
                    + "   ON saspw.Nome = saspwac.Nome\n"
                    + " WHERE Pessoa.Codigo = ?\n"
                    + " AND   (saspwac.sistema = 302001)";// or  PstServ.SupervDiu = Pessoa.Codigo)"; //Alteracao para permitir supervisor - Carlos

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codpessoa);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LoginDao.isSupervisorRondas - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public static boolean isInspecao(String codpessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            boolean retorno = false;
            Consulta consulta;
            /*String sql = " select * from saspwac "
                    + " Left Join SasPW on SASPW.nome = saspwac.nome "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa "
                    + " where pessoa.codigo = ? and saspwac.sistema = 302001 ";*/

            // Insert de Novo Código de validação de Inspetor
            // Quem possuir o código 300009 e não possuir o 302001, o sistema se encarregará de inserir automaticamente

            // Consulta para verificar se Supervisor
            sql = "SELECT top 1 \n"
                    + " Pessoa.matr \n"
                    + " FROM Pessoa \n"
                    + " LEFT JOIN Pessoa PessoaLista\n"
                    + "   ON Pessoa.CPF = PessoaLista.CPF\n"
                    + "  AND PessoaLista.CPF IS NOT NULL \n"
                    + "  AND PessoaLista.CPF <> ''\n"
                    + " Left Join Funcion on Funcion.matr = Pessoa.matr " //Alteracao Carlos 01/08/2022
                    + " Left Join PstServ on PstServ.SupervDiu = Pessoa.Codigo \n"
                    + "                  and PstServ.CodFil = Funcion.CodFil \n"                    
//                    + " JOIN saspw\n"
//                    + "   ON COALESCE(PessoaLista.Codigo, Pessoa.Codigo) = saspw.CodPessoa\n"
                    + " WHERE Pessoa.Codigo = ?\n"; //Alteracao para permitir supervisor - Carlos 01/08/2022

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codpessoa);            
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LoginDao.isSupervisorRondas - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }    
    
    public static List<Login> LoginMobilePonto(String matricula, String nomepessoa, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList<>();
            String sql = "select Top 1 ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, \n"
                    //+ "         RHPonto.DtBatida ultimaBatidaData, max(RHPonto.hora) ultimaBatidaHora, \n"
                    + " (Select top 1 DtBatida from RhPonto where Matr = Funcion.matr Order by DtCompet Desc) ultimaBatidaData, (Select top 1 Hora from RhPonto where Matr = Funcion.matr Order by Hora Desc) ultimaBatidaHora,   \n"
                    + "         PstServ.local, RHEscala.Descricao Escala,  \n"
                    + "         clientes.ende, clientes.latitude, clientes.longitude,  \n"
                    + "         pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,  \n"
                    + "         pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, cargos.descricao cargo,  \n"
                    + "         funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota RotaM,  \n"
                    + "         Rh_Horas.Secao, saspwac.Sistema \n"
                    + " from pessoa  \n"
                    + " left join funcion on funcion.matr=pessoa.matr  \n"
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala  \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil  \n"
                    + " left join cargos on cargos.cargo = funcion.cargo  \n"
                    + " left join Escala EscalaMatr on EscalaMatr.Data = ? \n"
                    + "         and (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr) \n"
                    + " Left Join Rh_Horas on Rh_Horas.Matr = Funcion.Matr  \n"
                    + "         and Rh_Horas.data = ? \n"
                    + " Left Join PstServ on PstServ.Secao = Rh_Horas.Secao  \n"
                    + "         and PstServ.Codfil = Funcion.CodFil  \n"
                    + " Left Join Clientes on Clientes.Codigo = PstServ.CodCli  \n"
                    + "         and Clientes.codFil = PstServ.CodFil  \n"
                    //+ " Left Join RHPonto on RHPonto.matr = pessoa.matr  \n"
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil  \n"
                    + "         and ctritens.contrato = pstserv.contrato  \n"
                    + "         and ctritens.tipoposto = pstserv.tipoposto  \n"
                    + " Left Join SasPW on SASPW.codpessoa = pessoa.codigo  \n"
                    + " left join saspwac on saspw.nome = saspwac.nome \n";
            if (nomepessoa.equals("")) {
                sql += " where pessoa.Matr = ? \n";
            } else {
                sql += " where SASPW.nome = ? \n";
            }
            /* Desativado em 23/03/2023 Carlos
            sql += "  and RHPonto.dtBatida = (select max(dtBatida) from RHPonto where  RHPonto.matr = pessoa.matr) \n"
                    + " group by ctritens.TipoPosto+' - '+ctritens.Descricao,  \n"
                    + "         RHPonto.DtBatida, PstServ.local, RHEscala.Descricao,  \n"
                    + "         clientes.ende, clientes.latitude, clientes.longitude,  \n"
                    + "         pessoa.matr, SubString(pessoa.nome,1,20),  \n"
                    + "         pessoa.pw, pessoa.codigo, pessoa.pwweb, cargos.descricao,  \n"
                    + "         funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota,  \n"
                    + "         Rh_Horas.Secao, saspwac.Sistema \n";
            */
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            if (nomepessoa.equals("")) {
                consulta.setString(matricula);
            } else {
                consulta.setString(nomepessoa); // Será aceito nome do usuário também
            }
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                Clientes cliente = new Clientes();
                RHPonto rhPonto = new RHPonto();
                saspw.setSituacao(consulta.getString("Situacao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setSecao(consulta.getString("Secao"));
                funcion.setCargo(consulta.getString("cargo"));
                funcion.setSexo(consulta.getString("sexo"));
                funcion.setEscala(consulta.getString("Escala"));
                /* SALVANDO TIPO DO POSTO/DESCRIÇÃO NO CAMPO ABAIXO */
                saspw.setDescricao(consulta.getString("Descricao"));

                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                cliente.setNRed(consulta.getString("local"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                cliente.setEnde(consulta.getString("ende"));
                rhPonto.setDtBatida(consulta.getString("ultimaBatidaData"));
                rhPonto.setHora(consulta.getString("ultimaBatidaHora"));
                saspwac.setSistema(consulta.getString("sistema"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setCliente(cliente);
                login.setRhPonto(rhPonto);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Consulta login para ponto WEB
     *
     * @param matricula
     * @param senha
     * @param codFil
     * @param dataAtual
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Login> LoginWebPonto(String matricula, String senha, String codFil, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList<>();
            String sql = "SELECT\n"
                    + " funcion.Secao Secao2,ctritens.TipoPosto+' - '+ctritens.Descricao Descricao,\n"
                    //+ " RHPonto.DtBatida ultimaBatidaData, max(RHPonto.hora) ultimaBatidaHora,\n"
                    + " (Select top 1 DtBatida from RhPonto where Matr = Funcion.matr Order by DtCompet Desc) ultimaBatidaData, (Select top 1 Hora from RhPonto where Matr = Funcion.matr Order by Hora Desc) ultimaBatidaHora, \n" 
                    + " PstServ.local, RHEscala.Descricao Escala, \n"
                    + " clientes.ende, clientes.latitude, clientes.longitude, \n"
                    + " pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer, \n"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, cargos.descricao cargo, COALESCE(cargos.descricao,pessoa.funcao) funcao,\n"
                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota RotaM, \n"
                    + " Rh_Horas.Secao, saspwac.Sistema\n"
                    + " FROM Pessoa\n"
                    + " Left join funcion  on funcion.matr=pessoa.matr \n"
                    //+ "                   and funcion.CodFil   = ?\n"
                    + "                   AND funcion.Situacao = 'A'\n"
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala \n"
                    + "                   and RHEscala.CodFil  = Funcion.CodFil \n"
                    + " left join cargos   on cargos.cargo     = funcion.cargo \n"
                    + " left join Escala EscalaMatr on EscalaMatr.Data     = ?\n"
                    + "                            and (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr)\n"
                    + " Left Join Rh_Horas on Rh_Horas.Matr = Funcion.Matr \n"
                    + "                   and Rh_Horas.data   = ?\n"
                    + " Left Join PstServ  on PstServ.Secao   = funcion.Secao\n"
                    + "                   and PstServ.Codfil  = Funcion.CodFil \n"
                    + " Left Join Clientes on Clientes.Codigo = PstServ.CodCli \n"
                    + "                   and Clientes.codFil = PstServ.CodFil \n"
                   // + " Left Join RHPonto  on RHPonto.matr       = pessoa.matr\n"
                   // + "                   and RHPonto.dtBatida   = (select max(dtBatida) from RHPonto where RHPonto.matr = pessoa.matr)\n"
                    + " Left Join ctritens on  ctritens.codfil   = pstserv.codfil \n"
                    + "                   and ctritens.contrato  = pstserv.contrato \n"
                    + "                   and ctritens.tipoposto = pstserv.tipoposto \n"
                    + " Left Join SasPW    on SASPW.codpessoa = pessoa.codigo \n"
                    + " left join saspwac  on saspw.nome      = saspwac.nome\n"
                    + " WHERE Pessoa.Matr  = ?\n"
                    + " AND   (Pessoa.PWWeb = ? OR Pessoa.PwPortal = ?)\n";
                    /* Desativado em 23/03/2023
                    + " group by funcion.Secao,ctritens.TipoPosto+' - '+ctritens.Descricao,\n"
                    + "          RHPonto.DtBatida, PstServ.local, RHEscala.Descricao,\n"
                    + "          clientes.ende, clientes.latitude, clientes.longitude,\n"
                    + "          pessoa.matr, SubString(pessoa.nome,1,20),\n"
                    + "          pessoa.pw, pessoa.codigo, pessoa.pwweb, cargos.descricao, COALESCE(cargos.descricao,pessoa.funcao),\n"
                    + "          funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota,\n"
                    + "          Rh_Horas.Secao, saspwac.Sistema";
            */
            Consulta consulta = new Consulta(sql, persistencia);
            //consulta.setString(codFil);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(matricula);
            consulta.setString(senha);
            consulta.setString(senha);

            consulta.select();

            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                Clientes cliente = new Clientes();
                RHPonto rhPonto = new RHPonto();
                saspw.setSituacao(consulta.getString("Situacao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setSecao(consulta.getString("Secao2"));
                funcion.setCargo(consulta.getString("cargo"));
                funcion.setSexo(consulta.getString("sexo"));
                funcion.setEscala(consulta.getString("Escala"));
                /* SALVANDO TIPO DO POSTO/DESCRIÇÃO NO CAMPO ABAIXO */
                saspw.setDescricao(consulta.getString("Descricao"));
                pessoa.setFuncao(consulta.getString("funcao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                cliente.setNRed(consulta.getString("local"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                cliente.setEnde(consulta.getString("ende"));
                rhPonto.setDtBatida(consulta.getString("ultimaBatidaData"));
                rhPonto.setHora(consulta.getString("ultimaBatidaHora"));
                saspwac.setSistema(consulta.getString("sistema"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setCliente(cliente);
                login.setRhPonto(rhPonto);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Consulta login
     *
     * @param matricula
     * @param nomepessoa
     * @param prestador
     * @param codFil
     * @param secao
     * @param dataAtual
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static List<Login> LoginMobilePonto(String matricula, String nomepessoa, boolean prestador, String codFil, String secao,
            String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList<>();
            String sql = " select Top 1 pessoa.pwportal,ctritens.TipoPosto+' - '+ctritens.Descricao Descricao, \n"
//                    + "        RHPonto.DtBatida ultimaBatidaData, max(RHPonto.hora) ultimaBatidaHora, \n"
                    + " (Select top 1 DtBatida from RhPonto where Matr = Funcion.matr Order by DtCompet Desc) ultimaBatidaData, (Select top 1 Hora from RhPonto where Matr = Funcion.matr Order by Hora Desc) ultimaBatidaHora, \n"                     
                    + "         PstServ.local, RHEscala.Descricao Escala,  \n"
                    + "         clientes.ende, clientes.latitude, clientes.longitude,  \n"
                    + "         clientes.CercaElet,  \n"
                    + "         pessoa.matr Matr, SubString(COALESCE(pessoaSenha.nome, pessoa.nome),1,20) Nome_Guer,  \n"
                    + "         COALESCE(pessoaSenha.pw, pessoa.pw) pw, COALESCE(pessoaSenha.codigo, pessoa.codigo) codigo, COALESCE(pessoaSenha.PwWeb, pessoa.pwweb) PwWeb, cargos.descricao cargo, COALESCE(pessoaSenha.funcao, pessoa.funcao) funcao, \n"
                    + "         funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota RotaM,  \n"
                    + "         Rh_Horas.Secao, saspwac.Sistema, PstServ.Contrato, funcion.Secao SecaoRef, PstServRef.Contrato ContratoRef, ClientesRef.Nred LocalRef \n"
                    + " from pessoa  \n"
                    + " left join funcion on funcion.matr=pessoa.matr  \n";
            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                sql += "                    and funcion.CodFil = ? \n";
            }
            sql += "  LEFT JOIN pessoa pessoaSenha ON funcion.Matr = pessoaSenha.Matr \n"
                    + "                              AND funcion.Nome = pessoaSenha.Nome \n"
                    + " Left Join RHEscala on  RHEscala.Codigo = Funcion.Escala  \n"
                    + "                    and RHEscala.CodFil = Funcion.CodFil  \n"
                    + " left join cargos on cargos.cargo = funcion.cargo  \n"
                    + " left join Escala EscalaMatr on EscalaMatr.Data = ? \n"
                    + "         and (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr) \n"
                    + " Left Join Rh_Horas on Rh_Horas.Matr = Funcion.Matr  \n"
                    + "         and Rh_Horas.data = ? \n"
                    + " Left Join PstServ on PstServ.Secao = ? \n"
                    + "         and PstServ.Codfil = Funcion.CodFil  \n"
                    + " Left Join PstServ PstServRef on PstServRef.Secao = Funcion.secao \n"
                    + "         and PstServRef.Codfil = Funcion.CodFil  \n"
                    + " Left Join Clientes on Clientes.Codigo = PstServ.CodCli  \n"
                    + "         and Clientes.codFil = PstServ.CodFil  \n"
                    + " Left Join Clientes ClientesRef on ClientesRef.Codigo = PstServRef.CodCli  \n"
                    + "         and Clientes.codFil = PstServRef.CodFil  \n"
                    //+ " Left Join RHPonto on RHPonto.matr = pessoa.matr \n"
                    //+" \n"
                    //+ "              and RHPonto.dtBatida = (select max(dtBatida) from RHPonto where RHPonto.matr = pessoa.matr) \n" Desativado em 23/03/2023 - Carlos
                    + " Left Join ctritens on  ctritens.codfil = pstserv.codfil  \n"
                    + "         and ctritens.contrato = pstserv.contrato  \n"
                    + "         and ctritens.tipoposto = pstserv.tipoposto  \n"
                    + " Left Join SasPW on SASPW.codpessoa = pessoa.codigo  \n"
                    + " left join saspwac on saspw.nome = saspwac.nome \n";
            if (prestador) {
                sql += " where pessoa.codigo = ? \n";
            } else if (nomepessoa.equals("")) {
                sql += " where pessoa.Matr = ? \n";
            } else {
                sql += " where SASPW.nome = ? \n";
            }
        // Desativado em 23/03/2023
        //    sql += " GROUP BY pessoa.pwportal,ctritens.TipoPosto+' - '+ctritens.Descricao, RHPonto.DtBatida, PstServ.local, RHEscala.Descricao,  \n"
        //            + "         clientes.ende, clientes.latitude, clientes.longitude,  \n"
        //            + "         pessoa.matr, SubString(COALESCE(pessoaSenha.nome, pessoa.nome),1,20),  \n"
        //            + "         COALESCE(pessoaSenha.pw, pessoa.pw), COALESCE(pessoaSenha.codigo, pessoa.codigo), COALESCE(pessoaSenha.PwWeb, pessoa.pwweb), cargos.descricao, COALESCE(pessoaSenha.funcao, pessoa.funcao), \n"
        //            + "         funcion.CodFil, funcion.Situacao, Funcion.Matr, funcion.sexo, EscalaMatr.Rota,  \n"
        //            + "         Rh_Horas.Secao, saspwac.Sistema, PstServ.Contrato, funcion.Secao,PstServRef.Contrato, ClientesRef.Nred \n";
            
            Consulta consulta = new Consulta(sql, persistencia);
            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                consulta.setString(codFil);
            }
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(secao);
            if (prestador) {
                consulta.setString(matricula);
            } else if (nomepessoa.equals("")) {
                consulta.setString(matricula);
            } else {
                consulta.setString(nomepessoa);
            }
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                Clientes cliente = new Clientes();
                RHPonto rhPonto = new RHPonto();
                saspw.setSituacao(consulta.getString("Situacao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setSecao(consulta.getString("Secao"));
                funcion.setContrato(consulta.getString("Contrato"));
                funcion.setContratoRef(consulta.getString("ContratoRef"));
                funcion.setLocalRef(consulta.getString("LocalRef"));
                funcion.setSecaoRef(consulta.getString("SecaoRef"));
                funcion.setCargo(consulta.getString("cargo"));
                funcion.setSexo(consulta.getString("sexo"));
                funcion.setEscala(consulta.getString("Escala"));
                /* SALVANDO TIPO DO POSTO/DESCRIÇÃO NO CAMPO ABAIXO */
                saspw.setDescricao(consulta.getString("Descricao"));
                pessoa.setFuncao(consulta.getString("funcao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPWPortal(consulta.getString("pwportal"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                cliente.setNRed(consulta.getString("local"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                cliente.setEnde(consulta.getString("ende"));
                cliente.setCercaElet(consulta.getString("CercaElet"));
                rhPonto.setDtBatida(consulta.getString("ultimaBatidaData"));
                rhPonto.setHora(consulta.getString("ultimaBatidaHora"));
                saspwac.setSistema(consulta.getString("sistema"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setCliente(cliente);
                login.setRhPonto(rhPonto);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public static List<Login> LoginMobileGetLock(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList<>();
            String sql = " select pessoa.nome, pessoa.pwweb PwWeb, saspw.Nivelx "
                    + " from pessoa  "
                    + " Left Join SasPW on SASPW.codpessoa = pessoa.codigo  "
                    //                    + " left join saspwac on saspw.nome = saspwac.nome "
                    + " where pessoa.codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                Clientes cliente = new Clientes();
                RHPonto rhPonto = new RHPonto();
                saspw.setNivelx(consulta.getString("Nivelx"));
//                funcion.setMatr(consulta.getString("Matr"));
//                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
//                funcion.setCodFil(consulta.getString("CodFil"));
//                funcion.setSituacao(consulta.getString("Situacao"));
//                funcion.setSecao(consulta.getString("Secao"));
//                funcion.setCargo(consulta.getString("cargo"));
//                funcion.setSexo(consulta.getString("sexo"));
//                funcion.setEscala(consulta.getString("Escala"));
//                saspw.setDescricao(consulta.getString("Descricao"));
//                pessoa.setFuncao(consulta.getString("funcao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setNome(consulta.getString("Nome"));
//                pessoa.setPW(consulta.getString("pw"));
//                pessoa.setCodigo(consulta.getString("codigo"));
//                cliente.setNRed(consulta.getString("local"));
//                cliente.setLatitude(consulta.getString("latitude"));
//                cliente.setLongitude(consulta.getString("longitude"));
//                cliente.setEnde(consulta.getString("ende"));
//                rhPonto.setDtBatida(consulta.getString("ultimaBatidaData"));
//                rhPonto.setHora(consulta.getString("ultimaBatidaHora"));
//                saspwac.setSistema(consulta.getString("sistema"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setCliente(cliente);
                login.setRhPonto(rhPonto);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Obtem-se a ultima movimentação do EW
     *
     * @param codPessoa
     * @param matr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static Login ultimaMovimentacaoEW(String codPessoa, String matr, Persistencia persistencia) throws Exception {
        try {
            String sql = " select top 1 convert(varchar, data, 112) data, hora from (\n"
                    + "select top 1 dt_alter data, hr_alter hora from pessoatrajeto where codpessoa = ? order by dt_alter desc, hr_alter desc \n"
                    + "UNION \n"
                    + "select top 1 dt_alter data, hr_alter hora from tmktdet where codpessoa = ? order by dt_alter desc, hr_alter desc \n"
                    + "UNION \n"
                    + "select top 1 dt_alter data, hr_alter hora from tmktdetpst where codpessoa = ? order by dt_alter desc, hr_alter desc \n"
                    + "UNION\n"
                    + "select top 1 dt_alter data, hr_alter hora from rhponto where matr = ? order by dt_alter desc, hr_alter desc \n"
                    + "UNION\n"
                    + "select top 1 dt_alter data, hr_alter hora from rondas where matr = ? order by dt_alter desc, hr_alter desc \n"
                    + ") z\n"
                    + "order by data desc, hora desc; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.setString(codPessoa);
            consulta.setString(codPessoa);
            consulta.setString(matr);
            consulta.setString(matr);
            consulta.select();
            Login retorno = new Login();
            retorno.setHora(getDataAtual("HORA"));
            retorno.setData(getDataAtual("SQL"));
            while (consulta.Proximo()) {
                retorno.setHora(consulta.getString("hora"));
                retorno.setData(consulta.getString("data"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("LoginDao.ultimaMovimentacaoEW - " + e.getMessage() + "\r\n"
                    + "select top 1 convert(varchar, data, 112) data, hora from (\n"
                    + "select top 1 dt_alter data, hr_alter hora from pessoatrajeto where codpessoa = " + codPessoa + " order by dt_alter desc, hr_alter desc \n"
                    + "UNION \n"
                    + "select top 1 dt_alter data, hr_alter hora from tmktdet where codpessoa = " + codPessoa + " order by dt_alter desc, hr_alter desc \n"
                    + "UNION \n"
                    + "select top 1 dt_alter data, hr_alter hora from tmktdetpst where codpessoa = " + codPessoa + " order by dt_alter desc, hr_alter desc \n"
                    + "UNION\n"
                    + "select top 1 dt_alter data, hr_alter hora from rhponto where matr = " + matr + " order by dt_alter desc, hr_alter desc \n"
                    + "UNION\n"
                    + "select top 1 dt_alter data, hr_alter hora from rondas where matr = " + matr + " order by dt_alter desc, hr_alter desc \n"
                    + ") z\n"
                    + "order by data desc, hora desc;");
        }
    }

    /**
     * Login inicial do mobile, trazendo as permissões de acesso
     *
     * @param sCodPessoa - Codigo pessoa (usuário)
     * @param persistencia - Classe de conexão ao cliente
     * @return - Retorna lista de permissões
     * @throws Exception - pode gerar exception
     */
    public static List<Login> LoginMobile(String sCodPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select top 1 saspwac.sistema Sistema, saspwac.inclusao Inclusao, saspwac.alteracao Alteracao, "
                    + " saspwac.exclusao Exclusao, pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, saspw.Situacao,"
                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, cargos.descricao cargo, EscalaMatr.Rota RotaM, "
                    + " EscalaCodPessoa.Rota RotaP"
                    + " from saspwac "
                    + " left join saspw on saspw.nome = saspwac.nome"
                    + "                and saspw.situacao <> 'B'"
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " left join funcion on funcion.matr=pessoa.matr"
                    + " left join cargos on cargos.cargo = funcion.codcargo"
                    + " left join Escala EscalaMatr on EscalaMatr.Data     = Cast(GetDate() as Date)"
                    + "        and EscalaMatr.MatrChe = Funcion.Matr "
                    + " left join Escala EscalaCodPessoa on EscalaCodPessoa.Data = Cast(GetDate() as Date)"
                    + "             and EscalaCodPessoa.CodPessoaSup = Pessoa.Codigo"
                    //+" where funcion.matr=? and sistema>300000 and sistema<400000";
                    + " where pessoa.codigo=? and sistema>300000 and sistema<400000";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sCodPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                saspw.setSituacao(consulta.getString("Situacao"));
                saspwac.setSistema(consulta.getString("Sistema"));
                saspwac.setInclusao(consulta.getInt("Inclusao"));
                saspwac.setAlteracao(consulta.getInt("Alteracao"));
                saspwac.setExclusao(consulta.getInt("Exclusao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setCargo(consulta.getString("cargo"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setRotaM(consulta.getString("rotam"));
                login.setRotaP(consulta.getString("rotap"));
                list_login.add(login);
            }
            if (list_login.isEmpty()) {
                sql = "select Codigo, Nome, PwWeb from pessoa"
                        + " where pessoa.codigo=? ";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(sCodPessoa);
                consulta.select();
                while (consulta.Proximo()) {
                    Pessoa pessoa = new Pessoa();
                    Funcion funcion = new Funcion();
                    Login login = new Login();
                    pessoa.setPWWeb(consulta.getString("PwWeb"));
                    pessoa.setCodigo(consulta.getString("codigo"));
                    pessoa.setNome(consulta.getString("Nome"));
                    funcion.setSituacao("A");
                    login.setPessoa(pessoa);
                    login.setFuncion(funcion);
                    login.setSaspw(null);
                    login.setSaspwac(null);
                    list_login.add(login);
                }
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public static List<Login> LoginRecontagem(String sCodPessoa, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select top 1 tesAutomatiza.*,pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,\n"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb,\n"
                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, cargos.descricao cargo\n"
                    + "from tesAutomatiza\n"
                    + "left join pessoa on pessoa.codigo = tesAutomatiza.codpessoa\n"
                    + "left join funcion on funcion.matr=pessoa.matr\n"
                    + "left join cargos on cargos.cargo = funcion.cargo\n"
                    + "where data = ? and codpessoa = ?\n"
                    + "order by sequencia desc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(sCodPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                TesAutomatiza tesAutomatiza = new TesAutomatiza();
                tesAutomatiza.setCamera(consulta.getInt("camera"));
                tesAutomatiza.setCodFil(consulta.getBigDecimal("codfil"));
                tesAutomatiza.setCodPessoa(consulta.getBigDecimal("codpessoa"));
                tesAutomatiza.setCodTes(consulta.getString("codtes"));
                tesAutomatiza.setData(consulta.getLocalDate("data"));
                tesAutomatiza.setDt_alter(consulta.getLocalDate("dt_alter"));
                tesAutomatiza.setHr_alter(consulta.getString("hr_alter"));
                tesAutomatiza.setMatr(consulta.getBigDecimal("matr"));
                tesAutomatiza.setOperador(consulta.getString("operador"));
                tesAutomatiza.setSequencia(consulta.getBigDecimal("sequencia"));
                tesAutomatiza.setTipoContadora(consulta.getInt("tipocontadora"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setCargo(consulta.getString("cargo"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                saspwac.setSistema("0");
                saspwac.setInclusao(0);
                saspwac.setAlteracao(0);
                saspwac.setExclusao(0);
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public static List<Login> LoginMobile(String sCodPessoa, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
//            String sql = "select saspwac.sistema Sistema, saspwac.inclusao Inclusao, saspwac.alteracao Alteracao, \n"
//                    + " saspwac.exclusao Exclusao, pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,\n"
//                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, saspw.Situacao,\n"
//                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, cargos.descricao cargo, EscalaMatr.SeqRota RotaM, \n"
//                    + " EscalaCodPessoa.Rota RotaP, \n"
//                    + " ISNULL((SELECT TOP 1 MoedaPdrMobile FROM Paramet WHERE Paramet.Filial_Pdr = Funcion.CodFil), 'BRL') MoedaPdrMobile \n"
//                    + " from saspwac \n"
//                    + " left join saspw on saspw.nome = saspwac.nome\n"
//                    + "                and saspw.situacao <> 'B'\n"
//                    + " left join pessoa on pessoa.codigo = saspw.codpessoa\n"
//                    + " left join funcion on funcion.matr=pessoa.matr\n"
//                    + " left join cargos on cargos.cargo = funcion.cargo\n"
//                    + " left join Escala EscalaMatr on (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr) \n"
//                    + "Inner JOIN Rotas ON Rotas.Sequencia = EscalaMatr.SeqRota\n"
//                    + "                 and Rotas.DtFim >= ? \n"
//                    + " left join Escala EscalaCodPessoa on EscalaCodPessoa.Data = ?\n"
//                    + "             and EscalaCodPessoa.CodPessoaSup = Pessoa.Codigo\n"
//                    //+" where funcion.matr=? and sistema>300000 and sistema<400000";
//                    + " where pessoa.codigo=? and sistema>300000 and sistema<400000";
            String sql = "select saspwac.sistema Sistema, saspwac.inclusao Inclusao, saspwac.alteracao Alteracao, \n"
                    + "    saspwac.exclusao Exclusao, CONVERT(BigInt, pessoa.matr) Matr, SubString(pessoa.nome,1,20) Nome_Guer,\n"
                    + "    pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, saspw.Situacao,\n"
                    + "    funcion.CodFil, funcion.Situacao, Funcion.Matr, cargos.descricao cargo, EscalaMatr.SeqRota RotaM, \n"
                    + "    EscalaCodPessoa.Rota RotaP, \n"
                    + "    ISNULL((SELECT TOP 1 MoedaPdrMobile FROM Paramet WHERE Paramet.Filial_Pdr = Funcion.CodFil), 'BRL') MoedaPdrMobile \n"
                    + "from pessoa\n"
                    + "left join saspw on saspw.codpessoa = pessoa.codigo\n"
                    + "left join saspwac on saspwac.nome = saspw.nome\n"
                    + "                and saspw.situacao <> 'B'\n"
/*                    
                    + "                and sistema > 10000 \n"
                    + "                and sistema < 400000\n"
*/                    
                    + "                and sistema > 300000 \n"
                    + "                and sistema < 330000\n"
                    + "left join funcion on funcion.matr=pessoa.matr\n"
                    + "left join cargos on cargos.cargo = funcion.cargo\n"
                    + "left join Escala EscalaMatr on (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr) \n"
                    + "                            AND EscalaMatr.SeqRota IN (SELECT Sequencia FROM Rotas WHERE Rotas.DtFim >= ?) \n"
                    + "left join Escala EscalaCodPessoa on EscalaCodPessoa.Data = ? \n"
                    + "            and EscalaCodPessoa.CodPessoaSup = Pessoa.Codigo\n"
                    + "where pessoa.codigo= ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(sCodPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                saspw.setSituacao(consulta.getString("Situacao"));
                saspwac.setSistema(consulta.getString("Sistema"));
                saspwac.setInclusao(consulta.getInt("Inclusao"));
                saspwac.setAlteracao(consulta.getInt("Alteracao"));
                saspwac.setExclusao(consulta.getInt("Exclusao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setCargo(consulta.getString("cargo"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setRotaM(consulta.getString("rotam"));
                login.setRotaP(consulta.getString("rotap"));
                login.setMoedaPdrMobile(consulta.getString("MoedaPdrMobile"));
                list_login.add(login);
            }

            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public static List<Login> LoginMobileSemDtFim(String sCodPessoa, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select saspwac.sistema Sistema, saspwac.inclusao Inclusao, saspwac.alteracao Alteracao, \n"
                    + " saspwac.exclusao Exclusao, pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,\n"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, saspw.Situacao,\n"
                    + " funcion.CodFil, funcion.Situacao, Funcion.Matr, cargos.descricao cargo, EscalaMatr.Rota RotaM, \n"
                    + " EscalaCodPessoa.Rota RotaP\n"
                    + " from saspwac \n"
                    + " left join saspw on saspw.nome = saspwac.nome\n"
                    + "                and saspw.situacao <> 'B'\n"
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa\n"
                    + " left join funcion on funcion.matr=pessoa.matr\n"
                    + " left join cargos on cargos.cargo = funcion.cargo\n"
                    + " left join Escala EscalaMatr on (EscalaMatr.MatrChe = Funcion.Matr OR EscalaMatr.MatrMot = Funcion.Matr) \n"
                    //                    + "Inner JOIN Rotas ON Rotas.Sequencia = EscalaMatr.SeqRota\n"
                    + "                 and EscalaMatr.Data = ? \n"
                    + " left join Escala EscalaCodPessoa on EscalaCodPessoa.Data = ?\n"
                    + "             and EscalaCodPessoa.CodPessoaSup = Pessoa.Codigo\n"
                    //+" where funcion.matr=? and sistema>300000 and sistema<400000";
                    + " where pessoa.codigo=? and sistema>300000 and sistema<400000";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(sCodPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                saspw.setSituacao(consulta.getString("Situacao"));
                saspwac.setSistema(consulta.getString("Sistema"));
                saspwac.setInclusao(consulta.getInt("Inclusao"));
                saspwac.setAlteracao(consulta.getInt("Alteracao"));
                saspwac.setExclusao(consulta.getInt("Exclusao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                funcion.setCargo(consulta.getString("cargo"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                login.setRotaM(consulta.getString("rotam"));
                login.setRotaP(consulta.getString("rotap"));
                list_login.add(login);
            }
            if (list_login.isEmpty()) {
                sql = "select Codigo, Nome, PwWeb from pessoa"
                        + " where pessoa.codigo=? ";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(sCodPessoa);
                consulta.select();
                while (consulta.Proximo()) {
                    Pessoa pessoa = new Pessoa();
                    Funcion funcion = new Funcion();
                    Login login = new Login();
                    pessoa.setPWWeb(consulta.getString("PwWeb"));
                    pessoa.setCodigo(consulta.getString("codigo"));
                    pessoa.setNome(consulta.getString("Nome"));
                    funcion.setSituacao("A");
                    login.setPessoa(pessoa);
                    login.setFuncion(funcion);
                    login.setSaspw(null);
                    login.setSaspwac(null);
                    list_login.add(login);
                }
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Login inicial do mobile, trazendo as permissões de acesso, para empresas
     * com vários bds
     *
     * @param cpf - CPF do usuário
     * @param persistencia - Classe de conexão ao cliente
     * @return - Retorna lista de permissões
     * @throws Exception - pode gerar exception
     */
    public static List<Login> LoginMobileParamCentralEmpresa(String cpf, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select saspwac.sistema Sistema, saspwac.inclusao Inclusao, saspwac.alteracao Alteracao, "
                    + " saspwac.exclusao Exclusao, pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb, saspw.Situacao,"
                    + " funcion.CodFil, funcion.Situacao"
                    + " from saspwac "
                    + " left join saspw on saspw.nome = saspwac.nome"
                    + "                and saspw.situacao <> 'B'"
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " left join funcion on funcion.matr=pessoa.matr"
                    //+" where funcion.matr=? and sistema>300000 and sistema<400000";
                    + " where pessoa.cpf=? and sistema>300000 and sistema<400000";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpf);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                saspw.setSituacao(consulta.getString("Situacao"));
                saspwac.setSistema(consulta.getString("Sistema"));
                saspwac.setInclusao(consulta.getInt("Inclusao"));
                saspwac.setAlteracao(consulta.getInt("Alteracao"));
                saspwac.setExclusao(consulta.getInt("Exclusao"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                list_login.add(login);
            }
            if (list_login.isEmpty()) {
                sql = "select Codigo, Nome, PwWeb from pessoa"
                        + " where pessoa.cpf=? ";
                consulta = new Consulta(sql, persistencia);
                consulta.setString(cpf);
                consulta.select();
                while (consulta.Proximo()) {
                    Pessoa pessoa = new Pessoa();
                    Funcion funcion = new Funcion();
                    Login login = new Login();
                    pessoa.setPWWeb(consulta.getString("PwWeb"));
                    pessoa.setCodigo(consulta.getString("codigo"));
                    pessoa.setNome(consulta.getString("Nome"));
                    funcion.setSituacao("A");
                    login.setPessoa(pessoa);
                    login.setFuncion(funcion);
                    login.setSaspw(null);
                    login.setSaspwac(null);
                    list_login.add(login);
                }
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Login inicial do mobile, trazendo as permissões de acesso
     *
     * @param sCodPessoa - Codigo pessoa (usuário)
     * @param persistencia - Classe de conexão ao cliente
     * @return - Retorna lista de permissões
     * @throws Exception - pode gerar exception
     */
    public static List<Login> LoginMobileParamCentralEmpresa2(String sCodPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select pessoa.matr Matr, SubString(pessoa.nome,1,20) Nome_Guer,"
                    + " pessoa.pw, pessoa.codigo, pessoa.pwweb PwWeb,"
                    + " funcion.CodFil, funcion.Situacao"
                    + " from pessoa"
                    + " left join funcion on funcion.matr=pessoa.matr"
                    + " where pessoa.codigo=? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sCodPessoa);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPW(consulta.getString("pw"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    /**
     * Verifica o login para acesso do Portal RH
     *
     * @param persistencia - classe de conexão persistida
     * @return - retorna lista com informações sobre o usuário
     * @throws Exception - pode gerar exception
     */
    public List<Login> LoginWebRh(String matr, Persistencia persistencia) throws Exception {
        try {
            List<Login> list_login = new ArrayList();
            String sql = "select pessoa.matr Matr, "
                    + "SubString(pessoa.nome,1,20) Nome_Guer, "
                    + "pessoa.pwportal, "
                    + "pessoa.codigo, "
                    + "pessoa.pwweb PwWeb, "
                    + "funcion.CodFil, "
                    + "funcion.Situacao "
                    + "from funcion "
                    + "left join pessoa on pessoa.matr = funcion.matr "
                    + "where funcion.matr=?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();
            while (consulta.Proximo()) {
                Saspwac saspwac = new Saspwac();
                Saspw saspw = new Saspw();
                Funcion funcion = new Funcion();
                Pessoa pessoa = new Pessoa();
                Login login = new Login();
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setNome_Guer(consulta.getString("Nome_Guer"));
                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setSituacao(consulta.getString("Situacao"));
                pessoa.setPWWeb(consulta.getString("PwWeb"));
                pessoa.setPWPortal(consulta.getString("pwportal"));
                pessoa.setCodigo(consulta.getString("codigo"));
                login.setFuncion(funcion);
                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
                login.setSaspw(saspw);
                list_login.add(login);
            }
            consulta.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public UsuarioSatMobWeb NovoPortalRH(String matricula, Persistencia persistencia) throws Exception {
        try {
            String sql = "select pessoa.matr Matr, pessoa.nome, pessoa.pwportal, pessoa.codigo, pessoa.dt_ultacportal, funcion.CodFil, "
                    + " pessoa.Sexo, pessoa.codpessoaweb, pessoa.cpf, pessoa.pwweb "
                    + " from funcion "
                    + " left join pessoa on pessoa.matr = funcion.matr "
                    + " where funcion.matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            Pessoa pessoa;
            Saspw saspw;
            UsuarioSatMobWeb usuario;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setMatr(consulta.getString("Matr"));
                pessoa.setCPF(consulta.getString("cpf"));
                pessoa.setNome(consulta.getString("nome"));
                pessoa.setPWPortal(consulta.getString("pwportal"));
                pessoa.setPWWeb(consulta.getString("pwweb"));
                pessoa.setDt_UltAcPortal(consulta.getString("dt_ultacportal"));
                pessoa.setSexo(consulta.getString("sexo"));
                pessoa.setCodigo(consulta.getString("codigo"));
                pessoa.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                saspw = new Saspw();
                saspw.setCodFil(consulta.getString("codfil"));
                usuario = new UsuarioSatMobWeb();
                usuario.setPessoa(pessoa);
                usuario.setSaspw(saspw);
                consulta.Close();
                return usuario;
            }
            return null;
        } catch (Exception e) {
            throw new Exception("Falha ao verificar dados de login - " + e.getMessage());
        }
    }

    public static List<Pessoa> LoginPortalRH(String sEmail, Persistencia persistencia) throws Exception {
        List<Pessoa> listPessoa;
        try {
            Pessoa pessoa;
            Consulta consult = new Consulta("Select Email, Codigo, PWPortal, SubString(pessoa.nome,1,20) "
                    + "Nome_Guer, Situacao from pessoa where email=?", persistencia);
            consult.setString(sEmail);
            consult.select();
            listPessoa = new ArrayList();
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getString("Codigo"));
                pessoa.setNome(consult.getString("Nome_Guer"));
                pessoa.setPWPortal(consult.getString("PWPortal"));
                pessoa.setSituacao(consult.getString("Situacao"));
                pessoa.setEmail(consult.getString("Email"));
                listPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            listPessoa = null;
            throw new Exception("Falha ao buscar pessoa - " + e.getMessage());
        }
        return listPessoa;
    }

    public static List<Pessoa> LoginCodigo(String sEmail, Persistencia persistencia) throws Exception {
        List<Pessoa> listPessoa;
        try {
            Pessoa pessoa;
            Consulta consult = new Consulta("Select Email, Codigo, PWWeb, SubString(pessoa.nome,1,20) "
                    + "Nome_Guer, Situacao from pessoa where email=?", persistencia);
            consult.setString(sEmail);
            consult.select();
            listPessoa = new ArrayList();
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getString("Codigo"));
                pessoa.setNome(consult.getString("Nome_Guer"));
                pessoa.setPWPortal(consult.getString("PWWeb"));
                pessoa.setSituacao(consult.getString("Situacao"));
                pessoa.setEmail(consult.getString("Email"));
                listPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            listPessoa = null;
            throw new Exception("Falha ao buscar pessoa - " + e.getMessage());
        }
        return listPessoa;
    }

    /**
     * Verifica o login para acesso a rota no mobile
     *
     * @param codigo - codigo do usuário
     * @param persistencia - classe de conexão persistida
     * @return - retorna lista com informações sobre o usuário
     * @throws Exception - pode gerar exception
     */
    public static List<String> LoginRotaPonto(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<String> list_login = new ArrayList<>();
            Consulta consult = new Consulta("Select Pessoa.PwWeb from Pessoa "
                    + " Where Pessoa.Codigo = ? ", persistencia);
            consult.setString(codigo);
            consult.select();
            while (consult.Proximo()) {
                list_login.add(consult.getString("PwWeb"));
            }
            consult.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao Logar - " + e.getMessage());
        }
    }

    /**
     * Verifica o login para acesso a rota no mobile
     *
     * @param codigo - codigo do usuário
     * @param persistencia - classe de conexão persistida
     * @return - retorna lista com informações sobre o usuário
     * @throws Exception - pode gerar exception
     */
    public static List<String> LoginRotaPontoPwPortal(String codigo, Persistencia persistencia) throws Exception {
        try {
            List<String> list_login = new ArrayList<>();
            Consulta consult = new Consulta("Select Pessoa.pwportal from Pessoa "
                    + " Where Pessoa.Codigo = ? ", persistencia);
            consult.setString(codigo);
            consult.select();
            while (consult.Proximo()) {
                list_login.add(consult.getString("pwportal"));
            }
            consult.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao Logar - " + e.getMessage());
        }
    }

    /**
     * Verifica o login para acesso a rota no mobile
     *
     * @param CodPessoa - Matricula do usuário
     * @param Senha - Senha do usuário PWWeb
     * @param persistencia - classe de conexão persistida
     * @return - retorna lista com informações sobre o usuário
     * @throws Exception - pode gerar exception
     */
    public static List<LoginRota> LoginRota(String CodPessoa, String Senha, Persistencia persistencia) throws Exception {
        try {
            List<LoginRota> list_login = new ArrayList<LoginRota>();
            Consulta consult = new Consulta("Select Funcion.Nome_Guer, Pessoa.PwWeb from Pessoa "
                                          + " Left Join Funcion on Funcion.Matr=Pessoa.Matr "
                                         //+" Where Funcion.Matr = ?",persistencia);
                                          + " Where Pessoa.Codigo = ? ", persistencia);
            consult.setString(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                Pessoa pessoa = new Pessoa();
                Funcion funcion = new Funcion();
                LoginRota loginrota = new LoginRota();
                pessoa.setPWWeb(consult.getString("PwWeb"));
                funcion.setNome_Guer(consult.getString("Nome_Guer"));
                loginrota.setFuncion(funcion);
                loginrota.setPessoa(pessoa);
                list_login.add(loginrota);
            }
            consult.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao Logar - " + e.getMessage());
        }
    }

    /**
     * Verifica o login para acesso a rota no mobile
     *
     * @param CodPessoa - Matricula do usuário
     * @param Senha - Senha do usuário PWWeb
     * @param persistencia - classe de conexão persistida
     * @return - retorna lista com informações sobre o usuário
     * @throws Exception - pode gerar exception
     */
    public static List<LoginRota> LoginRota(String CodPessoa, String nome, String Senha, Persistencia persistencia) throws Exception {
        try {
            List<LoginRota> list_login = new ArrayList<LoginRota>();
            Consulta consult = new Consulta("Select Funcion.Nome_Guer, Pessoa.PwWeb from Pessoa "
                    + " Left Join Funcion on Funcion.Matr=Pessoa.Matr "
                    + " Left Join SasPW on SASPW.codpessoa = pessoa.codigo "
                    + " where pessoa.Matr = ? or SASPW.nome = ? ", persistencia);
            consult.setString(CodPessoa);
            consult.setString(nome);
            consult.select();
            while (consult.Proximo()) {
                Pessoa pessoa = new Pessoa();
                Funcion funcion = new Funcion();
                LoginRota loginrota = new LoginRota();
                pessoa.setPWWeb(consult.getString("PwWeb"));
                funcion.setNome_Guer(consult.getString("Nome_Guer"));
                loginrota.setFuncion(funcion);
                loginrota.setPessoa(pessoa);
                list_login.add(loginrota);
            }
            consult.Close();
            return list_login;
        } catch (Exception e) {
            throw new Exception("Falha ao Logar - " + e.getMessage());
        }
    }

    /**
     * Busca a senha e o número da permissao para a rota
     *
     * @param codpessoa Código da pessoa
     * @param persistencia Conexão com o banco de dados
     * @return Retorna a senha mobile o código da permissão para rotas
     * @throws Exception
     */
    public Login PermissaoPessoaRota(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        Login login = new Login();
        try {

            String sql = "Select Pessoa.PWWeb, SASPwAc.Sistema \n"
                    + "from Pessoa \n"
                    + "left join SASPw   on SASPW.CodPessoa = Pessoa.Codigo \n"
                    + "left join SASPwAc on SASPwAC.Nome    = SASPw.Nome    \n"
                    + "and SASPwAc.Sistema = '300001' \n"
                    + "where Pessoa.Codigo = ? ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codpessoa);
            consult.select();

            while (consult.Proximo()) {
                Pessoa pessoa = new Pessoa();
                Saspwac saspwac = new Saspwac();

                pessoa.setCodigo(codpessoa);
                pessoa.setPWWeb(consult.getString("PWWeb"));
                saspwac.setSistema(consult.getString("Sistema"));

                login.setPessoa(pessoa);
                login.setSaspwac(saspwac);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar permissao - " + e.getMessage());
        }

        return login;
    }
}

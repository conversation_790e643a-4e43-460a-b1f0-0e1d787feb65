/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.PessoaCofre;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PessoaCofreDao {

    public Boolean deletaPessoaCofre(PessoaCofre pessoaCofre, Persistencia persistencia) throws Exception {
        Boolean resultado = true;
        String sql = "delete from pessoacofre where codpessoa = ? and codcofre = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(pessoaCofre.getCodPessoa());
            consulta.setBigDecimal(pessoaCofre.getCodCofre());
            resultado = consulta.delete() > 0;
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao desassociar pessoa à empresas autorizadas - " + e.getMessage());
        }
        return resultado;
    }

    public void deletaPessoaCofre(String sCod, Persistencia persistencia) throws Exception {
        //Boolean resultado = true;
        String sql = "delete from pessoacofre where codpessoa = ? ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(new BigDecimal(sCod));
            //resultado=consult.execute();
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao remover clientes dessa pessoa - " + e.getMessage());
        }
        //return resultado;
    }

    public void gravaPessoaCofres(PessoaCofre pessoaCofre, Persistencia persistencia) throws Exception {
        String sql = "insert into pessoacofre(codpessoa, codcofre) values(?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoaCofre.getCodPessoa());
            consulta.setBigDecimal(pessoaCofre.getCodCofre());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao salvar dados do cofre na base local - " + e.getMessage());
        }
    }

    public List<PessoaCofre> getCodPessoaCofres(BigDecimal CodPessoa, BigDecimal CodCofre, Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select codpessoa from pessoacofre where codpessoa = ? and codcofre = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.setBigDecimal(CodCofre);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodPessoa(consult.getString("codpessoa"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar permissão cofre da base local - " + e.getMessage());
        }
    }

    public static List<PessoaCofre> getPermissaoCofres(String sCodPessoa, Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select codpessoa from pessoacofre where codpessoa = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodPessoa);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodPessoa(consult.getString("codpessoa"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar permissão cofre da base local - " + e.getMessage());
        }
    }

    public List<PessoaCofre> getAllClientesCofres(Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select pessoacofre.codcofre, clientes.nred from pessoacofre "
                + "left join clientes on pessoacofre.codcofre = clientes.codcofre ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodCofre(consult.getString("codcofre").replace(".0", ""));
                pl.setNred(consult.getString("nred"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados cliente da base local - " + e.getMessage());
        }
    }

    public List<PessoaCofre> getAllPessoaCofres(Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select pessoacofre.codpessoa, pessoa.nome, pessoacofre.codcofre, clientes.nred from pessoacofre "
                + "left join pessoa on pessoacofre.codpessoa = pessoa.codpessoaweb "
                + "left join clientes on pessoacofre.codcofre = clientes.codcofre ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodPessoa(consult.getString("codpessoa").replace(".0", ""));
                pl.setCodCofre(consult.getString("codcofre").replace(".0", ""));
                pl.setNred(consult.getString("nred"));
                pl.setNome(consult.getString("nome"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados cofre da base local - " + e.getMessage());
        }
    }

    public List<PessoaCofre> getAllPessoaCofres(String sCod, Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select pessoacofre.codpessoa, pessoacofre.codcofre, clientes.nred from pessoacofre "
                + "left join pessoa on pessoacofre.codpessoa = pessoa.codpessoaweb "
                + "left join clientes on pessoacofre.codcofre = clientes.codcofre "
                + "where pessoacofre.codpessoa = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCod);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodPessoa(consult.getString("codpessoa").replace(".0", ""));
                pl.setCodCofre(consult.getString("codcofre").replaceAll(".0", ""));
                pl.setNred(consult.getString("nred"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados cofre da base local - " + e.getMessage());
        }
    }

    public List<PessoaCofre> getComboPessoaCofres(Persistencia persistencia) throws Exception {
        List<PessoaCofre> listp = new ArrayList();
        String sql = "select distinct pessoacofre.codpessoa, pessoa.rg, pessoa.nome from pessoacofre "
                + "left join pessoa on pessoacofre.codpessoa = pessoa.codpessoaweb";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                PessoaCofre pl = new PessoaCofre();
                pl.setCodPessoa(consult.getString("codpessoa").replace(".0", ""));
                pl.setNome(consult.getString("nome"));
                pl.setRg(consult.getString("rg"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar dados cofre da base local - " + e.getMessage());
        }
    }
}

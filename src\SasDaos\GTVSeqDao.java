package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.GTVSeq;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GTVSeqDao {

    private Persistencia persistencia;

    public GTVSeqDao() {
    }

    public GTVSeqDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public void updateMaxGuia(String codFil, String serie, String guia, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE GTVSeq SET Sequencia = ? "
                    + " where serie = ? "
                    + "  and codFil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.setString(codFil);
            consult.update();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("GTVSeqDao.updateMaxGuia - " + e.getMessage() + "\r\n"
                    + "UPDATE GTVSeq SET Sequencia = " + guia
                    + " where serie = " + serie
                    + "  and codFil = " + codFil);
        }
    }

    public void inicializaSerie(String codFil, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO GTVSeq (Sequencia, Serie, CodFil, Codigo) VALUES (?, ?, ?, (SELECT ISNULL(MAX(Codigo),0) + 1 Codigo FROM GTVSeq)) ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("1");
            consult.setString(serie);
            consult.setString(codFil);
            consult.update();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("GTVSeqDao.inicializaSerie - " + e.getMessage() + "\r\n"
                    + "INSERT INTO GTVSeq (Sequencia, Serie, CodFil, Codigo) VALUES (1, " + serie + ", " + codFil
                    + ", (SELECT ISNULL(MAX(Codigo),0) + 1 Codigo FROM GTVSeq)) ");
        }
    }

    /**
     * Busca a próxima sequência para uma série/filial em GTVSeq
     *
     * @param codFil
     * @param serie
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String maxGuia(String codFil, String serie, Persistencia persistencia) throws Exception {
        try {
            String retorno = "1";
            String sql = " select ISNULL(MAX(sequencia),0) + 1 sequencia "
                    + " from GTVSeq "
                    + " where serie = ? "
                    + "  and codFil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(serie);
            consult.setString(codFil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("sequencia");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("GTVSeqDao.maxGuia - " + e.getMessage() + "\r\n"
                    + "select ISNULL(MAX(sequencia),0) + 1 sequencia "
                    + " from GTVSeq "
                    + " where serie = " + serie
                    + "  and codFil = " + codFil);
        }
    }

    /**
     * Valida séries guias
     *
     * @param persistencia - Conexão ao banco
     * @param serie - Número da sequência
     * @return - Retorna boolean
     * @throws java.lang.Exception - pode gerar exception
     */
    public boolean validaSerieGuia(Persistencia persistencia, String serie) throws Exception {
        boolean retornar = false;

        String sql = "select top 1 Codigo from GTVSeq "
                + "where Serie=?";
        try {
            Consulta validaSG = new Consulta(sql, persistencia);
            validaSG.setString(serie);
            validaSG.select();
            while (validaSG.Proximo()) {
                retornar = true;
            }
            validaSG.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao validar série de guia - " + e.getMessage());
        }
        return retornar;
    }

    /**
     * Busca todas as série válidas para a filial
     *
     * @param CodFil - Codigo da Filial
     * @param persistencia - Conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<GTVSeq> seriesValidas(String CodFil, Persistencia persistencia) throws Exception {
        String sql = "select Serie from gtvseq where codfil = ? ";
        List<GTVSeq> retorno = new ArrayList();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.select();
            while (consult.Proximo()) {
                GTVSeq serie = new GTVSeq();
                serie.setSerie(consult.getString("Serie"));
                retorno.add(serie);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar series válidas" + e.getMessage());
        }
    }

    /**
     * Busca todas GTVSeqs válidas para a filial
     *
     * @param CodFil - Codigo da Filial
     * @return Lista de GTVSeq
     * @throws Exception
     */
    public List<GTVSeq> getAllByCodFil(String CodFil) throws Exception {
        String sql = "SELECT Codigo,\n"
                + "       CodFil,\n"
                + "       Descricao,\n"
                + "       Sequencia,\n"
                + "       Serie,\n"
                + "       CodMaterial,\n"
                + "       Operador,\n"
                + "       Dt_Alter,\n"
                + "       Hr_Alter\n"
                + "FROM GTVSeq\n"
                + "WHERE CodFil = ? ";

        List<GTVSeq> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.select();
            while (consulta.Proximo()) {
                GTVSeq row = new GTVSeq();
                row.setCodigo(consulta.getString("Codigo"));
                row.setCodFil(consulta.getString("CodFil"));
                row.setDescricao(consulta.getString("Descricao"));
                row.setSequencia(consulta.getString("Sequencia"));
                row.setSerie(consulta.getString("Serie"));
                row.setCodMaterial(consulta.getInt("CodMaterial"));
                row.setOperador(consulta.getString("Operador"));
                row.setDt_Alter(consulta.getString("Dt_Alter"));
                row.setHr_Alter(consulta.getString("Hr_Alter"));

                lista.add(row);
            }
            return lista;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }
    
    public BigDecimal maxGuia53(String codFil, String operador, Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("0");
            String sql = "select max(sequencia) + 1 sequencia "
                    + " from GTVSeq "
                    + " where serie = '53' "
                    + "  and codFil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getBigDecimal("sequencia");
            }
            consult.Close();
            sql = "update GTVSeq SET Sequencia = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?"
                    + " WHERE CodFil = ? AND Serie = '53_'";
            PreparedStatement pst = persistencia.getState(sql);
            pst.setBigDecimal(1, retorno);
            pst.setString(2, operador);
            pst.setString(3, DataAtual.getDataAtual("SQL"));
            pst.setString(4, DataAtual.getDataAtual("HORA"));
            pst.setString(5, codFil);
            pst.execute();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar numero de guia");
        }
    }
}

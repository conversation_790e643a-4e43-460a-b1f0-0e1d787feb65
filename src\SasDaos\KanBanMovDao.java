/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.KanBan;
import SasBeans.KanBanMov;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class KanBanMovDao {

    /**
     * Lista toda a movimentação de um ticket específico
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<KanBanMov> listaMovimentacao(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        List<KanBanMov> movimentacoes = new ArrayList<>();
        try {
            String sql = "SELECT KanBanMov.*, pessoa.nome nomePessoa FROM KanBanMov "
                    + " left join pessoa on pessoa.codigo = KanBanMov.codpessoa "
                    + " WHERE sequencia = ? "
                    + " order by KanBanMov.data desc, KanBanMov.ordem desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(sequencia);
            consult.select();
            KanBanMov movimentacao;
            while (consult.Proximo()) {
                movimentacao = new KanBanMov();
                movimentacao.setSequencia(consult.getBigDecimal("sequencia"));
                movimentacao.setData(consult.getLocalDate("data"));
                movimentacao.setOrdem(consult.getBigDecimal("ordem"));
                movimentacao.setCodPessoa(consult.getBigDecimal("codpessoa"));
                movimentacao.setFase(consult.getString("fase"));
                movimentacao.setNomePessoa(consult.getString("nomePessoa"));
                movimentacao.setHr_Alter(consult.getString("hr_alter"));
                movimentacao.setDt_Alter(consult.getLocalDate("dt_alter"));
                movimentacao.setOperador(consult.getString("Operador"));
                movimentacoes.add(movimentacao);
            }
            consult.Close();
            return movimentacoes;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de movimentações de um ticket - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca o próximo número de sequência
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getMaxOrdem(KanBan kanban, Persistencia persistencia) throws Exception {
        try {
            String retorno = new String(), sql = "Select isnull(MAX(ordem),0)+1 ordem "
                    + " FROM kanbanmov "
                    + " WHERE sequencia = ? and data = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(kanban.getSequencia());
            consult.setString(DataAtual.getDataAtual("SQL"));
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("ordem");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar próxima ordem de movimentação - \r\n" + e.getMessage());
        }
    }

    /**
     * Insere uma movimentação
     *
     * @param kanbanmov
     * @param persistencia
     * @throws Exception
     */
    public void inserirMovimentacao(KanBanMov kanbanmov, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO kanbanmov (sequencia, data, ordem, fase, codpessoa, hr_alter, dt_alter, operador) "
                    + "VALUES(?,?,?,?,?,?,?,?)";
            PreparedStatement Mstat = persistencia.getState(sql);
            Mstat.setBigDecimal(1, kanbanmov.getSequencia());
            Mstat.setDate(2, DataAtual.LC2Date(kanbanmov.getData()));
            Mstat.setBigDecimal(3, kanbanmov.getOrdem());
            Mstat.setString(4, kanbanmov.getFase());
            Mstat.setBigDecimal(5, kanbanmov.getCodPessoa());
            Mstat.setString(6, kanbanmov.getHr_Alter());
            Mstat.setDate(7, DataAtual.LC2Date(kanbanmov.getDt_Alter()));
            Mstat.setString(8, kanbanmov.getOperador());
            Mstat.execute();
            Mstat.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir movimentação - \r\n" + e.getMessage());
        }
    }
}

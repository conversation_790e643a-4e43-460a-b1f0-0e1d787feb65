/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;

/**
 *
 * <AUTHOR>
 */
public class OperacoesServDao {

    /**
     *
     * @param dtCompet
     * @param matr
     * @param persistencia
     * @throws Exception
     */
    public void atualizaQtdePresenca(String dtCompet, String matr, Persistencia persistencia) throws Exception {
        try {
            String sql = " Update OperacoesServ set QtdePresenca = ISNULL(QtdePresenca,0)+1 "
                    + " from OperacoesServ "
                    + " Left join RH_Horas  on RH_Horas.Secao = OperacoesServ.Secao "
                    + "                   and RH_Horas.CodFil = OperacoesServ.CodFil "
                    + "                     and RH_Horas.Data = OperacoesServ.Data "
                    + " where OperacoesServ.Data = ? "
                    + "        and RH_Horas.Matr = ? "
                    + " and OperacoesServ.QtdeEscala > 0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtCompet);
            consulta.setString(matr);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("OperacoesServDao.atualizaQtdePresenca - " + e.getMessage() + "\r\n"
                    + " Update OperacoesServ set QtdePresenca = ISNULL(QtdePresenca,0)+1"
                    + " from OperacoesServ "
                    + " Left join RH_Horas  on RH_Horas.Secao = OperacoesServ.Secao "
                    + "                   and RH_Horas.CodFil = OperacoesServ.CodFil "
                    + "                     and RH_Horas.Data = OperacoesServ.Data "
                    + " where OperacoesServ.Data = " + dtCompet
                    + "        and RH_Horas.Matr = " + matr
                    + " and OperacoesServ.QtdeEscala > 0");
        }
    }
}
